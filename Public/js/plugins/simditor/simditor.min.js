!function(a,b){"function"==typeof define&&define.amd?define("simditor",["jquery","simple-module","simple-hotkeys","simple-uploader"],function(c,d,e,f){return a.returnExportsGlobal=b(c,d,e,f)}):"object"==typeof exports?module.exports=b(require("jquery"),require("simple-module"),require("simple-hotkeys"),require("simple-uploader")):a.Simditor=b(jQuery,SimpleModule,simple.hotkeys,simple.uploader)}(this,function(a,b,c,d){var e,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};e=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="Selection",c.prototype._init=function(){return this.editor=this._module,this.sel=document.getSelection()},c.prototype.clear=function(){var a;try{return this.sel.removeAllRanges()}catch(b){a=b}},c.prototype.getRange=function(){return this.editor.inputManager.focused&&this.sel.rangeCount?this.sel.getRangeAt(0):null},c.prototype.selectRange=function(a){return this.clear(),this.sel.addRange(a),this.editor.inputManager.focused||!this.editor.util.browser.firefox&&!this.editor.util.browser.msie||this.editor.body.focus(),a},c.prototype.rangeAtEndOf=function(b,c){var d,e,f;return null==c&&(c=this.getRange()),null!=c&&c.collapsed?(b=a(b)[0],d=c.endContainer,e=this.editor.util.getNodeLength(d),c.endOffset===e-1&&a(d).contents().last().is("br")||c.endOffset===e?b===d?!0:a.contains(b,d)?(f=!0,a(d).parentsUntil(b).addBack().each(function(){return function(b,c){var d,e;return e=a(c).parent().contents().filter(function(){return!(this!==c&&3===this.nodeType&&!this.nodeValue)}),d=e.last(),d.get(0)===c||d.is("br")&&d.prev().get(0)===c?void 0:(f=!1,!1)}}(this)),f):!1:!1):void 0},c.prototype.rangeAtStartOf=function(b,c){var d,e;return null==c&&(c=this.getRange()),null!=c&&c.collapsed?(b=a(b)[0],e=c.startContainer,0!==c.startOffset?!1:b===e?!0:a.contains(b,e)?(d=!0,a(e).parentsUntil(b).addBack().each(function(){return function(b,c){var e;return e=a(c).parent().contents().filter(function(){return!(this!==c&&3===this.nodeType&&!this.nodeValue)}),e.first().get(0)!==c?d=!1:void 0}}(this)),d):!1):void 0},c.prototype.insertNode=function(b,c){return null==c&&(c=this.getRange()),null!=c?(b=a(b)[0],c.insertNode(b),this.setRangeAfter(b,c)):void 0},c.prototype.setRangeAfter=function(b,c){return null==c&&(c=this.getRange()),null!=c?(b=a(b)[0],c.setEndAfter(b),c.collapse(!1),this.selectRange(c)):void 0},c.prototype.setRangeBefore=function(b,c){return null==c&&(c=this.getRange()),null!=c?(b=a(b)[0],c.setEndBefore(b),c.collapse(!1),this.selectRange(c)):void 0},c.prototype.setRangeAtStartOf=function(b,c){return null==c&&(c=this.getRange()),b=a(b).get(0),c.setEnd(b,0),c.collapse(!1),this.selectRange(c)},c.prototype.setRangeAtEndOf=function(b,c){var d,e,f,g,h,i;return null==c&&(c=this.getRange()),e=a(b),b=e.get(0),e.is("pre")?(f=e.contents(),f.length>0?(g=f.last(),h=g.text(),"\n"===h.charAt(h.length-1)?c.setEnd(g[0],this.editor.util.getNodeLength(g[0])-1):c.setEnd(g[0],this.editor.util.getNodeLength(g[0]))):c.setEnd(b,0)):(i=this.editor.util.getNodeLength(b),3!==b.nodeType&&i>0&&(d=a(b).contents().last(),d.is("br")?i-=1:3!==d[0].nodeType&&this.editor.util.isEmptyNode(d)&&(d.append(this.editor.util.phBr),b=d[0],i=0)),c.setEnd(b,i)),c.collapse(!1),this.selectRange(c)},c.prototype.deleteRangeContents=function(a){var b,c;return null==a&&(a=this.getRange()),c=a.cloneRange(),b=a.cloneRange(),c.collapse(!0),b.collapse(!1),!a.collapsed&&this.rangeAtStartOf(this.editor.body,c)&&this.rangeAtEndOf(this.editor.body,b)?(this.editor.body.empty(),a.setStart(this.editor.body[0],0),a.collapse(!0),this.selectRange(a)):a.deleteContents(),a},c.prototype.breakBlockEl=function(b,c){var d;return null==c&&(c=this.getRange()),d=a(b),c.collapsed?(c.setStartBefore(d.get(0)),c.collapsed?d:d.before(c.extractContents())):d},c.prototype.save=function(b){var c,d,e;return null==b&&(b=this.getRange()),this._selectionSaved?void 0:(d=b.cloneRange(),d.collapse(!1),e=a("<span/>").addClass("simditor-caret-start"),c=a("<span/>").addClass("simditor-caret-end"),d.insertNode(c[0]),b.insertNode(e[0]),this.clear(),this._selectionSaved=!0)},c.prototype.restore=function(){var a,b,c,d,e,f,g;return this._selectionSaved?(e=this.editor.body.find(".simditor-caret-start"),a=this.editor.body.find(".simditor-caret-end"),e.length&&a.length?(f=e.parent(),g=f.contents().index(e),b=a.parent(),c=b.contents().index(a),f[0]===b[0]&&(c-=1),d=document.createRange(),d.setStart(f.get(0),g),d.setEnd(b.get(0),c),e.remove(),a.remove(),this.selectRange(d)):(e.remove(),a.remove()),this._selectionSaved=!1,d):!1},c}(b);var h,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},i=[].indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(b in this&&this[b]===a)return b;return-1};h=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="Formatter",c.prototype._init=function(){return this.editor=this._module,this._allowedTags=["br","a","img","b","strong","i","u","font","p","ul","ol","li","blockquote","pre","h1","h2","h3","h4","hr"],this._allowedAttributes={img:["src","alt","width","height","data-image-src","data-image-size","data-image-name","data-non-image"],a:["href","target"],font:["color"],pre:["data-lang","class"],p:["data-indent"],h1:["data-indent"],h2:["data-indent"],h3:["data-indent"],h4:["data-indent"]},this.editor.body.on("click","a",function(){return function(){return!1}}(this))},c.prototype.decorate=function(a){return null==a&&(a=this.editor.body),this.editor.trigger("decorate",[a])},c.prototype.undecorate=function(b){return null==b&&(b=this.editor.body.clone()),this.editor.trigger("undecorate",[b]),a.trim(b.html())},c.prototype.autolink=function(b){var c,d,e,f,g,h,i,j,k,l,m;for(null==b&&(b=this.editor.body),f=[],d=function(c){return c.contents().each(function(c,e){var g,h;return g=a(e),g.is("a")||g.closest("a, pre",b).length?void 0:g.contents().length?d(g):(h=g.text())&&/https?:\/\/|www\./gi.test(h)?f.push(g):void 0})},d(b),h=/(https?:\/\/|www\.)[\w\-\.\?&=\/#%:,@\!\+]+/gi,l=0,m=f.length;m>l;l++){for(c=f[l],j=c.text(),i=[],g=null,e=0;null!==(g=h.exec(j));)i.push(document.createTextNode(j.substring(e,g.index))),e=h.lastIndex,k=/^(http(s)?:\/\/|\/)/.test(g[0])?g[0]:"http://"+g[0],i.push(a('<a href="'+k+'" rel="nofollow"></a>').text(g[0])[0]);i.push(document.createTextNode(j.substring(e))),c.replaceWith(a(i))}return b},c.prototype.format=function(b){var c,d,e,f,g,h,i,j,k,l;if(null==b&&(b=this.editor.body),b.is(":empty"))return b.append("<p>"+this.editor.util.phBr+"</p>"),b;for(k=b.contents(),g=0,i=k.length;i>g;g++)e=k[g],this.cleanNode(e,!0);for(l=b.contents(),h=0,j=l.length;j>h;h++)f=l[h],c=a(f),c.is("br")?("undefined"!=typeof d&&null!==d&&(d=null),c.remove()):this.editor.util.isBlockNode(f)?c.is("li")?d&&d.is("ul, ol")?d.append(f):(d=a("<ul/>").insertBefore(f),d.append(f)):d=null:((!d||d.is("ul, ol"))&&(d=a("<p/>").insertBefore(f)),d.append(f));return b},c.prototype.cleanNode=function(b,c){var d,e,f,g,h,j,k,l,m,n,o,p,q,r,s,t,u;if(e=a(b),e.length>0){if(3===e[0].nodeType)return n=e.text().replace(/(\r\n|\n|\r)/gm,""),void(n?(o=document.createTextNode(n),e.replaceWith(o)):e.remove());if(k=e.contents(),l=e.is('[class^="simditor-"]'),e.is(this._allowedTags.join(","))||l){if(e.is("a")&&(d=e.find("img")).length>0&&(e.replaceWith(d),e=d,k=null),e.is("img")&&e.hasClass("uploading")&&e.remove(),!l)for(h=this._allowedAttributes[e[0].tagName.toLowerCase()],t=a.makeArray(e[0].attributes),p=0,r=t.length;r>p;p++)j=t[p],null!=h&&(u=j.name,i.call(h,u)>=0)||e.removeAttr(j.name)}else 1!==e[0].nodeType||e.is(":empty")?(e.remove(),k=null):e.is("div, article, dl, header, footer, tr")?(e.append("<br/>"),k.first().unwrap()):e.is("table")?(f=a("<p/>"),e.find("tr").each(function(){return function(b,c){return f.append(a(c).text()+"<br/>")}}(this)),e.replaceWith(f),k=null):e.is("thead, tfoot")?(e.remove(),k=null):e.is("th")?(g=a("<td/>").append(e.contents()),e.replaceWith(g)):k.first().unwrap();if(c&&null!=k&&!e.is("pre"))for(q=0,s=k.length;s>q;q++)m=k[q],this.cleanNode(m,!0);return null}},c.prototype.clearHtml=function(b,c){var d,e,f;return null==c&&(c=!0),d=a("<div/>").append(b),e=d.contents(),f="",e.each(function(b){return function(d,g){var h,i;return 3===g.nodeType?f+=g.nodeValue:1===g.nodeType&&(h=a(g),i=h.contents(),i.length>0&&(f+=b.clearHtml(i)),c&&d<e.length-1&&h.is("br, p, div, li, tr, pre, address, artticle, aside, dl, figcaption, footer, h1, h2, h3, h4, header"))?f+="\n":void 0}}(this)),f},c.prototype.beautify=function(b){var c;return c=function(a){return!!(a.is("p")&&!a.text()&&a.children(":not(br)").length<1)},b.each(function(){return function(b,d){var e;return e=a(d),e.is(':not(img, br, col, td, hr, [class^="simditor-"]):empty')&&e.remove(),c(e)&&e.remove(),e.find(':not(img, br, col, td, hr, [class^="simditor-"]):empty').remove()}}(this))},c}(b);var j,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},i=[].indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(b in this&&this[b]===a)return b;return-1};j=function(b){function d(){return d.__super__.constructor.apply(this,arguments)}return g(d,b),d.pluginName="InputManager",d.prototype.opts={pasteImage:!1},d.prototype._modifierKeys=[16,17,18,91,93,224],d.prototype._arrowKeys=[37,38,39,40],d.prototype._init=function(){var b;return this.editor=this._module,this.opts.pasteImage&&"string"!=typeof this.opts.pasteImage&&(this.opts.pasteImage="inline"),this._keystrokeHandlers={},this.hotkeys=c({el:this.editor.body}),this._pasteArea=a("<div/>").css({width:"1px",height:"1px",overflow:"hidden",position:"fixed",right:"0",bottom:"100px"}).attr({tabIndex:"-1",contentEditable:!0}).addClass("simditor-paste-area").appendTo(this.editor.el),this._cleanPasteArea=a("<textarea/>").css({width:"1px",height:"1px",overflow:"hidden",position:"fixed",right:"0",bottom:"101px"}).attr({tabIndex:"-1"}).addClass("simditor-clean-paste-area").appendTo(this.editor.el),a(document).on("selectionchange.simditor"+this.editor.id,function(a){return function(){return a.focused?(a._selectionTimer&&(clearTimeout(a._selectionTimer),a._selectionTimer=null),a._selectionTimer=setTimeout(function(){return a.editor.trigger("selectionchanged")},20)):void 0}}(this)),this.editor.on("valuechanged",function(b){return function(){return!b.editor.util.closestBlockEl()&&b.focused&&(b.editor.selection.save(),b.editor.formatter.format(),b.editor.selection.restore()),b.editor.body.find("hr, pre, .simditor-table").each(function(c,d){var e,f;return e=a(d),(e.parent().is("blockquote")||e.parent()[0]===b.editor.body[0])&&(f=!1,0===e.next().length&&(a("<p/>").append(b.editor.util.phBr).insertAfter(e),f=!0),0===e.prev().length&&(a("<p/>").append(b.editor.util.phBr).insertBefore(e),f=!0),f)?setTimeout(function(){return b.editor.trigger("valuechanged")},10):void 0}),b.editor.body.find("pre:empty").append(b.editor.util.phBr),!b.editor.util.supportSelectionChange&&b.focused?b.editor.trigger("selectionchanged"):void 0}}(this)),this.editor.on("selectionchanged",function(a){return function(){return a.editor.undoManager.update()}}(this)),this.editor.body.on("keydown",a.proxy(this._onKeyDown,this)).on("keypress",a.proxy(this._onKeyPress,this)).on("keyup",a.proxy(this._onKeyUp,this)).on("mouseup",a.proxy(this._onMouseUp,this)).on("focus",a.proxy(this._onFocus,this)).on("blur",a.proxy(this._onBlur,this)).on("paste",a.proxy(this._onPaste,this)).on("drop",a.proxy(this._onDrop,this)),this.editor.util.browser.firefox&&(this.addShortcut("cmd+left",function(a){return function(b){return b.preventDefault(),a.editor.selection.sel.modify("move","backward","lineboundary"),!1}}(this)),this.addShortcut("cmd+right",function(a){return function(b){return b.preventDefault(),a.editor.selection.sel.modify("move","forward","lineboundary"),!1}}(this)),this.addShortcut("cmd+a",function(a){return function(){var b,c,d,e;return b=a.editor.body.children(),b.length>0?(c=b.first().get(0),d=b.last().get(0),e=document.createRange(),e.setStart(c,0),e.setEnd(d,a.editor.util.getNodeLength(d)),a.editor.selection.selectRange(e),!1):void 0}}(this))),b=this.editor.util.os.mac?"cmd+enter":"ctrl+enter",this.addShortcut(b,function(a){return function(){return a.editor.el.closest("form").find("button:submit").click(),!1}}(this)),this.editor.textarea.attr("autofocus")?setTimeout(function(a){return function(){return a.editor.focus()}}(this),0):void 0},d.prototype._onFocus=function(){return this.editor.el.addClass("focus").removeClass("error"),this.focused=!0,this.lastCaretPosition=null,setTimeout(function(a){return function(){return a.editor.triggerHandler("focus"),a.editor.trigger("selectionchanged")}}(this),0)},d.prototype._onBlur=function(){var a;return this.editor.el.removeClass("focus"),this.editor.sync(),this.focused=!1,this.lastCaretPosition=null!=(a=this.editor.undoManager.currentState())?a.caret:void 0,this.editor.triggerHandler("blur")},d.prototype._onMouseUp=function(){return this.editor.util.supportSelectionChange?void 0:setTimeout(function(a){return function(){return a.editor.trigger("selectionchanged")}}(this),0)},d.prototype._onKeyDown=function(b){var c,d,e,f,g,h;if(this.editor.triggerHandler(b)===!1)return!1;if(!this.hotkeys.respondTo(b)){if(b.which in this._keystrokeHandlers){if(e="function"==typeof(f=this._keystrokeHandlers[b.which])["*"]?f["*"](b):void 0)return this.editor.trigger("valuechanged"),!1;if(this.editor.util.traverseUp(function(c){return function(d){var f,g;if(1===d.nodeType)return f=null!=(g=c._keystrokeHandlers[b.which])?g[d.tagName.toLowerCase()]:void 0,e="function"==typeof f?f(b,a(d)):void 0,e===!0||e===!1?!1:void 0}}(this)),e)return this.editor.trigger("valuechanged"),!1}if(g=b.which,!(i.call(this._modifierKeys,g)>=0||(h=b.which,i.call(this._arrowKeys,h)>=0)||(d=this.editor.util.metaKey(b),c=this.editor.util.closestBlockEl(),d&&86===b.which)))return this.editor.util.browser.webkit&&8===b.which&&this.editor.selection.rangeAtStartOf(c)?(setTimeout(function(a){return function(){var b;if(a.focused)return b=a.editor.util.closestBlockEl(),a.editor.selection.save(),a.editor.formatter.cleanNode(b,!0),a.editor.selection.restore(),a.editor.trigger("valuechanged")}}(this),10),this.typing=!0):this._typing?(this._typing!==!0&&clearTimeout(this._typing),this._typing=setTimeout(function(a){return function(){return a.editor.trigger("valuechanged"),a._typing=!1}}(this),200)):(setTimeout(function(a){return function(){return a.editor.trigger("valuechanged")}}(this),10),this._typing=!0),null}},d.prototype._onKeyPress=function(a){return this.editor.triggerHandler(a)===!1?!1:void 0},d.prototype._onKeyUp=function(b){var c,d;return this.editor.triggerHandler(b)===!1?!1:!this.editor.util.supportSelectionChange&&(d=b.which,i.call(this._arrowKeys,d)>=0)?void this.editor.trigger("selectionchanged"):void(8!==b.which&&46!==b.which||!this.editor.util.isEmptyNode(this.editor.body)||(this.editor.body.empty(),c=a("<p/>").append(this.editor.util.phBr).appendTo(this.editor.body),this.editor.selection.setRangeAtStartOf(c)))},d.prototype._onPaste=function(b){var c,d,e,f,g,h,i;if(this.editor.triggerHandler(b)===!1)return!1;if(g=this.editor.selection.deleteRangeContents(),g.collapsed||g.collapse(!0),c=this.editor.util.closestBlockEl(),d=c.is("pre, table"),b.originalEvent.clipboardData&&b.originalEvent.clipboardData.items&&b.originalEvent.clipboardData.items.length>0&&(f=b.originalEvent.clipboardData.items[0],/^image\//.test(f.type)&&!d)){if(e=f.getAsFile(),null==e||!this.opts.pasteImage)return;return e.name||(e.name="Clipboard Image.png"),h={},h[this.opts.pasteImage]=!0,null!=(i=this.editor.uploader)&&i.upload(e,h),!1}return this.editor.selection.save(g),d?(this._cleanPasteArea.focus(),this.editor.util.browser.firefox?(b.preventDefault(),this._cleanPasteArea.val(b.originalEvent.clipboardData.getData("text/plain"))):this.editor.util.browser.msie&&10===this.editor.util.browser.version&&(b.preventDefault(),this._cleanPasteArea.val(window.clipboardData.getData("Text")))):(this._pasteArea.focus(),this.editor.util.browser.msie&&10===this.editor.util.browser.version&&(b.preventDefault(),this._pasteArea.html(window.clipboardData.getData("Text")))),setTimeout(function(b){return function(){var e,f,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B;if(b._pasteArea.is(":empty")&&!b._cleanPasteArea.val()?o=null:d?o=b._cleanPasteArea.val():(o=a("<div/>").append(b._pasteArea.contents()),o.find("table colgroup").remove(),b.editor.formatter.format(o),b.editor.formatter.decorate(o),b.editor.formatter.beautify(o.children()),o=o.contents()),b._pasteArea.empty(),b._cleanPasteArea.val(""),g=b.editor.selection.restore(),b.editor.triggerHandler("pasting",[o])!==!1&&o){if(d)if(c.is("table")){for(m=o.split("\n"),k=m.pop(),p=0,t=m.length;t>p;p++)l=m[p],b.editor.selection.insertNode(document.createTextNode(l)),b.editor.selection.insertNode(a("<br/>"));b.editor.selection.insertNode(document.createTextNode(k))}else for(o=a("<div/>").text(o),z=o.contents(),q=0,u=z.length;u>q;q++)n=z[q],b.editor.selection.insertNode(a(n)[0],g);else if(c.is(b.editor.body))for(r=0,v=o.length;v>r;r++)n=o[r],b.editor.selection.insertNode(n,g);else{if(o.length<1)return;if(1===o.length)if(o.is("p")){if(i=o.contents(),1===i.length&&i.is("img")){if(e=i,/^data:image/.test(e.attr("src"))){if(!b.opts.pasteImage)return;return f=b.editor.util.dataURLtoBlob(e.attr("src")),f.name="Clipboard Image.png",h={},h[b.opts.pasteImage]=!0,void(null!=(A=b.editor.uploader)&&A.upload(f,h))}if(e.is('img[src^="webkit-fake-url://"]'))return}for(s=0,w=i.length;w>s;s++)n=i[s],b.editor.selection.insertNode(n,g)}else if(c.is("p")&&b.editor.util.isEmptyNode(c))c.replaceWith(o),b.editor.selection.setRangeAtEndOf(o,g);else if(o.is("ul, ol"))if(1===o.find("li").length)for(o=a("<div/>").text(o.text()),B=o.contents(),y=0,x=B.length;x>y;y++)n=B[y],b.editor.selection.insertNode(a(n)[0],g);else c.is("li")?(c.parent().after(o),b.editor.selection.setRangeAtEndOf(o,g)):(c.after(o),b.editor.selection.setRangeAtEndOf(o,g));else c.after(o),b.editor.selection.setRangeAtEndOf(o,g);else c.is("li")&&(c=c.parent()),b.editor.selection.rangeAtStartOf(c,g)?j="before":b.editor.selection.rangeAtEndOf(c,g)?j="after":(b.editor.selection.breakBlockEl(c,g),j="before"),c[j](o),b.editor.selection.setRangeAtEndOf(o.last(),g)}return b.editor.trigger("valuechanged")}}}(this),10)},d.prototype._onDrop=function(a){return this.editor.triggerHandler(a)===!1?!1:setTimeout(function(a){return function(){return a.editor.trigger("valuechanged")}}(this),0)},d.prototype.addKeystrokeHandler=function(a,b,c){return this._keystrokeHandlers[a]||(this._keystrokeHandlers[a]={}),this._keystrokeHandlers[a][b]=c},d.prototype.addShortcut=function(b,c){return this.hotkeys.add(b,a.proxy(c,this))},d}(b);var k,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};k=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="Keystroke",c.prototype._init=function(){var b;return this.editor=this._module,this.editor.util.browser.safari&&this.editor.inputManager.addKeystrokeHandler("13","*",function(b){return function(c){var d,e;if(c.shiftKey&&(d=b.editor.util.closestBlockEl(),!d.is("pre")))return e=a("<br/>"),b.editor.selection.rangeAtEndOf(d)?(b.editor.selection.insertNode(e),b.editor.selection.insertNode(a("<br/>")),b.editor.selection.setRangeBefore(e)):b.editor.selection.insertNode(e),!0}}(this)),(this.editor.util.browser.webkit||this.editor.util.browser.msie)&&(b=function(b){return function(c,d){var e;if(b.editor.selection.rangeAtEndOf(d))return e=a("<p/>").append(b.editor.util.phBr).insertAfter(d),b.editor.selection.setRangeAtStartOf(e),!0}}(this),this.editor.inputManager.addKeystrokeHandler("13","h1",b),this.editor.inputManager.addKeystrokeHandler("13","h2",b),this.editor.inputManager.addKeystrokeHandler("13","h3",b),this.editor.inputManager.addKeystrokeHandler("13","h4",b),this.editor.inputManager.addKeystrokeHandler("13","h5",b),this.editor.inputManager.addKeystrokeHandler("13","h6",b)),this.editor.inputManager.addKeystrokeHandler("8","*",function(a){return function(){var b,c;return c=a.editor.util.furthestBlockEl(),b=c.prev(),b.is("hr")&&a.editor.selection.rangeAtStartOf(c)?(a.editor.selection.save(),b.remove(),a.editor.selection.restore(),!0):void 0}}(this)),this.editor.inputManager.addKeystrokeHandler("9","*",function(a){return function(b){var c;return c=a.editor.toolbar.findButton("code"),a.editor.opts.tabIndent||c&&c.active?(b.shiftKey?a.editor.util.outdent():a.editor.util.indent(),!0):void 0}}(this)),this.editor.inputManager.addKeystrokeHandler("13","li",function(b){return function(c,d){var e,f,g,h;if(e=d.clone(),e.find("ul, ol").remove(),b.editor.util.isEmptyNode(e)&&d.is(b.editor.util.closestBlockEl())){if(f=d.parent(),d.next("li").length>0){if(!b.editor.util.isEmptyNode(d))return;f.parent("li").length>0?(g=a("<li/>").append(b.editor.util.phBr).insertAfter(f.parent("li")),h=a("<"+f[0].tagName+"/>").append(d.nextAll("li")),g.append(h)):(g=a("<p/>").append(b.editor.util.phBr).insertAfter(f),h=a("<"+f[0].tagName+"/>").append(d.nextAll("li")),g.after(h))}else f.parent("li").length>0?(g=a("<li/>").insertAfter(f.parent("li")),g.append(d.contents().length>0?d.contents():b.editor.util.phBr)):(g=a("<p/>").append(b.editor.util.phBr).insertAfter(f),d.children("ul, ol").length>0&&g.after(d.children("ul, ol")));return d.prev("li").length?d.remove():f.remove(),b.editor.selection.setRangeAtStartOf(g),!0}}}(this)),this.editor.inputManager.addKeystrokeHandler("13","pre",function(b){return function(c,d){var e,f,g;return c.preventDefault(),c.shiftKey?(e=a("<p/>").append(b.editor.util.phBr).insertAfter(d),b.editor.selection.setRangeAtStartOf(e),!0):(g=b.editor.selection.getRange(),f=null,g.deleteContents(),!b.editor.util.browser.msie&&b.editor.selection.rangeAtEndOf(d)?(f=document.createTextNode("\n\n"),g.insertNode(f),g.setEnd(f,1)):(f=document.createTextNode("\n"),g.insertNode(f),g.setStartAfter(f)),g.collapse(!1),b.editor.selection.selectRange(g),!0)}}(this)),this.editor.inputManager.addKeystrokeHandler("13","blockquote",function(a){return function(b,c){var d,e;return d=a.editor.util.closestBlockEl(),d.is("p")&&!d.next().length&&a.editor.util.isEmptyNode(d)?(c.after(d),e=document.createRange(),a.editor.selection.setRangeAtStartOf(d,e),!0):void 0}}(this)),this.editor.inputManager.addKeystrokeHandler("8","li",function(b){return function(c,d){var e,f,g,h,i,j,k,l;return f=d.children("ul, ol"),i=d.prev("li"),f.length>0&&i.length>0?(l="",j=null,d.contents().each(function(b,c){if(1===c.nodeType&&/UL|OL/.test(c.nodeName))return!1;if(1!==c.nodeType||!/BR/.test(c.nodeName))return 3===c.nodeType&&c.nodeValue?l+=c.nodeValue:1===c.nodeType&&(l+=a(c).text()),j=a(c)}),j&&1===l.length&&b.editor.util.browser.firefox&&!j.next("br").length?(e=a(b.editor.util.phBr).insertAfter(j),j.remove(),b.editor.selection.setRangeBefore(e),!0):l.length>0?!1:(k=document.createRange(),h=i.children("ul, ol"),h.length>0?(g=a("<li/>").append(b.editor.util.phBr).appendTo(h),h.append(f.children("li")),d.remove(),b.editor.selection.setRangeAtEndOf(g,k)):(b.editor.selection.setRangeAtEndOf(i,k),i.append(f),d.remove(),b.editor.selection.selectRange(k)),!0)):!1}}(this)),this.editor.inputManager.addKeystrokeHandler("8","pre",function(b){return function(c,d){var e,f,g;if(b.editor.selection.rangeAtStartOf(d))return f=d.html().replace("\n","<br/>"),e=a("<p/>").append(f||b.editor.util.phBr).insertAfter(d),d.remove(),g=document.createRange(),b.editor.selection.setRangeAtStartOf(e,g),!0}}(this)),this.editor.inputManager.addKeystrokeHandler("8","blockquote",function(a){return function(b,c){var d,e;if(a.editor.selection.rangeAtStartOf(c))return d=c.children().first().unwrap(),e=document.createRange(),a.editor.selection.setRangeAtStartOf(d,e),!0}}(this))},c}(b);var l,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};l=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="UndoManager",c.prototype._index=-1,c.prototype._capacity=50,c.prototype._timer=null,c.prototype._init=function(){var a,b;return this.editor=this._module,this._stack=[],this.editor.util.os.mac?(b="cmd+z",a="shift+cmd+z"):this.editor.util.os.win?(b="ctrl+z",a="ctrl+y"):(b="ctrl+z",a="shift+ctrl+z"),this.editor.inputManager.addShortcut(b,function(a){return function(b){return b.preventDefault(),a.undo(),!1}}(this)),this.editor.inputManager.addShortcut(a,function(a){return function(b){return b.preventDefault(),a.redo(),!1}}(this)),this.editor.on("valuechanged",function(a){return function(b,c){return"undo"!==c?(a._timer&&(clearTimeout(a._timer),a._timer=null),a._timer=setTimeout(function(){return a._pushUndoState(),a._timer=null},200)):void 0}}(this))},c.prototype._pushUndoState=function(){var a,b;if(this.editor.triggerHandler("pushundostate")!==!1&&(a=this.currentState(),b=this.editor.body.html(),!a||a.html!==b))return this._index+=1,this._stack.length=this._index,this._stack.push({html:b,caret:this.caretPosition()}),this._stack.length>this._capacity?(this._stack.shift(),this._index-=1):void 0},c.prototype.currentState=function(){return this._stack.length&&this._index>-1?this._stack[this._index]:null},c.prototype.undo=function(){var a;if(!(this._index<1||this._stack.length<2))return this.editor.hidePopover(),this._index-=1,a=this._stack[this._index],this.editor.body.html(a.html),this.caretPosition(a.caret),this.editor.body.find(".selected").removeClass("selected"),this.editor.sync(),this.editor.trigger("valuechanged",["undo"])},c.prototype.redo=function(){var a;if(!(this._index<0||this._stack.length<this._index+2))return this.editor.hidePopover(),this._index+=1,a=this._stack[this._index],this.editor.body.html(a.html),this.caretPosition(a.caret),this.editor.body.find(".selected").removeClass("selected"),this.editor.sync(),this.editor.trigger("valuechanged",["undo"])},c.prototype.update=function(){var a,b;if(!this._timer&&(a=this.currentState()))return b=this.editor.body.html(),a.html=b,a.caret=this.caretPosition()},c.prototype._getNodeOffset=function(b,c){var d,e,f;return d=c?a(b):a(b).parent(),f=0,e=!1,d.contents().each(function(){return function(a,d){return c===a||b===d?!1:(3===d.nodeType?e||(f+=1,e=!0):(f+=1,e=!1),null)}}(this)),f},c.prototype._getNodePosition=function(a,b){var c,d;if(3===a.nodeType)for(d=a.previousSibling;d&&3===d.nodeType;)a=d,b+=this.editor.util.getNodeLength(d),d=d.previousSibling;else b=this._getNodeOffset(a,b);return c=[],c.unshift(b),this.editor.util.traverseUp(function(a){return function(b){return c.unshift(a._getNodeOffset(b))}}(this),a),c},c.prototype._getNodeByPosition=function(b){var c,d,e,f,g,h,i,j;for(f=this.editor.body[0],j=b.slice(0,b.length-1),e=h=0,i=j.length;i>h;e=++h){if(g=j[e],d=f.childNodes,g>d.length-1){if(e!==b.length-2||!a(f).is("pre")){f=null;break}c=document.createTextNode(""),f.appendChild(c),d=f.childNodes}f=d[g]}return f},c.prototype.caretPosition=function(a){var b,c,d,e,f;if(a){if(this.editor.inputManager.focused||this.editor.body.focus(),!a.start)return void this.editor.body.blur();if(e=this._getNodeByPosition(a.start),f=a.start[a.start.length-1],a.collapsed?(b=e,c=f):(b=this._getNodeByPosition(a.end),c=a.start[a.start.length-1]),!e||!b)throw new Error("simditor: invalid caret state");return d=document.createRange(),d.setStart(e,f),d.setEnd(b,c),this.editor.selection.selectRange(d)}return d=this.editor.selection.getRange(),this.editor.inputManager.focused&&null!=d?(a={start:[],end:null,collapsed:!0},a.start=this._getNodePosition(d.startContainer,d.startOffset),d.collapsed||(a.end=this._getNodePosition(d.endContainer,d.endOffset),a.collapsed=!1),a):{}},c}(b);var m,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};m=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="Util",c.prototype._init=function(){return this.editor=this._module,this.browser.msie&&this.browser.version<11?this.phBr="":void 0},c.prototype.phBr="<br/>",c.prototype.os=function(){var a;return a={},/Mac/.test(navigator.appVersion)?a.mac=!0:/Linux/.test(navigator.appVersion)?a.linux=!0:/Win/.test(navigator.appVersion)?a.win=!0:/X11/.test(navigator.appVersion)&&(a.unix=!0),/Mobi/.test(navigator.appVersion)&&(a.mobile=!0),a}(),c.prototype.browser=function(){var a,b,c,d,e,f,g,h,i;return e=navigator.userAgent,c=/(msie|trident)/i.test(e),a=/chrome|crios/i.test(e),d=/safari/i.test(e)&&!a,b=/firefox/i.test(e),c?{msie:!0,version:1*(null!=(f=e.match(/(msie |rv:)(\d+(\.\d+)?)/i))?f[2]:void 0)}:a?{webkit:!0,chrome:!0,version:1*(null!=(g=e.match(/(?:chrome|crios)\/(\d+(\.\d+)?)/i))?g[1]:void 0)}:d?{webkit:!0,safari:!0,version:1*(null!=(h=e.match(/version\/(\d+(\.\d+)?)/i))?h[1]:void 0)}:b?{mozilla:!0,firefox:!0,version:1*(null!=(i=e.match(/firefox\/(\d+(\.\d+)?)/i))?i[1]:void 0)}:{}}(),c.prototype.supportSelectionChange=function(){var a,b;if(b=document.onselectionchange,void 0!==b)try{return document.onselectionchange=0,null===document.onselectionchange}catch(c){a=c}finally{document.onselectionchange=b}return!1}(),c.prototype.reflow=function(b){return null==b&&(b=document),a(b)[0].offsetHeight},c.prototype.metaKey=function(a){var b;return b=/Mac/.test(navigator.userAgent),b?a.metaKey:a.ctrlKey},c.prototype.isEmptyNode=function(b){var c;return c=a(b),c.is(":empty")||!c.text()&&!c.find(":not(br, span, div)").length},c.prototype.isBlockNode=function(b){return b=a(b)[0],b&&3!==b.nodeType?/^(div|p|ul|ol|li|blockquote|hr|pre|h1|h2|h3|h4|table)$/.test(b.nodeName.toLowerCase()):!1},c.prototype.closestBlockEl=function(b){var c,d,e;return null==b&&(e=this.editor.selection.getRange(),b=null!=e?e.commonAncestorContainer:void 0),c=a(b),c.length?(d=c.parentsUntil(this.editor.body).addBack(),d=d.filter(function(a){return function(b){return a.isBlockNode(d.eq(b))}}(this)),d.length?d.last():null):null},c.prototype.furthestNode=function(b,c){var d,e,f;return null==b&&(f=this.editor.selection.getRange(),b=null!=f?f.commonAncestorContainer:void 0),d=a(b),d.length?(e=d.parentsUntil(this.editor.body).addBack(),e=e.filter(function(){return function(b){var d;return d=e.eq(b),a.isFunction(c)?c(d):d.is(c)}}(this)),e.length?e.first():null):null},c.prototype.furthestBlockEl=function(a){return this.furthestNode(a,this.isBlockNode)},c.prototype.getNodeLength=function(a){switch(a.nodeType){case 7:case 10:return 0;case 3:case 8:return a.length;default:return a.childNodes.length}},c.prototype.traverseUp=function(b,c){var d,e,f,g,h,i,j;if(null==c&&(f=this.editor.selection.getRange(),c=null!=f?f.commonAncestorContainer:void 0),null==c||!a.contains(this.editor.body[0],c))return!1;for(e=a(c).parentsUntil(this.editor.body).get(),e.unshift(c),j=[],h=0,i=e.length;i>h&&(d=e[h],g=b(d),g!==!1);h++)j.push(void 0);return j},c.prototype.indent=function(){var b,c,d,e,f,g,h,i,j,k;if(b=this.editor.util.closestBlockEl(),!(b&&b.length>0))return!1;if(b.is("pre"))i=document.createTextNode("  "),this.editor.selection.insertNode(i);else if(b.is("li")){if(e=b.prev("li"),e.length<1)return!1;this.editor.selection.save(),j=b.parent()[0].tagName,c=e.children("ul, ol"),c.length>0?c.append(b):a("<"+j+"/>").append(b).appendTo(e),this.editor.selection.restore()}else if(b.is("p, h1, h2, h3, h4"))g=null!=(k=b.attr("data-indent"))?k:0,g=1*g+1,g>10&&(g=10),b.attr("data-indent",g);else if(b.is("table")){if(h=this.editor.selection.getRange(),f=a(h.commonAncestorContainer).closest("td"),d=f.next("td"),d.length>0||(d=f.parent("tr").next("tr").find("td:first")),!(f.length>0&&d.length>0))return!1;
this.editor.selection.setRangeAtEndOf(d)}else i=document.createTextNode("    "),this.editor.selection.insertNode(i);return this.editor.trigger("valuechanged"),!0},c.prototype.outdent=function(){var b,c,d,e,f,g,h,i,j;if(b=this.editor.util.closestBlockEl(),!(b&&b.length>0))return!1;if(b.is("pre"))return!1;if(b.is("li")){if(c=b.parent(),d=c.parent("li"),d.length<1)return g=this.editor.toolbar.findButton(c[0].tagName.toLowerCase()),null!=g&&g.command(),!1;this.editor.selection.save(),b.next("li").length>0&&a("<"+c[0].tagName+"/>").append(b.nextAll("li")).appendTo(b),b.insertAfter(d),c.children("li").length<1&&c.remove(),this.editor.selection.restore()}else if(b.is("p, h1, h2, h3, h4"))h=null!=(j=b.attr("data-indent"))?j:0,h=1*h-1,0>h&&(h=0),b.attr("data-indent",h);else{if(!b.is("table"))return!1;if(i=this.editor.selection.getRange(),f=a(i.commonAncestorContainer).closest("td"),e=f.prev("td"),e.length>0||(e=f.parent("tr").prev("tr").find("td:last")),!(f.length>0&&e.length>0))return!1;this.editor.selection.setRangeAtEndOf(e)}return this.editor.trigger("valuechanged"),!0},c.prototype.dataURLtoBlob=function(a){var b,c,d,e,f,g,h,i,j,k,l;if(g=window.Blob&&function(){var a;try{return Boolean(new Blob)}catch(b){return a=b,!1}}(),f=g&&window.Uint8Array&&function(){var a;try{return 100===new Blob([new Uint8Array(100)]).size}catch(b){return a=b,!1}}(),b=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,!((g||b)&&window.atob&&window.ArrayBuffer&&window.Uint8Array))return!1;for(e=a.split(",")[0].indexOf("base64")>=0?atob(a.split(",")[1]):decodeURIComponent(a.split(",")[1]),c=new ArrayBuffer(e.length),i=new Uint8Array(c),h=k=0,l=e.length;l>=0?l>=k:k>=l;h=l>=0?++k:--k)i[h]=e.charCodeAt(h);return j=a.split(",")[0].split(":")[1].split(";")[0],g?new Blob([f?i:c],{type:j}):(d=new b,d.append(c),d.getBlob(j))},c}(b);var n,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};n=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.pluginName="Toolbar",c.prototype.opts={toolbar:!0,toolbarFloat:!0,toolbarHidden:!1,toolbarFloatOffset:0},c.prototype._tpl={wrapper:'<div class="simditor-toolbar"><ul></ul></div>',separator:'<li><span class="separator"></span></li>'},c.prototype._init=function(){var b;return this.editor=this._module,this.opts.toolbar?(a.isArray(this.opts.toolbar)||(this.opts.toolbar=["bold","italic","underline","strikethrough","|","ol","ul","blockquote","code","|","link","image","|","indent","outdent"]),this._render(),this.list.on("click",function(){return function(){return!1}}(this)),this.wrapper.on("mousedown",function(a){return function(){return a.list.find(".menu-on").removeClass(".menu-on")}}(this)),a(document).on("mousedown.simditor"+this.editor.id,function(a){return function(){return a.list.find(".menu-on").removeClass(".menu-on")}}(this)),!this.opts.toolbarHidden&&this.opts.toolbarFloat&&(this.wrapper.width(this.wrapper.outerWidth()),this.wrapper.css("top",this.opts.toolbarFloatOffset),b=this.wrapper.outerHeight(),this.editor.util.os.mobile||a(window).on("resize.simditor-"+this.editor.id,function(a){return function(){return a.wrapper.css("position","static"),a.editor.util.reflow(a.wrapper),a.wrapper.css("left",a.wrapper.offset().left),a.wrapper.css("position","")}}(this)).resize(),a(window).on("scroll.simditor-"+this.editor.id,function(c){return function(){var d,e,f;if(f=c.editor.wrapper.offset().top,d=f+c.editor.wrapper.outerHeight()-80,e=a(document).scrollTop()+c.opts.toolbarFloatOffset,f>=e||e>=d){if(c.editor.wrapper.removeClass("toolbar-floating").css("padding-top",""),c.editor.util.os.mobile)return c.wrapper.css("top",c.opts.toolbarFloatOffset)}else if(c.editor.wrapper.addClass("toolbar-floating").css("padding-top",b),c.editor.util.os.mobile)return c.wrapper.css("top",e-f+c.opts.toolbarFloatOffset)}}(this))),this.editor.on("selectionchanged",function(a){return function(){return a.toolbarStatus()}}(this)),this.editor.on("destroy",function(a){return function(){return a.buttons.length=0}}(this)),a(document).on("mousedown.simditor-"+this.editor.id,function(a){return function(){return a.list.find("li.menu-on").removeClass("menu-on")}}(this))):void 0},c.prototype._render=function(){var b,c,d,e;for(this.buttons=[],this.wrapper=a(this._tpl.wrapper).prependTo(this.editor.wrapper),this.list=this.wrapper.find("ul"),e=this.opts.toolbar,c=0,d=e.length;d>c;c++)if(b=e[c],"|"!==b){if(!this.constructor.buttons[b])throw new Error('simditor: invalid toolbar button "'+b+'"');this.buttons.push(new this.constructor.buttons[b]({editor:this.editor}))}else a(this._tpl.separator).appendTo(this.list);return this.opts.toolbarHidden?this.wrapper.hide():this.editor.placeholderEl.css("top",this.wrapper.outerHeight())},c.prototype.toolbarStatus=function(b){var c;if(this.editor.inputManager.focused)return c=this.buttons.slice(0),this.editor.util.traverseUp(function(){return function(d){var e,f,g,h,i,j,k;for(g=[],f=h=0,j=c.length;j>h;f=++h)e=c[f],(null==b||e.name===b)&&(e.status&&e.status(a(d))!==!0||g.push(e));for(i=0,k=g.length;k>i;i++)e=g[i],f=a.inArray(e,c),c.splice(f,1);return 0===c.length?!1:void 0}}(this))},c.prototype.findButton=function(a){var b;return b=this.list.find(".toolbar-item-"+a).data("button"),null!=b?b:null},c.addButton=function(a){return this.buttons[a.prototype.name]=a},c.buttons={},c}(b);var o,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};o=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.connect(m),c.connect(j),c.connect(l),c.connect(k),c.connect(h),c.connect(e),c.connect(n),c.count=0,c.prototype.opts={textarea:null,placeholder:"",defaultImage:"images/image.png",params:{},upload:!1,tabIndent:!0},c.prototype._init=function(){var b,e,f,g;if(this.textarea=a(this.opts.textarea),this.opts.placeholder=this.opts.placeholder||this.textarea.attr("placeholder"),!this.textarea.length)throw new Error("simditor: param textarea is required.");if(e=this.textarea.data("simditor"),null!=e&&e.destroy(),this.id=++c.count,this._render(),this.opts.upload&&d&&(g="object"==typeof this.opts.upload?this.opts.upload:{},this.uploader=d(g)),f=this.textarea.closest("form"),f.length&&(f.on("submit.simditor-"+this.id,function(a){return function(){return a.sync()}}(this)),f.on("reset.simditor-"+this.id,function(a){return function(){return a.setValue("")}}(this))),this.on("initialized",function(a){return function(){return a.opts.placeholder&&a.on("valuechanged",function(){return a._placeholder()}),a.setValue(a.textarea.val().trim()||"")}}(this)),this.util.browser.mozilla){this.util.reflow();try{return document.execCommand("enableObjectResizing",!1,!1),document.execCommand("enableInlineTableEditing",!1,!1)}catch(h){b=h}}},c.prototype._tpl='<div class="simditor">\n  <div class="simditor-wrapper">\n    <div class="simditor-placeholder"></div>\n    <div class="simditor-body" contenteditable="true">\n    </div>\n  </div>\n</div>',c.prototype._render=function(){var b,c,d,e;if(this.el=a(this._tpl).insertBefore(this.textarea),this.wrapper=this.el.find(".simditor-wrapper"),this.body=this.wrapper.find(".simditor-body"),this.placeholderEl=this.wrapper.find(".simditor-placeholder").append(this.opts.placeholder),this.el.append(this.textarea).data("simditor",this),this.textarea.data("simditor",this).hide().blur(),this.body.attr("tabindex",this.textarea.attr("tabindex")),this.util.os.mac?this.el.addClass("simditor-mac"):this.util.os.linux&&this.el.addClass("simditor-linux"),this.util.os.mobile&&this.el.addClass("simditor-mobile"),this.opts.params){d=this.opts.params,e=[];for(b in d)c=d[b],e.push(a("<input/>",{type:"hidden",name:b,value:c}).insertAfter(this.textarea));return e}},c.prototype._placeholder=function(){var a,b;return a=this.body.children(),0===a.length||1===a.length&&this.util.isEmptyNode(a)&&(null!=(b=a.data("indent"))?b:0)<1?this.placeholderEl.show():this.placeholderEl.hide()},c.prototype.setValue=function(a){return this.hidePopover(),this.textarea.val(a),this.body.html(a),this.formatter.format(),this.formatter.decorate(),this.util.reflow(this.body),this.inputManager.lastCaretPosition=null,this.trigger("valuechanged")},c.prototype.getValue=function(){return this.sync()},c.prototype.sync=function(){var b,c,d,e,f,g;for(c=this.body.clone(),this.formatter.undecorate(c),this.formatter.format(c),this.formatter.autolink(c),b=c.children(),f=b.last("p"),e=b.first("p");f.is("p")&&this.util.isEmptyNode(f);)d=f,f=f.prev("p"),d.remove();for(;e.is("p")&&this.util.isEmptyNode(e);)d=e,e=f.next("p"),d.remove();return c.find("img.uploading").remove(),g=a.trim(c.html()),this.textarea.val(g),g},c.prototype.focus=function(){var a,b;if(this.inputManager.lastCaretPosition)return this.undoManager.caretPosition(this.inputManager.lastCaretPosition);if(a=this.body.find("p, li, pre, h1, h2, h3, h4, td").first(),a.length>0)return b=document.createRange(),this.selection.setRangeAtStartOf(a,b),this.body.focus()},c.prototype.blur=function(){return this.body.blur()},c.prototype.hidePopover=function(){return this.el.find(".simditor-popover").each(function(){return function(b,c){return c=a(c).data("popover"),c.active?c.hide():void 0}}(this))},c.prototype.destroy=function(){return this.triggerHandler("destroy"),this.textarea.closest("form").off(".simditor .simditor-"+this.id),this.selection.clear(),this.inputManager.focused=!1,this.textarea.insertBefore(this.el).hide().val("").removeData("simditor"),this.el.remove(),a(document).off(".simditor-"+this.id),a(window).off(".simditor-"+this.id),this.off()},c}(b),o.i18n={"zh-CN":{blockquote:"引用",bold:"加粗文字",code:"插入代码",color:"文字颜色",hr:"分隔线",image:"插入图片",localImage:"本地图片",externalImage:"外链图片",uploadImage:"上传图片",uploadFailed:"上传失败了",uploadError:"上传出错了",imageUrl:"图片地址",imageSize:"图片尺寸",restoreImageSize:"还原图片尺寸",uploading:"正在上传",indent:"向右缩进",outdent:"向左缩进",italic:"斜体文字",link:"插入链接",text:"文本",linkText:"链接文字",linkUrl:"地址",removeLink:"移除链接",ol:"有序列表",ul:"无序列表",strikethrough:"删除线文字",table:"表格",deleteRow:"删除行",insertRowAbove:"在上面插入行",insertRowBelow:"在下面插入行",deleteColumn:"删除列",insertColumnLeft:"在左边插入列",insertColumnRight:"在右边插入列",deleteTable:"删除表格",title:"标题",normalText:"普通文本",underline:"下划线文字"}};var p,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},q=[].slice;p=function(b){function c(a){this.editor=a.editor,this.title=this._t(this.name),c.__super__.constructor.call(this,a)}return g(c,b),c.prototype._tpl={item:'<li><a tabindex="-1" unselectable="on" class="toolbar-item" href="javascript:;"><span></span></a></li>',menuWrapper:'<div class="toolbar-menu"></div>',menuItem:'<li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;"><span></span></a></li>',separator:'<li><span class="separator"></span></li>'},c.prototype.name="",c.prototype.icon="",c.prototype.title="",c.prototype.text="",c.prototype.htmlTag="",c.prototype.disableTag="",c.prototype.menu=!1,c.prototype.active=!1,c.prototype.disabled=!1,c.prototype.needFocus=!0,c.prototype.shortcut=null,c.prototype._init=function(){var b,c,d,e,f;for(this.render(),this.el.on("mousedown",function(a){return function(b){var c,d;return b.preventDefault(),a.el.hasClass("disabled")||a.needFocus&&!a.editor.inputManager.focused?!1:a.menu?(a.wrapper.toggleClass("menu-on").siblings("li").removeClass("menu-on"),a.wrapper.is(".menu-on")&&(c=a.menuWrapper.offset().left+a.menuWrapper.outerWidth()+5-a.editor.wrapper.offset().left-a.editor.wrapper.outerWidth(),c>0&&a.menuWrapper.css({left:"auto",right:0}),a.trigger("menuexpand")),!1):(d=a.el.data("param"),a.command(d),!1)}}(this)),this.wrapper.on("click","a.menu-item",function(b){return function(c){var d,e;return c.preventDefault(),d=a(c.currentTarget),b.wrapper.removeClass("menu-on"),d.hasClass("disabled")||b.needFocus&&!b.editor.inputManager.focused?!1:(b.editor.toolbar.wrapper.removeClass("menu-on"),e=d.data("param"),b.command(e),!1)}}(this)),this.wrapper.on("mousedown","a.menu-item",function(){return function(){return!1}}(this)),this.editor.on("blur",function(a){return function(){return a.setActive(!1),a.setDisabled(!1)}}(this)),null!=this.shortcut&&this.editor.inputManager.addShortcut(this.shortcut,function(a){return function(){return a.el.mousedown(),!1}}(this)),e=this.htmlTag.split(","),f=[],c=0,d=e.length;d>c;c++)b=e[c],b=a.trim(b),f.push(b&&a.inArray(b,this.editor.formatter._allowedTags)<0?this.editor.formatter._allowedTags.push(b):void 0);return f},c.prototype.render=function(){return this.wrapper=a(this._tpl.item).appendTo(this.editor.toolbar.list),this.el=this.wrapper.find("a.toolbar-item"),this.el.attr("title",this.title).addClass("toolbar-item-"+this.name).data("button",this),this.el.find("span").addClass(this.icon?"fa fa-"+this.icon:"").text(this.text),this.menu?(this.menuWrapper=a(this._tpl.menuWrapper).appendTo(this.wrapper),this.menuWrapper.addClass("toolbar-menu-"+this.name),this.renderMenu()):void 0},c.prototype.renderMenu=function(){var b,c,d,e,f,g,h,i;if(a.isArray(this.menu)){for(this.menuEl=a("<ul/>").appendTo(this.menuWrapper),g=this.menu,i=[],e=0,f=g.length;f>e;e++)d=g[e],"|"!==d?(c=a(this._tpl.menuItem).appendTo(this.menuEl),i.push(b=c.find("a.menu-item").attr({title:null!=(h=d.title)?h:d.text,"data-param":d.param}).addClass("menu-item-"+d.name).find("span").text(d.text))):a(this._tpl.separator).appendTo(this.menuEl);return i}},c.prototype.setActive=function(a){return a!==this.active?(this.active=a,this.el.toggleClass("active",this.active),this.editor.toolbar.trigger("buttonstatus",[this])):void 0},c.prototype.setDisabled=function(a){return a!==this.disabled?(this.disabled=a,this.el.toggleClass("disabled",this.disabled),this.editor.toolbar.trigger("buttonstatus",[this])):void 0},c.prototype.status=function(a){return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:(null!=a&&this.setActive(a.is(this.htmlTag)),this.active)},c.prototype.command=function(){},c.prototype._t=function(){var a,b,d;return a=1<=arguments.length?q.call(arguments,0):[],b=c.__super__._t.apply(this,a),b||(b=(d=this.editor)._t.apply(d,a)),b},c}(b),o.Button=p;var r,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};r=function(b){function c(a){this.button=a.button,this.editor=a.button.editor,c.__super__.constructor.call(this,a)}return g(c,b),c.prototype.offset={top:4,left:0},c.prototype.target=null,c.prototype.active=!1,c.prototype._init=function(){return this.el=a('<div class="simditor-popover"></div>').appendTo(this.editor.el).data("popover",this),this.render(),this.el.on("mouseenter",function(a){return function(){return a.el.addClass("hover")}}(this)),this.el.on("mouseleave",function(a){return function(){return a.el.removeClass("hover")}}(this))},c.prototype.render=function(){},c.prototype.show=function(b,c){return null==c&&(c="bottom"),null!=b?(this.el.siblings(".simditor-popover").each(function(){return function(b,c){return c=a(c).data("popover"),c.active?c.hide():void 0}}(this)),this.target=b.addClass("selected"),this.active?(this.refresh(c),this.trigger("popovershow")):(this.active=!0,this.el.css({left:-9999}).show(),setTimeout(function(a){return function(){return a.refresh(c),a.trigger("popovershow")}}(this),0))):void 0},c.prototype.hide=function(){return this.active?(this.target&&this.target.removeClass("selected"),this.target=null,this.active=!1,this.el.hide(),this.trigger("popoverhide")):void 0},c.prototype.refresh=function(a){var b,c,d,e,f;return null==a&&(a="bottom"),this.active?(b=this.editor.el.offset(),e=this.target.offset(),d=this.target.outerHeight(),"bottom"===a?f=e.top-b.top+d:"top"===a&&(f=e.top-b.top-this.el.height()),c=Math.min(e.left-b.left,this.editor.wrapper.width()-this.el.outerWidth()-10),this.el.css({top:f+this.offset.top,left:c+this.offset.left})):void 0},c.prototype.destroy=function(){return this.target=null,this.active=!1,this.editor.off(".linkpopover"),this.el.remove()},c}(b),o.Popover=r;var s,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};s=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="title",c.prototype.htmlTag="h1, h2, h3, h4",c.prototype.disableTag="pre, table",c.prototype._init=function(){return this.menu=[{name:"normal",text:this._t("normalText"),param:"p"},"|",{name:"h1",text:this._t("title")+" 1",param:"h1"},{name:"h2",text:this._t("title")+" 2",param:"h2"},{name:"h3",text:this._t("title")+" 3",param:"h3"},{name:"h4",text:this._t("title")+" 4",param:"h4"},{name:"h5",text:this._t("title")+" 5",param:"h5"}],c.__super__._init.call(this)},c.prototype.setActive=function(a,b){return c.__super__.setActive.call(this,a),this.el.removeClass("active-p active-h1 active-h2 active-h3"),a?this.el.addClass("active active-"+b):void 0},c.prototype.status=function(a){var b,c;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:(null!=a&&(b=null!=(c=a[0].tagName)?c.toLowerCase():void 0,this.setActive(a.is(this.htmlTag),b)),this.active)},c.prototype.command=function(b){var c,d,e,f,g,h,i,j,k,l,m;for(h=this.editor.selection.getRange(),j=h.startContainer,f=h.endContainer,e=this.editor.util.closestBlockEl(j),d=this.editor.util.closestBlockEl(f),this.editor.selection.save(),h.setStartBefore(e[0]),h.setEndAfter(d[0]),c=a(h.extractContents()),i=[],c.children().each(function(a){return function(c,d){var e,f,g,h,j;for(f=a._convertEl(d,b),j=[],g=0,h=f.length;h>g;g++)e=f[g],j.push(i.push(e));return j}}(this)),m=i.reverse(),k=0,l=m.length;l>k;k++)g=m[k],h.insertNode(g[0]);return this.editor.selection.restore(),this.editor.trigger("valuechanged")},c.prototype._convertEl=function(b,c){var d,e,f;return e=a(b),f=[],e.is(c)?f.push(e):(d=a("<"+c+"/>").append(e.contents()),f.push(d)),f},c}(p),o.Toolbar.addButton(s);var t,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};t=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="bold",c.prototype.icon="bold",c.prototype.htmlTag="b, strong",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+b",c.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + b )":(this.title=this.title+" ( Ctrl + b )",this.shortcut="ctrl+b"),c.__super__._init.call(this)},c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:(b=document.queryCommandState("bold")===!0,this.setActive(b),b)},c.prototype.command=function(){return document.execCommand("bold"),this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(p),o.Toolbar.addButton(t);var u,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};u=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="italic",c.prototype.icon="italic",c.prototype.htmlTag="i",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+i",c.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + i )":(this.title=this.title+" ( Ctrl + i )",this.shortcut="ctrl+i"),c.__super__._init.call(this)},c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?this.disabled:(b=document.queryCommandState("italic")===!0,this.setActive(b),b)},c.prototype.command=function(){return document.execCommand("italic"),this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(p),o.Toolbar.addButton(u);var v,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};v=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="underline",c.prototype.icon="underline",c.prototype.htmlTag="u",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+u",c.prototype.render=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + u )":(this.title=this.title+" ( Ctrl + u )",this.shortcut="ctrl+u"),c.__super__.render.call(this)},c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?this.disabled:(b=document.queryCommandState("underline")===!0,this.setActive(b),b)},c.prototype.command=function(){return document.execCommand("underline"),this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(p),o.Toolbar.addButton(v);var w,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},q=[].slice;w=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="color",c.prototype.icon="font",c.prototype.disableTag="pre",c.prototype.menu=!0,c.prototype.render=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],c.__super__.render.apply(this,a)},c.prototype.renderMenu=function(){return a('<ul class="color-list">\n  <li><a href="javascript:;" class="font-color font-color-1" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-2" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-3" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-4" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-5" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-6" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-7" data-color=""></a></li>\n  <li><a href="javascript:;" class="font-color font-color-default" data-color=""></a></li>\n</ul>').appendTo(this.menuWrapper),this.menuWrapper.on("mousedown",".color-list",function(){return!1}),this.menuWrapper.on("click",".font-color",function(b){return function(c){var d,e,f,g;if(b.wrapper.removeClass("menu-on"),d=a(c.currentTarget),d.hasClass("font-color-default")){if(e=b.editor.body.find("p, li"),!(e.length>0))return;g=window.getComputedStyle(e[0],null).getPropertyValue("color"),f=b._convertRgbToHex(g)}else g=window.getComputedStyle(d[0],null).getPropertyValue("background-color"),f=b._convertRgbToHex(g);return f?(document.execCommand("foreColor",!1,f),b.editor.trigger("valuechanged")):void 0}}(this))},c.prototype._convertRgbToHex=function(a){var b,c,d;return c=/rgb\((\d+),\s?(\d+),\s?(\d+)\)/g,(b=c.exec(a))?(d=function(a,b,c){var d;return d=function(a){var b;return b=a.toString(16),1===b.length?"0"+b:b},"#"+d(a)+d(b)+d(c)})(1*b[1],1*b[2],1*b[3]):""},c}(p),o.Toolbar.addButton(w);var x,y,z,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};x=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.type="",c.prototype.disableTag="pre, table",c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:null==a?this.active:(b="ul"===this.type?"ol":"ul",a.is(b)?(this.setActive(!1),!0):(this.setActive(a.is(this.htmlTag)),this.active))},c.prototype.command=function(){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r;for(l=this.editor.selection.getRange(),o=l.startContainer,i=l.endContainer,g=this.editor.util.closestBlockEl(o),c=this.editor.util.closestBlockEl(i),this.editor.selection.save(),l.setStartBefore(g[0]),l.setEndAfter(c[0]),g.is("li")&&c.is("li")&&(e=this.editor.util.furthestNode(g,"ul, ol"),d=this.editor.util.furthestNode(c,"ul, ol"),e.is(d)?(j=function(a){var b;for(b=1;!a.parent().is(e);)b+=1,a=a.parent();return b},n=j(g),h=j(c),f=n>h?c.parent():g.parent(),l.setStartBefore(f[0]),l.setEndAfter(f[0])):(l.setStartBefore(e[0]),l.setEndAfter(d[0]))),b=a(l.extractContents()),m=[],b.children().each(function(a){return function(b,c){var d,e,f,g,h;for(e=a._convertEl(c),h=[],f=0,g=e.length;g>f;f++)d=e[f],h.push(m.length&&m[m.length-1].is(a.type)&&d.is(a.type)?m[m.length-1].append(d.children()):m.push(d));return h}}(this)),r=m.reverse(),p=0,q=r.length;q>p;p++)k=r[p],l.insertNode(k[0]);return this.editor.selection.restore(),this.editor.trigger("valuechanged")},c.prototype._convertEl=function(b){var c,d,e,f,g,h,i,j,k;if(c=a(b),h=[],d="ul"===this.type?"ol":"ul",c.is(this.type))c.children("li").each(function(b){return function(c,d){var e,f,g;return f=a(d),e=f.children("ul, ol").remove(),g=a("<p/>").append(a(d).html()||b.editor.util.phBr),h.push(g),e.length>0?h.push(e):void 0}}(this));else if(c.is(d))e=a("<"+this.type+"/>").append(c.html()),h.push(e);else if(c.is("blockquote")){for(k=c.children().get(),i=0,j=k.length;j>i;i++)f=k[i],g=this._convertEl(f);a.merge(h,g)}else c.is("table")||(e=a("<"+this.type+"><li></li></"+this.type+">"),e.find("li").append(c.html()||this.editor.util.phBr),h.push(e));return h},c}(p),y=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return g(b,a),b.prototype.type="ol",b.prototype.name="ol",b.prototype.icon="list-ol",b.prototype.htmlTag="ol",b.prototype.shortcut="cmd+/",b.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + / )":(this.title=this.title+" ( ctrl + / )",this.shortcut="ctrl+/"),b.__super__._init.call(this)},b}(x),z=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return g(b,a),b.prototype.type="ul",b.prototype.name="ul",b.prototype.icon="list-ul",b.prototype.htmlTag="ul",b.prototype.shortcut="cmd+.",b.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + . )":(this.title=this.title+" ( Ctrl + . )",this.shortcut="ctrl+."),b.__super__._init.call(this)},b}(x),o.Toolbar.addButton(y),o.Toolbar.addButton(z);var A,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};A=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="blockquote",c.prototype.icon="quote-left",c.prototype.htmlTag="blockquote",c.prototype.disableTag="pre, table",c.prototype.command=function(){var b,c,d,e,f,g,h,i,j,k,l;for(g=this.editor.selection.getRange(),i=g.startContainer,e=g.endContainer,d=this.editor.util.furthestBlockEl(i),c=this.editor.util.furthestBlockEl(e),this.editor.selection.save(),g.setStartBefore(d[0]),g.setEndAfter(c[0]),b=a(g.extractContents()),h=[],b.children().each(function(a){return function(b,c){var d,e,f,g,i;for(e=a._convertEl(c),i=[],f=0,g=e.length;g>f;f++)d=e[f],i.push(h.length&&h[h.length-1].is(a.htmlTag)&&d.is(a.htmlTag)?h[h.length-1].append(d.children()):h.push(d));return i}}(this)),l=h.reverse(),j=0,k=l.length;k>j;j++)f=l[j],g.insertNode(f[0]);return this.editor.selection.restore(),this.editor.trigger("valuechanged")},c.prototype._convertEl=function(b){var c,d,e;return c=a(b),e=[],c.is(this.htmlTag)?c.children().each(function(){return function(b,c){return e.push(a(c))}}(this)):(d=a("<"+this.htmlTag+"/>").append(c),e.push(d)),e},c}(p),o.Toolbar.addButton(A);var B,C,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},q=[].slice;B=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="code",c.prototype.icon="code",c.prototype.htmlTag="pre",c.prototype.disableTag="li, table",c.prototype._init=function(){return c.__super__._init.call(this),this.editor.on("decorate",function(b){return function(c,d){return d.find("pre").each(function(c,d){return b.decorate(a(d))})}}(this)),this.editor.on("undecorate",function(b){return function(c,d){return d.find("pre").each(function(c,d){return b.undecorate(a(d))})}}(this))},c.prototype.render=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new C({button:this})},c.prototype.status=function(a){var b;return b=c.__super__.status.call(this,a),this.active?this.popover.show(a):this.editor.util.isBlockNode(a)&&this.popover.hide(),b},c.prototype.decorate=function(a){var b;return b=a.attr("data-lang"),a.removeClass(),b&&-1!==b?a.addClass("lang-"+b):void 0},c.prototype.undecorate=function(a){var b;return b=a.attr("data-lang"),a.removeClass(),b&&-1!==b?a.addClass("lang-"+b):void 0},c.prototype.command=function(){var b,c,d,e,f,g,h,i,j,k,l;for(g=this.editor.selection.getRange(),i=g.startContainer,e=g.endContainer,d=this.editor.util.closestBlockEl(i),c=this.editor.util.closestBlockEl(e),g.setStartBefore(d[0]),g.setEndAfter(c[0]),b=a(g.extractContents()),h=[],b.children().each(function(a){return function(b,c){var d,e,f,g,i;for(e=a._convertEl(c),i=[],f=0,g=e.length;g>f;f++)d=e[f],i.push(h.length&&h[h.length-1].is(a.htmlTag)&&d.is(a.htmlTag)?h[h.length-1].append(d.contents()):h.push(d));return i}}(this)),l=h.reverse(),j=0,k=l.length;k>j;j++)f=l[j],g.insertNode(f[0]);return this.editor.selection.setRangeAtEndOf(h[0]),this.editor.trigger("valuechanged")},c.prototype._convertEl=function(b){var c,d,e,f;return c=a(b),f=[],c.is(this.htmlTag)?(d=a("<p/>").append(c.html().replace("\n","<br/>")),f.push(d)):(e=!c.text()&&1===c.children().length&&c.children().is("br")?"\n":this.editor.formatter.clearHtml(c),d=a("<"+this.htmlTag+"/>").text(e),f.push(d)),f},c}(p),C=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return g(b,a),b.prototype._tpl='<div class="code-settings">\n  <div class="settings-field">\n    <select class="select-lang">\n      <option value="-1">选择程序语言</option>\n      <option value="bash">Bash</option>\n      <option value="c++">C++</option>\n      <option value="cs">C#</option>\n      <option value="css">CSS</option>\n      <option value="erlang">Erlang</option>\n      <option value="less">Less</option>\n      <option value="scss">Sass</option>\n      <option value="diff">Diff</option>\n      <option value="coffeeScript">CoffeeScript</option>\n      <option value="html">Html,XML</option>\n      <option value="json">JSON</option>\n      <option value="java">Java</option>\n      <option value="js">JavaScript</option>\n      <option value="markdown">Markdown</option>\n      <option value="oc">Objective C</option>\n      <option value="php">PHP</option>\n      <option value="perl">Perl</option>\n      <option value="python">Python</option>\n      <option value="ruby">Ruby</option>\n      <option value="sql">SQL</option>\n    </select>\n  </div>\n</div>',b.prototype.render=function(){return this.el.addClass("code-popover").append(this._tpl),this.selectEl=this.el.find(".select-lang"),this.selectEl.on("change",function(a){return function(){var b;return a.lang=a.selectEl.val(),b=a.target.hasClass("selected"),a.target.removeClass().removeAttr("data-lang"),-1!==a.lang&&(a.target.addClass("lang-"+a.lang),a.target.attr("data-lang",a.lang)),b?a.target.addClass("selected"):void 0}}(this))},b.prototype.show=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],b.__super__.show.apply(this,a),this.lang=this.target.attr("data-lang"),this.selectEl.val(null!=this.lang?this.lang:-1)},b}(r),o.Toolbar.addButton(B);
var D,E,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},q=[].slice;D=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="link",c.prototype.icon="link",c.prototype.htmlTag="a",c.prototype.disableTag="pre",c.prototype.render=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new E({button:this})},c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:null==a?this.active:(b=!0,!a.is(this.htmlTag)||a.is('[class^="simditor-"]')?(this.setActive(!1),b=!1):this.editor.selection.rangeAtEndOf(a)?(this.setActive(!0),b=!1):this.setActive(!0),b?this.popover.show(a):this.editor.util.isBlockNode(a)&&this.popover.hide(),this.active)},c.prototype.command=function(){var b,c,d,e,f,g,h,i,j,k;return i=this.editor.selection.getRange(),this.active?(d=a(i.commonAncestorContainer).closest("a"),k=document.createTextNode(d.text()),d.replaceWith(k),i.selectNode(k)):(j=i.startContainer,g=i.endContainer,f=this.editor.util.closestBlockEl(j),c=this.editor.util.closestBlockEl(g),b=a(i.extractContents()),h=this.editor.formatter.clearHtml(b.contents(),!1),d=a("<a/>",{href:"http://www.example.com",target:"_blank",text:h||this._t("linkText")}),f[0]===c[0]?i.insertNode(d[0]):(e=a("<p/>").append(d),i.insertNode(e[0])),i.selectNodeContents(d[0]),this.popover.one("popovershow",function(a){return function(){return h?(a.popover.urlEl.focus(),a.popover.urlEl[0].select()):(a.popover.textEl.focus(),a.popover.textEl[0].select())}}(this))),this.editor.selection.selectRange(i),this.editor.trigger("valuechanged")},c}(p),E=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.render=function(){var b;return b='<div class="link-settings">\n  <div class="settings-field">\n    <label>'+this._t("text")+'</label>\n    <input class="link-text" type="text"/>\n    <a class="btn-unlink" href="javascript:;" title="'+this._t("removeLink")+'" tabindex="-1"><span class="fa fa-unlink"></span></a>\n  </div>\n  <div class="settings-field">\n    <label>'+this._t("linkUrl")+'</label>\n    <input class="link-url" type="text"/>\n  </div>\n</div>',this.el.addClass("link-popover").append(b),this.textEl=this.el.find(".link-text"),this.urlEl=this.el.find(".link-url"),this.unlinkEl=this.el.find(".btn-unlink"),this.textEl.on("keyup",function(a){return function(b){return 13!==b.which?a.target.text(a.textEl.val()):void 0}}(this)),this.urlEl.on("keyup",function(a){return function(b){var c;if(13!==b.which)return c=a.urlEl.val(),!/https?:\/\/|^\//gi.test(c)&&c&&(c="http://"+c),a.target.attr("href",c)}}(this)),a([this.urlEl[0],this.textEl[0]]).on("keydown",function(b){return function(c){return 13===c.which||27===c.which||!c.shiftKey&&9===c.which&&a(c.target).hasClass("link-url")?(c.preventDefault(),setTimeout(function(){var a;return a=document.createRange(),b.editor.selection.setRangeAfter(b.target,a),b.hide(),b.editor.trigger("valuechanged")},0)):void 0}}(this)),this.unlinkEl.on("click",function(a){return function(){var b,c;return c=document.createTextNode(a.target.text()),a.target.replaceWith(c),a.hide(),b=document.createRange(),a.editor.selection.setRangeAfter(c,b),a.editor.trigger("valuechanged")}}(this))},c.prototype.show=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],c.__super__.show.apply(this,a),this.textEl.val(this.target.text()),this.urlEl.val(this.target.attr("href"))},c}(r),o.Toolbar.addButton(D);var F,G,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},q=[].slice;F=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="image",c.prototype.icon="picture-o",c.prototype.htmlTag="img",c.prototype.disableTag="pre, table",c.prototype.defaultImage="",c.prototype.needFocus=!1,c.prototype._init=function(){return this.menu=null!=this.editor.uploader?[{name:"upload-image",text:this._t("localImage")},{name:"external-image",text:this._t("externalImage")}]:!1,this.defaultImage=this.editor.opts.defaultImage,this.editor.body.on("click","img:not([data-non-image])",function(b){return function(c){var d,e;return d=a(c.currentTarget),e=document.createRange(),e.selectNode(d[0]),b.editor.selection.selectRange(e),b.editor.util.supportSelectionChange||b.editor.trigger("selectionchanged"),!1}}(this)),this.editor.body.on("mouseup","img:not([data-non-image])",function(){return function(){return!1}}(this)),this.editor.on("selectionchanged.image",function(b){return function(){var c,d,e;return e=b.editor.selection.getRange(),null!=e?(c=a(e.cloneContents()).contents(),1===c.length&&c.is("img:not([data-non-image])")?(d=a(e.startContainer).contents().eq(e.startOffset),b.popover.show(d)):b.popover.hide()):void 0}}(this)),this.editor.on("valuechanged.image",function(b){return function(){var c;return c=b.editor.wrapper.find(".simditor-image-loading"),c.length>0?c.each(function(c,d){var e,f,g;return f=a(d),e=f.data("img"),!(e&&e.parent().length>0)&&(f.remove(),e&&(g=e.data("file"),g&&(b.editor.uploader.cancel(g),b.editor.body.find("img.uploading").length<1)))?b.editor.uploader.trigger("uploadready",[g]):void 0}):void 0}}(this)),c.__super__._init.call(this)},c.prototype.render=function(){var a;return a=1<=arguments.length?q.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new G({button:this})},c.prototype.renderMenu=function(){var b,d,e;return c.__super__.renderMenu.call(this),d=this.menuEl.find(".menu-item-upload-image"),b=null,e=function(c){return function(){return b&&b.remove(),b=a('<input type="file" title="'+c._t("uploadImage")+'" accept="image/*">').appendTo(d)}}(this),e(),d.on("click mousedown","input[type=file]",function(){return function(a){return a.stopPropagation()}}(this)),d.on("change","input[type=file]",function(a){return function(){return a.editor.inputManager.focused?(a.editor.uploader.upload(b,{inline:!0}),e()):(a.editor.one("focus",function(){return a.editor.uploader.upload(b,{inline:!0}),e()}),a.editor.focus()),a.wrapper.removeClass("menu-on")}}(this)),this._initUploader()},c.prototype._initUploader=function(){return null==this.editor.uploader?void this.el.find(".btn-upload").remove():(this.editor.uploader.on("beforeupload",function(b){return function(c,d){var e;if(d.inline)return d.img?e=a(d.img):(e=b.createImage(d.name),d.img=e),e.addClass("uploading"),e.data("file",d),b.editor.uploader.readImageFile(d.obj,function(a){var c;if(e.hasClass("uploading"))return c=a?a.src:b.defaultImage,b.loadImage(e,c,function(){return b.popover.active?(b.popover.refresh(),b.popover.srcEl.val(b._t("uploading")).prop("disabled",!0)):void 0})})}}(this)),this.editor.uploader.on("uploadprogress",function(){return function(a,b,c,d){var e,f,g,h;if(b.inline)return h=c/d,h=(100*h).toFixed(0),h>99&&(h=99),f=b.img.data("mask"),f?(e=f.data("img"),g=f.find("span"),e&&e.parent().length>0&&h!==g.text()?g.text(h):f.remove()):void 0}}(this)),this.editor.uploader.on("uploadsuccess",function(a){return function(b,c,d){var e,f,g;if(c.inline)return e=c.img,e.removeData("file"),e.removeClass("uploading"),f=e.data("mask"),f&&f.remove(),e.removeData("mask"),d.success===!1?(g=d.msg||a._t("uploadFailed"),alert(g),e.attr("src",a.defaultImage)):e.attr("src",d.file_path),a.popover.active&&(a.popover.srcEl.prop("disabled",!1),a.popover.srcEl.val(d.file_path)),a.editor.trigger("valuechanged"),a.editor.body.find("img.uploading").length<1?a.editor.uploader.trigger("uploadready",[c,d]):void 0}}(this)),this.editor.uploader.on("uploaderror",function(b){return function(c,d,e){var f,g,h,i;if(d.inline&&"abort"!==e.statusText){if(e.responseText){try{i=a.parseJSON(e.responseText),h=i.msg}catch(j){c=j,h=b._t("uploadError")}alert(h)}return f=d.img,f.removeData("file"),f.removeClass("uploading"),g=f.data("mask"),g&&g.remove(),f.removeData("mask"),f.attr("src",b.defaultImage),b.popover.active&&(b.popover.srcEl.prop("disabled",!1),b.popover.srcEl.val(b.defaultImage)),b.editor.trigger("valuechanged"),b.editor.body.find("img.uploading").length<1?b.editor.uploader.trigger("uploadready",[d,i]):void 0}}}(this)))},c.prototype.status=function(a){return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:void 0},c.prototype.loadImage=function(b,c,d){var e,f;return e=b.data("mask"),e||(e=a('<div class="simditor-image-loading"><span></span></div>').hide().appendTo(this.editor.wrapper),b.hasClass("uploading")&&e.addClass("uploading"),b.data("mask",e),e.data("img",b)),f=new Image,f.onload=function(a){return function(){var g,h,i,j;if(!e.hasClass("uploading")||b.hasClass("uploading"))return i=f.width,g=f.height,b.attr({src:c,"data-image-size":i+","+g}),b.hasClass("uploading")?(a.editor.util.reflow(a.editor.body),j=a.editor.wrapper.offset(),h=b.offset(),e.css({top:h.top-j.top,left:h.left-j.left,width:b.width(),height:b.height()}).show()):(e.remove(),b.removeData("mask")),d(f)}}(this),f.onerror=function(){return function(){return d(!1),e.remove(),b.removeData("mask")}}(this),f.src=c},c.prototype.createImage=function(b){var c,d,e,f;return null==b&&(b="Image"),this.editor.inputManager.focused||this.editor.focus(),f=this.editor.selection.getRange(),f.deleteContents(),c=this.editor.util.closestBlockEl(),c.is("p")&&!this.editor.util.isEmptyNode(c)&&(c=a("<p/>").append(this.editor.util.phBr).insertAfter(c),this.editor.selection.setRangeAtStartOf(c,f)),d=a("<img/>").attr("alt",b),f.insertNode(d[0]),e=c.next("p"),e.length>0||(e=a("<p/>").append(this.editor.util.phBr).insertAfter(c)),this.editor.selection.setRangeAtStartOf(e),d},c.prototype.command=function(a){var b;return b=this.createImage(),this.loadImage(b,a||this.defaultImage,function(a){return function(){return a.editor.trigger("valuechanged"),a.editor.util.reflow(b),b.click(),a.popover.one("popovershow",function(){return a.popover.srcEl.focus(),a.popover.srcEl[0].select()})}}(this))},c}(p),G=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.offset={top:6,left:-4},c.prototype.render=function(){var b;return b='<div class="link-settings">\n  <div class="settings-field">\n    <label>'+this._t("imageUrl")+'</label>\n    <input class="image-src" type="text" tabindex="1" />\n    <a class="btn-upload" href="javascript:;" title="'+this._t("uploadImage")+'" tabindex="-1">\n      <span class="fa fa-upload"></span>\n    </a>\n  </div>\n  <div class="settings-field">\n    <label>'+this._t("imageSize")+'</label>\n    <input class="image-size" id="image-width" type="text" tabindex="2" />\n    <span class="times">×</span>\n    <input class="image-size" id="image-height" type="text" tabindex="3" />\n    <a class="btn-restore" href="javascript:;" title="'+this._t("restoreImageSize")+'" tabindex="-1">\n      <span class="fa fa-reply"></span>\n    </a>\n  </div>\n</div>',this.el.addClass("image-popover").append(b),this.srcEl=this.el.find(".image-src"),this.srcEl.on("keydown",function(a){return function(b){var c,d;if(13===b.which||27===b.which)return b.preventDefault(),c=function(){return a.button.editor.body.focus(),a.button.editor.selection.setRangeAfter(a.target),a.hide()},13!==b.which||a.target.hasClass("uploading")?c():(d=a.srcEl.val(),/^data:image/.test(d)&&!a.editor.uploader?void c():a.button.loadImage(a.target,d,function(b){var e;if(b)return/^data:image/.test(d)?(e=a.editor.util.dataURLtoBlob(d),e.name="Base64 Image.png",a.editor.uploader.upload(e,{inline:!0,img:a.target})):(c(),a.editor.trigger("valuechanged"))}))}}(this)),this.widthEl=this.el.find("#image-width"),this.heightEl=this.el.find("#image-height"),this.el.find(".image-size").on("blur",function(b){return function(c){return b._resizeImg(a(c.currentTarget)),b.el.data("popover").refresh()}}(this)),this.el.find(".image-size").on("keyup",function(b){return function(c){var d;return d=a(c.currentTarget),13!==c.which&&27!==c.which&&9!==c.which?b._resizeImg(d,!0):void 0}}(this)),this.el.find(".image-size").on("keydown",function(b){return function(c){var d;return d=a(c.currentTarget),13===c.which||27===c.which?(c.preventDefault(),13===c.which?b._resizeImg(d):b._restoreImg(),b.button.editor.body.focus(),b.button.editor.selection.setRangeAfter(b.target),b.hide()):9===c.which?b.el.data("popover").refresh():void 0}}(this)),this.el.find(".btn-restore").on("click",function(a){return function(){return a._restoreImg(),a.el.data("popover").refresh()}}(this)),this.editor.on("valuechanged",function(a){return function(){return a.active?a.refresh():void 0}}(this)),this._initUploader()},c.prototype._initUploader=function(){var b,c;return b=this.el.find(".btn-upload"),null==this.editor.uploader?void b.remove():(c=function(c){return function(){return c.input&&c.input.remove(),c.input=a('<input type="file" title="'+c._t("uploadImage")+'" accept="image/*">').appendTo(b)}}(this),c(),this.el.on("click mousedown","input[type=file]",function(){return function(a){return a.stopPropagation()}}(this)),this.el.on("change","input[type=file]",function(a){return function(){return a.editor.uploader.upload(a.input,{inline:!0,img:a.target}),c()}}(this)))},c.prototype._resizeImg=function(b,c){var d,e,f;return null==c&&(c=!1),e=1*b.val(),a.isNumeric(e)||0>e?(b.is(this.widthEl)?(d=this.height*e/this.width,this.heightEl.val(d)):(f=this.width*e/this.height,this.widthEl.val(f)),c?void 0:this.target.attr({width:f||e,height:d||e})):void 0},c.prototype._restoreImg=function(){var a,b;return a=(null!=(b=this.target.data("image-size"))?b.split(","):void 0)||[this.width,this.height],this.target.attr({width:1*a[0],height:1*a[1]}),this.widthEl.val(a[0]),this.heightEl.val(a[1])},c.prototype.show=function(){var a,b;return b=1<=arguments.length?q.call(arguments,0):[],c.__super__.show.apply(this,b),a=this.target,this.width=a.width(),this.height=a.height(),a.hasClass("uploading")?this.srcEl.val(this._t("uploading")).prop("disabled",!0):(this.srcEl.val(a.attr("src")).prop("disabled",!1),this.widthEl.val(this.width),this.heightEl.val(this.height))},c}(r),o.Toolbar.addButton(F);var H,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};H=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return g(b,a),b.prototype.name="indent",b.prototype.icon="indent",b.prototype._init=function(){return this.title=this._t(this.name)+" (Tab)",b.__super__._init.call(this)},b.prototype.status=function(){return!0},b.prototype.command=function(){return this.editor.util.indent()},b}(p),o.Toolbar.addButton(H);var I,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};I=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return g(b,a),b.prototype.name="outdent",b.prototype.icon="outdent",b.prototype._init=function(){return this.title=this._t(this.name)+" (Shift + Tab)",b.__super__._init.call(this)},b.prototype.status=function(){return!0},b.prototype.command=function(){return this.editor.util.outdent()},b}(p),o.Toolbar.addButton(I);var J,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};J=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="hr",c.prototype.icon="minus",c.prototype.htmlTag="hr",c.prototype.status=function(){return!0},c.prototype.command=function(){var b,c,d,e;return e=this.editor.util.furthestBlockEl(),d=e.next(),d.length>0?this.editor.selection.save():c=a("<p/>").append(this.editor.util.phBr),b=a("<hr/>").insertAfter(e),c?(c.insertAfter(b),this.editor.selection.setRangeAtStartOf(c)):this.editor.selection.restore(),this.editor.trigger("valuechanged")},c}(p),o.Toolbar.addButton(J);var K,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};K=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="table",c.prototype.icon="table",c.prototype.htmlTag="table",c.prototype.disableTag="pre, li, blockquote",c.prototype.menu=!0,c.prototype._init=function(){return c.__super__._init.call(this),a.merge(this.editor.formatter._allowedTags,["tbody","tr","td","colgroup","col"]),a.extend(this.editor.formatter._allowedAttributes,{td:["rowspan","colspan"],col:["width"]}),this._initShortcuts(),this.editor.on("decorate",function(b){return function(c,d){return d.find("table").each(function(c,d){return b.decorate(a(d))})}}(this)),this.editor.on("undecorate",function(b){return function(c,d){return d.find("table").each(function(c,d){return b.undecorate(a(d))})}}(this)),this.editor.on("selectionchanged.table",function(b){return function(){var c,d;return b.editor.body.find(".simditor-table td").removeClass("active"),d=b.editor.selection.getRange(),null!=d?(c=a(d.commonAncestorContainer),d.collapsed&&c.is(".simditor-table")&&(c=c.find(b.editor.selection.rangeAtStartOf(c)?"td:first":"td:last"),b.editor.selection.setRangeAtEndOf(c)),c.closest("td",b.editor.body).addClass("active")):void 0}}(this)),this.editor.on("blur.table",function(a){return function(){return a.editor.body.find(".simditor-table td").removeClass("active")}}(this)),this.editor.inputManager.addKeystrokeHandler("38","td",function(a){return function(b,c){var d,e,f;return e=c.parent("tr"),d=e.prev("tr"),d.length>0?(f=e.find("td").index(c),a.editor.selection.setRangeAtEndOf(d.find("td").eq(f)),!0):!0}}(this)),this.editor.inputManager.addKeystrokeHandler("40","td",function(a){return function(b,c){var d,e,f;return e=c.parent("tr"),d=e.next("tr"),d.length>0?(f=e.find("td").index(c),a.editor.selection.setRangeAtEndOf(d.find("td").eq(f)),!0):!0}}(this))},c.prototype.initResize=function(b){var c,d,e;return e=b.parent(".simditor-table"),c=b.find("colgroup"),c.length<1&&(c=a("<colgroup/>").prependTo(b),b.find("tr:first td").each(function(){return function(){var b;return b=a("<col/>").appendTo(c)}}(this)),this.refreshTableWidth(b)),d=a('<div class="simditor-resize-handle" contenteditable="false"></div>').appendTo(e),e.on("mousemove","td",function(){return function(b){var f,g,h,i,j,k;if(!e.hasClass("resizing"))return g=a(b.currentTarget),i=b.pageX-a(b.currentTarget).offset().left,5>i&&g.prev().length>0&&(g=g.prev()),g.next("td").length<1?void d.hide():(null!=(j=d.data("td"))?j.is(g):void 0)?void d.show():(h=g.parent().find("td").index(g),f=c.find("col").eq(h),(null!=(k=d.data("col"))?k.is(f):void 0)?void d.show():d.css("left",g.position().left+g.outerWidth()-5).data("td",g).data("col",f).show())}}(this)),e.on("mouseleave",function(){return function(){return d.hide()}}(this)),e.on("mousedown",".simditor-resize-handle",function(){return function(b){var c,d,f,g,h,i,j,k,l,m,n;return c=a(b.currentTarget),f=c.data("td"),d=c.data("col"),h=f.next("td"),g=d.next("col"),m=b.pageX,k=1*f.outerWidth(),l=1*h.outerWidth(),j=parseFloat(c.css("left")),n=f.closest("table").width(),i=50,a(document).on("mousemove.simditor-resize-table",function(a){var b,e,f;return b=a.pageX-m,e=k+b,f=l-b,i>e?(e=i,b=i-k,f=l-b):i>f&&(f=i,b=l-i,e=k+b),d.attr("width",e/n*100+"%"),g.attr("width",f/n*100+"%"),c.css("left",j+b)}),a(document).one("mouseup.simditor-resize-table",function(){return a(document).off(".simditor-resize-table"),e.removeClass("resizing")}),e.addClass("resizing"),!1}}(this))},c.prototype._initShortcuts=function(){return this.editor.inputManager.addShortcut("ctrl+alt+up",function(a){return function(){return a.editMenu.find(".menu-item[data-param=insertRowAbove]").click(),!1}}(this)),this.editor.inputManager.addShortcut("ctrl+alt+down",function(a){return function(){return a.editMenu.find(".menu-item[data-param=insertRowBelow]").click(),!1}}(this)),this.editor.inputManager.addShortcut("ctrl+alt+left",function(a){return function(){return a.editMenu.find(".menu-item[data-param=insertColLeft]").click(),!1}}(this)),this.editor.inputManager.addShortcut("ctrl+alt+right",function(a){return function(){return a.editMenu.find(".menu-item[data-param=insertColRight]").click(),!1}}(this))},c.prototype.decorate=function(a){return a.parent(".simditor-table").length>0&&this.undecorate(a),a.wrap('<div class="simditor-table"></div>'),this.initResize(a),a.parent()},c.prototype.undecorate=function(a){return a.parent(".simditor-table").length>0?a.parent().replaceWith(a):void 0},c.prototype.renderMenu=function(){return a('<div class="menu-create-table">\n</div>\n<div class="menu-edit-table">\n  <ul>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="deleteRow"><span>'+this._t("deleteRow")+' ( Ctrl + Alt + → )</span></a></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="insertRowAbove"><span>'+this._t("insertRowAbove")+' ( Ctrl + Alt + ↑ )</span></a></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="insertRowBelow"><span>'+this._t("insertRowBelow")+' ( Ctrl + Alt + ↓ )</span></a></li>\n    <li><span class="separator"></span></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="deleteCol"><span>'+this._t("deleteColumn")+'</span></a></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="insertColLeft"><span>'+this._t("insertColumnLeft")+' ( Ctrl + Alt + ← )</span></a></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="insertColRight"><span>'+this._t("insertColumnRight")+' ( Ctrl + Alt + → )</span></a></li>\n    <li><span class="separator"></span></li>\n    <li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;" data-param="deleteTable"><span>'+this._t("deleteTable")+"</span></a></li>\n  </ul>\n</div>").appendTo(this.menuWrapper),this.createMenu=this.menuWrapper.find(".menu-create-table"),this.editMenu=this.menuWrapper.find(".menu-edit-table"),this.createTable(6,6).appendTo(this.createMenu),this.createMenu.on("mouseenter","td",function(b){return function(c){var d,e,f;return b.createMenu.find("td").removeClass("selected"),d=a(c.currentTarget),e=d.parent(),f=e.find("td").index(d)+1,e.prevAll("tr").addBack().find("td:lt("+f+")").addClass("selected")}}(this)),this.createMenu.on("mouseleave",function(){return function(b){return a(b.currentTarget).find("td").removeClass("selected")}}(this)),this.createMenu.on("mousedown","td",function(b){return function(c){var d,e,f,g,h,i;return b.wrapper.removeClass("menu-on"),b.editor.inputManager.focused?(f=a(c.currentTarget),g=f.parent(),h=g.find("td").index(f)+1,i=g.prevAll("tr").length+1,e=b.createTable(i,h,!0),d=b.editor.util.closestBlockEl(),b.editor.util.isEmptyNode(d)?d.replaceWith(e):d.after(e),b.decorate(e),b.editor.selection.setRangeAtStartOf(e.find("td:first")),b.editor.trigger("valuechanged"),!1):void 0}}(this))},c.prototype.createTable=function(b,c,d){var e,f,g,h,i,j,k,l;for(e=a("<table/>"),f=a("<tbody/>").appendTo(e),j=k=0;b>=0?b>k:k>b;j=b>=0?++k:--k)for(h=a("<tr/>").appendTo(f),i=l=0;c>=0?c>l:l>c;i=c>=0?++l:--l)g=a("<td/>").appendTo(h),d&&g.append(this.editor.util.phBr);return e},c.prototype.refreshTableWidth=function(b){var c,d;return d=b.width(),c=b.find("col"),b.find("tr:first td").each(function(){return function(b,e){var f;return f=c.eq(b),f.attr("width",a(e).outerWidth()/d*100+"%")}}(this))},c.prototype.setActive=function(a){return c.__super__.setActive.call(this,a),a?(this.createMenu.hide(),this.editMenu.show()):(this.createMenu.show(),this.editMenu.hide())},c.prototype.deleteRow=function(a){var b,c,d;return c=a.parent("tr"),c.siblings("tr").length<1?this.deleteTable(a):(b=c.next("tr"),b.length>0||(b=c.prev("tr")),d=c.find("td").index(a),c.remove(),this.editor.selection.setRangeAtEndOf(b.find("td").eq(d)))},c.prototype.insertRow=function(b,c){var d,e,f,g,h,i,j;for(null==c&&(c="after"),f=b.parent("tr"),e=f.closest("table"),g=0,e.find("tr").each(function(){return function(b,c){return g=Math.max(g,a(c).find("td").length)}}(this)),d=a("<tr/>"),h=j=1;g>=1?g>=j:j>=g;h=g>=1?++j:--j)a("<td/>").append(this.editor.util.phBr).appendTo(d);return f[c](d),i=f.find("td").index(b),this.editor.selection.setRangeAtStartOf(d.find("td").eq(i))},c.prototype.deleteCol=function(b){var c,d,e,f;return e=b.parent("tr"),e.siblings("tr").length<1&&b.siblings("td").length<1?this.deleteTable(b):(f=e.find("td").index(b),c=b.next("td"),c.length>0||(c=e.prev("td")),d=e.closest("table"),d.find("col").eq(f).remove(),d.find("tr").each(function(){return function(b,c){return a(c).find("td").eq(f).remove()}}(this)),this.refreshTableWidth(d),this.editor.selection.setRangeAtEndOf(c))},c.prototype.insertCol=function(b,c){var d,e,f,g,h,i,j,k;return null==c&&(c="after"),h=b.parent("tr"),i=h.find("td").index(b),g=b.closest("table"),d=g.find("col").eq(i),g.find("tr").each(function(b){return function(d,e){var f;return f=a("<td/>").append(b.editor.util.phBr),a(e).find("td").eq(i)[c](f)}}(this)),e=a("<col/>"),d[c](e),j=g.width(),k=Math.max(parseFloat(d.attr("width"))/2,50/j*100),d.attr("width",k+"%"),e.attr("width",k+"%"),this.refreshTableWidth(g),f="after"===c?b.next("td"):b.prev("td"),this.editor.selection.setRangeAtStartOf(f)},c.prototype.deleteTable=function(a){var b,c;return c=a.closest(".simditor-table"),b=c.next("p"),c.remove(),b.length>0?this.editor.selection.setRangeAtStartOf(b):void 0},c.prototype.command=function(b){var c,d;if(d=this.editor.selection.getRange(),c=a(d.commonAncestorContainer).closest("td"),c.length>0){if("deleteRow"===b)this.deleteRow(c);else if("insertRowAbove"===b)this.insertRow(c,"before");else if("insertRowBelow"===b)this.insertRow(c);else if("deleteCol"===b)this.deleteCol(c);else if("insertColLeft"===b)this.insertCol(c,"before");else if("insertColRight"===b)this.insertCol(c);else{if("deleteTable"!==b)return;this.deleteTable(c)}return this.editor.trigger("valuechanged")}},c}(p),o.Toolbar.addButton(K);var L,f={}.hasOwnProperty,g=function(a,b){function c(){this.constructor=a}for(var d in b)f.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a};return L=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return g(c,b),c.prototype.name="strikethrough",c.prototype.icon="strikethrough",c.prototype.htmlTag="strike",c.prototype.disableTag="pre",c.prototype.status=function(a){var b;return null!=a&&this.setDisabled(a.is(this.disableTag)),this.disabled?!0:(b=document.queryCommandState("strikethrough")===!0,this.setActive(b),b)},c.prototype.command=function(){return document.execCommand("strikethrough"),this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(p),o.Toolbar.addButton(L),o});
