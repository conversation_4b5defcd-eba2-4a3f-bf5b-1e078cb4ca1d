<?php
namespace Admin\Common;
class CommonUtil {
    public static function getMenuids($roleid){
        $rolemenu=M('rolemenu');
        $result=$rolemenu->where("roleid=%d",array($roleid))->field('menuid')->select();
        if(empty($result)){
            return null;
        }
        $menuids=array();
        for($i=0;$i<count($result);$i++){
            $menuids[$i]=$result[$i]['menuid'];
        }
        return $menuids;
    }
}