<?php
namespace Admin\Common;
use Think\Controller;
class CommonController extends Controller {
    const server_url = "http://47.100.254.34:28080/fdddev/api/fdd/";

    public function checkLogin(){
        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');
        if(empty($userid)||empty($account)||empty($roleid)){
            \Think\Log::record("没有登录，立即退出 ".$account,"INFO");
            $this->logout();
            return;
        }
    }

    public function logout(){
        session('userid',null);
        session('account',null);
        session('roleid',null);
        $this->redirect('Login/index');
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_data($datas) {
        $data = array();
        $data['retCode'] = '0';
        $data['retMsg'] = 'success';
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_error($datas,$retCode,$retMsg) {
        $data = array();
        $data['retCode'] = $retCode;
        $data['retMsg'] = $retMsg;
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_commonerror($datas) {
        $data = array();
        $data['retCode'] = '1';
        $data['retMsg'] = '错误';
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    function getPrivilege($menuid){
        $roleid=session("roleid");
        if(!empty($roleid)){
            $rolemenu=M('rolemenu');
            $rolemenuinfo=$rolemenu->where("roleid=".$roleid." and menuid=".$menuid)->find();
            if(!empty($rolemenuinfo)){
                return $rolemenuinfo['privilege'];
//                return 0;
            }
        }
        return 0;
    }
}