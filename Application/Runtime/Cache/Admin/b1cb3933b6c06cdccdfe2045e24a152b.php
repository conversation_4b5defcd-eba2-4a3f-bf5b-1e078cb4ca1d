<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/szrecycle_mgr/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/szrecycle_mgr/Public/css/animate.css" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>广告文件列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                     role="group">
                                    <?php if(($add_able) == "1"): ?><button id="testBtn" type="button"
                                                class="btn btn-outline btn-default"
                                                onclick="script:window.location.href = '/szrecycle_mgr/index.php/Admin/AdsMgr/add'">
                                            <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                        </button><?php endif; ?>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                </div>
                                <table id="tb_bootstraptblist">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="downloadModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">文件下载</h4>
                </div>
                <div class="modal-body">
                    <div style="text-align: center;">
                        <a id='downloadurl' style="font-size: 16px;" href=""><u>下载地址</u></a>
                        <div style="color: #999999;margin-top: 20px;">说明：请点击直接打开或者右击目标另存为</div>
                    </div>
                </div>
                <div class="modal-footer" style="display: none">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="downloadModal_commit" type="button" class="btn btn-primary">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="/szrecycle_mgr/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/szrecycle_mgr/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/szrecycle_mgr/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/szrecycle_mgr/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_bootstraptblist').bootstrapTable({
                    url: '/szrecycle_mgr/index.php/Admin/AdsMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: 'ID'
                    }, {
                        field: 'filename',
                        title: '文件名'
                    }, {
                        field: 'typename',
                        title: '类型'
                    }, {
                        field: 'starttime',
                        title: '开始时间'
                    }, {
                        field: 'endtime',
                        title: '结束时间'
                    }, {
                        field: 'statusname',
                        title: '状态'
                    }, {
                        field: 'createusername',
                        title: '创建人'
                    },
                    //     {
                    //     field: 'operatorname',
                    //     title: '运营商'
                    // },
                        //     {
                    //     field: 'starttime',
                    //     title: '起始时间'
                    // }, {
                    //     field: 'endtime',
                    //     title: '结束时间'
                    // },
                        {
                        field: 'createtime',
                        title: '创建时间'
                    }
                    ,{
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }
                    ],
                    onLoadSuccess:function(){
                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "/szrecycle_mgr/index.php/Admin/AdsFiletaskMgr/index?id="+this.previousSibling.innerText;
                            });
                        }

                        var contentCheckBtns = $("button[name='contentCheckBtn']");
                        for(var i=0;i<contentCheckBtns.length;i++){
                            contentCheckBtns[i].addEventListener("click",function(){
                                var r=confirm("确认审批通过?")
                                if (r!=true){
                                    return;
                                }
                                // window.location.href = "/szrecycle_mgr/index.php/Admin/AdsFiletaskMgr/index?id="+this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                var req={
                                    id:id,
                                    status:1
                                }
                                postjson('/szrecycle_mgr/index.php/Admin/AdsMgr/modstatus',req,function (data) {
                                    toastr.success("处理成功！");
                                    $('#tb_bootstraptblist').bootstrapTable('refresh');
                                })
                            });
                        }

                        var financeCheckBtns = $("button[name='financeCheckBtn']");
                        for(var i=0;i<financeCheckBtns.length;i++){
                            financeCheckBtns[i].addEventListener("click",function(){
                                var r=confirm("确认审批通过?")
                                if (r!=true){
                                    return;
                                }
                                // window.location.href = "/szrecycle_mgr/index.php/Admin/AdsFiletaskMgr/index?id="+this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                var req={
                                    id:id,
                                    status:2
                                }
                                postjson('/szrecycle_mgr/index.php/Admin/AdsMgr/modstatus',req,function (data) {
                                    toastr.success("处理成功！");
                                    $('#tb_bootstraptblist').bootstrapTable('refresh');
                                })
                            });
                        }

                        var financeRejectBtns = $("button[name='financeRejectBtn']");
                        for(var i=0;i<financeRejectBtns.length;i++){
                            financeRejectBtns[i].addEventListener("click",function(){
                                var r=confirm("确认驳回?")
                                if (r!=true){
                                    return;
                                }
                                // window.location.href = "/szrecycle_mgr/index.php/Admin/AdsFiletaskMgr/index?id="+this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                var req={
                                    id:id,
                                    status:0
                                }
                                postjson('/szrecycle_mgr/index.php/Admin/AdsMgr/modstatus',req,function (data) {
                                    toastr.success("处理成功！");
                                    $('#tb_bootstraptblist').bootstrapTable('refresh');
                                })
                            });
                        }

                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                // window.location.href = this.previousSibling.innerText;
                                // var id=this.nextSibling.innerText;
                                $('#downloadurl').attr('href',this.previousSibling.innerText);
                                $('#downloadModal').modal();
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // name: params.search,
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:240px;text-align:left;">';
            html += '<div style="display:none;">'+row['url']+'</div>';
            html += '<button name="detailBtn" type="button" class="btn btn-white btn-sm" href="#" style="">下载</button>';
            html += '<div style="display:none;">'+row['id']+'</div>';
            if(<?php echo (session('roleid')); ?>==1){
                html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">推送状态</button>';

                if(row['status']==0){
                    html += '<button name="contentCheckBtn" type="button" class="btn btn-info btn-sm" href="#" style="margin-left:3px;">内容审批通过</button>';
                    html += '<div style="display:none;">'+row['id']+'</div>';
                }

            }else if(<?php echo (session('roleid')); ?>==4&&row['status']==1){
                html += '<button name="financeCheckBtn" type="button" class="btn btn-info btn-sm" href="#" style="margin-left:3px;">财务审批通过</button>';
                html += '<div style="display:none;">'+row['id']+'</div>';
                html += '<button name="financeRejectBtn" type="button" class="btn btn-danger btn-sm" href="#" style="margin-left:3px;">驳回</button>';
                html += '<div style="display:none;">'+row['id']+'</div>';
            }else if(<?php echo (session('roleid')); ?>==5){

            }

            html +='</div>';
            return html;
        }

        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }

                    var selectIds = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var ids = new Array();
                    for(var i=0;i<selectIds.length;i++){
                        ids.push(selectIds[i].id);
                    }
                    var deleteReq = {
                        idlist:ids
                    }

                    $.ajax({
                        url:'/szrecycle_mgr/index.php/Admin/AdsMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(deleteReq),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！"+data.data);
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_bootstraptblist').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });
            };

            return oInit;
        };
    </script>
</body>

</html>