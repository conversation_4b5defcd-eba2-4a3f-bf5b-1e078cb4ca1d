<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>用户列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_listEventsToolbar"
                                     role="group">
                                    <button id="testBtn" type="button" style="display: none"
                                            class="btn btn-outline btn-default"
                                            onclick="script:window.location.href = '/index.php/Admin/RegisterMgr/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="script:window.location.href = '/index.php/Admin/RegisterMgr/batchadd'"
                                            style="margin-left: 5px;display: none">批量导入</button>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <?php if(($_SESSION['roleid']) != "6"): if(($privilege) == "1"): ?><button id="delete" type="button" style="margin-left: 5px;"
                                                    class="btn btn-outline btn-default">
                                                <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                            </button><?php endif; endif; ?>

                                    <input id="search_account" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="账号">

                                    <?php if(($search_visible) == "1"): ?><button id="btn_search" type="button"
                                            class="btn btn-primary" style="margin-left: 3px;">搜索</button><?php endif; ?>

                                    <?php if(($_SESSION['roleid']) != "6"): if(($cansetrole) == "1"): ?><button id="setRoleBtn" class="btn btn-white" type="button"
                                                style="margin-left: 3px;">设置角色</button><?php endif; ?>
                                        <button id="addViolationBtn" class="btn btn-danger" type="button"
                                                style="margin-left: 3px;display:none;">添加违规</button>
                                        <button id="setDishonestflagBtn" class="btn btn-success" type="button"
                                                style="margin-left: 3px;">设为失信人</button>
                                        <button id="clearDishonestflagBtn" class="btn btn-default" type="button"
                                                style="margin-left: 3px;">取消失信人</button><?php endif; ?>
                                </div>
                                <table id="tb_list">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="addViolationModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">添加违规记录</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">违规描述：</label>
                            <div class="col-sm-10">
                                <textarea rows="5" id="addViolation_remark" type="text" class="form-control"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">计分：</label>
                            <div class="col-sm-10">
                                <input id="addViolation_point" type="text" class="form-control">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="addViolation_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="setRoleModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">设置角色</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">角色：</label>
                            <div class="col-sm-10">
                                <select id="setRoleModal_roleid" class="form-control m-b" >
                                    <?php if(is_array($rolelist)): $i = 0; $__LIST__ = $rolelist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["value"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="setRoleModal_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/Public/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_list').bootstrapTable({
                    url: '/index.php/Admin/RegisterMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: '用户ID'
                    },{
                        field: 'account',
                        title: '账号'
                    }, {
                        field: 'dishonestflagname',
                        title: '是否失信'
                    },
                    <?php if(($cansetrole) == "1"): ?>{
                        field: 'rolename',
                        title: '角色'
                    },<?php endif; ?>
                        {
                        field: 'createtime',
                        title: '注册时间'
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                window.location.href = "/index.php/Admin/RegisterMgr/point?id="+this.nextSibling.innerText;
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "/index.php/Admin/RegisterMgr/update?id="+this.previousSibling.innerText;
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                    account :$('#search_account').val(),
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:100px;text-align:center;">';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="">积分</button>';
            html += '<div style="display:none;">'+row['id']+'</div>';
            if(<?php echo ($privilege); ?>==1&&<?php echo (session('roleid')); ?>!=6){
                html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">修改</button>';
            }
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:users
                    }

                    $.ajax({
                        url:'/index.php/Admin/RegisterMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#btn_search').on("click",function () {
                    var opt = {
                        query:{
                            offset: 0,
                        }
                    };
                    $('#tb_list').bootstrapTable('refresh',opt);
                })

                $('#addViolationBtn').on('click',function () {
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    if(selectDevs.length==0){
                        toastr.error("请选择用户！");
                        return;
                    }
                    $('#addViolationModal').modal();
                })
                $('#addViolation_commit').on('click',function () {
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:users,
                        remark:$('#addViolation_remark').val(),
                        point:$('#addViolation_point').val(),
                        usertype:0
                    }

                    $.ajax({
                        url:'/index.php/Admin/ViolationMgr/api_addViolation',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("操作失败！");
                                return;
                            }

                            toastr.success("操作成功！");
                            $('#addViolationModal').modal('hide');
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                })

                $('#setDishonestflagBtn').on('click',function () {
                    setDishonestflag(1);
                })

                $('#clearDishonestflagBtn').on('click',function () {
                    setDishonestflag(0);
                })
                
                $('#setRoleBtn').on('click',function () {
                    $('#setRoleModal').modal();
                })
                $('#setRoleModal_commit').on('click',function () {
                    var roleid=$('#setRoleModal_roleid').val();
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    if(selectDevs.length==0){
                        toastr.error("请选择用户！");
                        return;
                    }
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req={
                        roleid:roleid,
                        idlist:users
                    }
                    postjson('/index.php/Admin/RegisterMgr/api_setRole',req,function (data) {
                        toastr.success("操作成功！");
                        $('#tb_list').bootstrapTable('refresh');
                        $('#setRoleModal').modal('hide');
                        return;
                    })
                })
            };

            return oInit;
        };

        function setDishonestflag(flag) {
            var selectDevs = $('#tb_list').bootstrapTable('getSelections');
            if(selectDevs.length==0){
                toastr.error("请选择用户！");
                return;
            }
            var users = new Array();
            for(var i=0;i<selectDevs.length;i++){
                users.push(selectDevs[i].id);
            }
            var req = {
                idlist:users,
                flag:flag,
                usertype:0
            }

            $.ajax({
                url:'/index.php/Admin/ViolationMgr/api_setDishonestflag',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                beforeSend:function(xhr){
                },
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！");
                        return;
                    }

                    toastr.success("操作成功！");
                    $('#tb_list').bootstrapTable('refresh');
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        }
    </script>
</body>

</html>