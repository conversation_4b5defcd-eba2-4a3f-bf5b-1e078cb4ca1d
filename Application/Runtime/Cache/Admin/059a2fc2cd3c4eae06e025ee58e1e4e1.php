<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--360浏览器优先以webkit内核解析-->


    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico"> <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">

    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">

    <style>
        .echarts {
            height: 400px;
            width: 100%;
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInUp" style="padding-top: 20px;">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                    <div class="ibox-title"><h5>数据筛选</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar" role="group">
                                    <select id="search_rubbishtype" class="searchInput" style="margin-left: 3px;width: 90px;">
                                        <option value="">全部类型</option>
                                        <?php if(is_array($rubbishtypelist)): $i = 0; $__LIST__ = $rubbishtypelist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["rubbishtype"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                    </select>
                                    <select id="search_timespan" class="searchInput" style="margin-left: 3px;width: 90px;">
                                        <option value="year">按年</option>
                                        <option value="halfyear">按半年</option>
                                        <option value="month">按月</option>
                                        <option value="week">按周</option>
                                    </select>
                                    <input id="search_starttime" class="layer-date searchInput" style="margin-left: 3px;width: 116px;" placeholder="YYYY-MM-DD" onclick="laydate({istime: false, format: 'YYYY-MM-DD'})">
                                    <input id="search_endtime" class="layer-date searchInput" style="margin-left: 3px;width: 116px;" placeholder="YYYY-MM-DD" onclick="laydate({istime: false, format: 'YYYY-MM-DD'})">
                                    <?php if($search_operatorid_visible == 1): ?><select id="search_operatorid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="">全部运营商</option>
                                            <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>
                                    <?php if($search_propertyid_visible == 1): ?><select id="search_propertyid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="">请选择物业</option>
                                        </select><?php endif; ?>
                                    <button id="btn_search" type="button" class="btn btn-primary" style="margin-left: 3px;">搜索</button>
                                    <button id="btn_reset" type="button" class="btn btn-default" style="margin-left: 3px;">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title"><h5>垃圾投放数据统计 - 柱状图</h5></div>
                <div class="ibox-content">
                    <div id="bar-chart" class="echarts"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title"><h5>垃圾类型占比 - 饼图</h5></div>
                <div class="ibox-content">
                    <div id="pie-chart" class="echarts"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="/Public/js/jquery.min.js?v=2.1.4"></script>
<script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
<script src="/Public/js/plugins/layer/layer.min.js"></script>
<script src="/Public/js/plugins/layer/laydate/laydate.js"></script>
<script src="/Public/js/plugins/toastr/toastr.min.js"></script>

<!-- ECharts -->
<script src="/Public/js/plugins/echarts/echarts-all.js"></script>

<!-- 自定义js -->
<script src="/Public/js/content.js"></script>
<script src="/Public/js/common.js"></script>

<script>
    $(document).ready(function() {
        toastr.options = {"positionClass": "toast-top-center", "timeOut": "3000"};

        // 初始化日期
        initDate();

        // 初始化图表
        var barChart = echarts.init(document.getElementById('bar-chart'));
        var pieChart = echarts.init(document.getElementById('pie-chart'));

        // 加载初始数据
        loadChartData();

        // 搜索按钮点击事件
        $('#btn_search').on('click', function() {
            loadChartData();
        });

        // 重置按钮点击事件
        $('#btn_reset').on('click', function() {
            $('#search_rubbishtype').val('');
            $('#search_timespan').val('year');
            initDate();
            if($('#search_operatorid').length > 0) {
                $('#search_operatorid').val('');
            }
            if($('#search_propertyid').length > 0) {
                $('#search_propertyid').val('');
                $('#search_propertyid').html('<option value="">请选择物业</option>');
            }
            loadChartData();
        });

        // 时间跨度变化事件
        $('#search_timespan').on('change', function() {
            // 不自动触发请求，只在点击搜索按钮时发送请求
        });

        // 垃圾类型变化事件
        $('#search_rubbishtype').on('change', function() {
            // 不自动触发请求，只在点击搜索按钮时发送请求
        });

        // 运营商选择变化事件
        $('#search_operatorid').on('change', function() {
            var operatorid = $(this).val();
            $('#search_propertyid').html('<option value="">请选择物业</option>');

            if(operatorid) {
                getjson('/index.php/Admin/Homepage/api_getPropertys?operatorid='+operatorid, function(data) {
                    if(!data || data.length == 0) {
                        $('#search_propertyid').html('<option value="">请选择物业</option>');
                        return;
                    }
                    var html = '<option value="">请选择物业</option>';
                    for(var i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].userid + '">' + data[i].name + '</option>';
                    }
                    $('#search_propertyid').html(html);
                });
            }
        });

        // 窗口大小改变时，重新调整图表大小
        $(window).resize(function() {
            barChart.resize();
            pieChart.resize();
        });

        // 加载图表数据
        function loadChartData() {
            var params = {
                rubbishtype: $('#search_rubbishtype').val(),
                timespan: $('#search_timespan').val(),
                starttime: $('#search_starttime').val(),
                endtime: $('#search_endtime').val()
            };

            // 添加运营商参数
            if($('#search_operatorid').length > 0) {
                params.operatorid = $('#search_operatorid').val();
            }

            // 添加物业参数
            if($('#search_propertyid').length > 0) {
                params.propertyid = $('#search_propertyid').val();
            }

            $.ajax({
                url: '/index.php/Admin/Homepage/api_getlist',
                type: 'POST',
                data: JSON.stringify(params),
                dataType: 'json',
                success: function(data) {
                    updateBarChart(barChart, data);
                    updatePieChart(pieChart, data);
                },
                error: function() {
                    toastr.error('获取数据失败，请稍后再试！');
                }
            });
        }

        // 更新柱状图
        function updateBarChart(chart, data) {
            // 清空图表
            chart.clear();
            
            var option = {};
            
            if (!data || !data.xAxis || !data.series || !data.legend) {
                // 设置一个空的图表选项，确保清除之前的样式
                option = {
                    title: {
                        text: '垃圾投放数据统计',
                        subtext: '按' + $('#search_timespan').find("option:selected").text() + '统计'
                    },
                    tooltip: {
                        show: false
                    },
                    series: []
                };
                // 设置图表选项后再显示警告
                chart.setOption(option);
                toastr.warning('没有找到符合条件的数据！');
                return;
            } else {
                // 清除可能存在的警告提示
                toastr.clear();
                option = {
                    title: {
                        text: '垃圾投放数据统计',
                        subtext: '按' + $('#search_timespan').find("option:selected").text() + '统计'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: data.legend,
                        orient: 'horizontal',
                        x: 'center',
                        y: 'top',
                        padding: [30, 5, 5, 5]
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true},
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    calculable: true,
                    xAxis: [{
                        type: 'category',
                        data: data.xAxis,
                        axisLabel: {
                            interval: 0,
                            rotate: 30
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        name: '重量(克)'
                    }],
                    series: data.series
                };
            }

            // 设置新选项
            chart.setOption(option);
        }

        // 更新饼图
        function updatePieChart(chart, data) {
            // 清空图表
            chart.clear();
            
            var option = {};
            
            if (!data || !data.series || !data.legend) {
                // 设置一个空的图表选项，确保清除之前的样式
                option = {
                    title: {
                        text: '垃圾类型占比',
                        subtext: '按重量统计',
                        x: 'center'
                    },
                    tooltip: {
                        show: false
                    },
                    series: []
                };
                // 设置图表选项后再显示警告
                chart.setOption(option);
                toastr.warning('没有找到符合条件的数据！');
                return;
            } else {
                // 清除可能存在的警告提示
                toastr.clear();
                // 计算每种垃圾类型的总量
                var pieData = [];
                var totalData = {};

                // 初始化每种类型的总量为0
                for (var i = 0; i < data.legend.length; i++) {
                    totalData[data.legend[i]] = 0;
                }

                // 累加每种类型在各个时间段的数量
                for (var i = 0; i < data.series.length; i++) {
                    var series = data.series[i];
                    var sum = 0;
                    for (var j = 0; j < series.data.length; j++) {
                        sum += series.data[j];
                    }
                    totalData[series.name] = sum;
                }

                // 转换为饼图所需的数据格式
                for (var key in totalData) {
                    pieData.push({
                        name: key,
                        value: totalData[key]
                    });
                }

                option = {
                    title: {
                        text: '垃圾类型占比',
                        subtext: '按重量统计',
                        x: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: "{a} <br/>{b} : {c}克 ({d}%)"
                    },
                    legend: {
                        orient: 'vertical',
                        x: 'left',
                        data: data.legend
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true},
                            dataView: {show: true, readOnly: false},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    calculable: true,
                    series: [{
                        name: '垃圾类型',
                        type: 'pie',
                        radius: '55%',
                        center: ['50%', '60%'],
                        data: pieData
                    }]
                };
            }

            // 设置新选项
            chart.setOption(option);
        }
    });

    // 初始化日期
    function initDate() {
        var now = new Date();
        var lastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        $('#search_starttime').val(formatDate(lastYear));
        $('#search_endtime').val(formatDate(now));
    }

    // 格式化日期为 YYYY-MM-DD
    function formatDate(date) {
        var year = date.getFullYear();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var day = date.getDate().toString().padStart(2, '0');
        return year + '-' + month + '-' + day;
    }
</script>

</body>

</html>