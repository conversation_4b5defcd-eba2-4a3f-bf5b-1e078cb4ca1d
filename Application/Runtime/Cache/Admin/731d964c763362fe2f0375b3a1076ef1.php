<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">

    <title>积富回收</title>

    <meta name="keywords" content="平台">
    <meta name="description" content="平台">

    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <link rel="shortcut icon" href="/Public/img/favicon.png">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.min.css?v=4.4.0" rel="stylesheet">
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5.0.1/dist/echarts.min.js"></script>
    <!--<link href="css/style.css?v=4.1.0" rel="stylesheet">-->
    <!--<link rel="stylesheet" href="css/my.css">-->
    <style>
        .fl{
            float:left;
            font-size:19px;
        }
        .fr{
            float:right;
            font-size:15px;
        }
        .clearfix:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
            font-size:15px;
        }
        .clearfix_my:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
        }
        .anchorBL{

            display:none;

        }
        /* wraper1 用来控制宽度 */
        .wraper1{
            width: 100%;
        }
        /* wraper2 用来实现宽高等比例 */
        .wraper2{
            width: 100%;
            /*padding-bottom: 56.25%;*/
            padding-bottom: 54%;
            height: 0;
            position: relative;
        }
        /* wraper3 用来放置全部的子元素 */
        .wraper3{
            width: 100%;
            height: 100%;
            position: absolute;
        }
        .header{
            height: 12%;
            text-align: center;
            /*background: url("/Public/img/topbag.png") no-repeat;
            background-size:100% 100%;*/
        }
        .header > .fl {
            width: 33%;
        }
        .content1{
            text-align: center;
            height: 92%;
        }
        .content1 > .fl {

        }
        .module-wraper{
            height: 32%;
            width: 100%;
            padding: 0;
            float: left;
        }
        .module-wraper1{
            height: 100%;
            width: 100%;
            padding: 0;
            float: left;
        }
        .module{
            float: left;
            margin: 0 4%;
            height: 100%;
            width: 92%;
            border:1px solid #00a4ff;
            box-shadow: inset 0px 0px 15px #00ADFE;
            border-radius:5px;
           
            /*background: url("/Public/img/bgk.png") no-repeat;
            background-size: 100% 100%;*/
            /*border: solid 1px #efefef;*/
        }
        .moduletz{
            position:absolute;
            top:10%;
            right:0%;
            width:25%;
            height: 40%;
            border:1px solid #00a4ff;
            box-shadow: inset 0px 0px 15px #00ADFE;
            border-radius:5px;
           
            /*background: url("/Public/img/bgk.png") no-repeat;
            background-size: 100% 100%;*/
            /*border: solid 1px #efefef;*/
        }
        .modulex{
            margin: 0 4%;
            height: 100%;
            width: 92%;
            position:absolute;
            border:1px solid #00a4ff;
            box-shadow: inset 0px 0px 15px #00ADFE;
            border-radius:5px;
        }
        .space{
            float: left;
            height: 1.8%;
            width: 100%;
        }
        .space1{
            float: left;
            height: 2.2%;
            width: 100%;
        }
        .module-content{
            padding: 16px 18px;
            font-size: 9px;
            height: 100%;
        }
        .module-title{
            text-align: left;
            font-size: 14px;
            color: #c0e7fe;
            font-weight: bold;
        }
        .modulex-title{
            font-size: 16px;
            color: #c0e7fe;
            font-weight: bold; 
            width:100%;
            height: 35px;
            line-height:15%;
            background: url("/Public/img/titlebk.png") no-repeat;
            background-size: 100% 100%;
            color:#00EEF8;
        }
        .icon1{
            width: 24px;
            height: 24px;
        }
        .icon2{
            width: 14px;
            height: 14px;
            margin: 0 4px 0 0;
        }
        .wraper1001{
            margin:6px 0 14px 0;
            color: #c0e7fe;
        }
        .icon11{
            display: inline-block;
            width: 4px;
            height: 14px;
            line-height: 14px;
            background-color: #0fffb8;
            margin: 0 4px 0 4px;
        }
        .icon12{
            display: inline-block;
            width: 6px;
            height: 6px;
            line-height: 6px;
            border-radius: 6px;
            background-color: #0fffb8;
            margin: 0 4px 0 4px;
        }
        .text111{
            width: 45%;
            color: #c0e7fe;
            text-align: left;
        }
        .text112{
            width: 40%;
            color: #0fffb8;
            text-align: right;
        }
        .text113{
            width: 40%;
            color: #c0e7fe;
            text-align: right;
        }
        .text114{
            width: 15%;
            color: #c0e7fe;
            text-align: left;
        }
        .wraper1003{
            margin: 6px 0;
            font-size: 10px;
        }
        .wraper1004{
            margin: 6px 0;
            font-size: 10px;
        }
        .btn11{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.7;width: 150px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn11x{
            color: #ffffff;
            cursor: pointer;
            position: absolute;opacity:0.7;width: 12%;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            background: url("/Public/img/bgk.png") no-repeat;
            height:9%;
        }
        .btnxxx{
            cursor: pointer;
            position: absolute;
            opacity:0.7;
            width: 30%;
            padding: 5px;
            border-radius: 2px;
            height:9%;
            font-size:26px;
            font-weight:bold;
            line-height: 45px;
            color:#BEEEFF;
        }
        .newbtn11{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.7;width: 25%;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            height:25%;
            top:75%;
            background: url("/Public/img/bgk.png") no-repeat;
            background-size: 100% 100%;
        }
        .btn12{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.8;width: 100px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn13{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #268ed4;opacity:0.9;width: 240px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn14{
            color: #ffffff;
            position: absolute;background-color: #268ed4;opacity:0.6;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            padding-left: 6px;
            padding-right: 6px;
        }

        ::-webkit-scrollbar-track {
            background-color: #F5F5F5;
        }

        ::-webkit-scrollbar {
            width: 4px;
            background-color: #F5F5F5;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #999;
        }

        .myscroll{
            overflow-y: scroll;
            height: 70%;
            padding: 0 6px;
            color:#FFFFFF;
            font-size:15px;
        }
        .myscroll::-webkit-scrollbar{ //设置整个滚动条宽高
            background-color: #F5F5F5;
            width: 0!important;
        }
        .myscroll::-webkit-scrollbar-thumb{ //设置滑块
            background: rgba(0,0,0,0.0)!important;
        }
        .myscroll::-webkit-scrollbar-track
        {
            background: rgba(0,0,0,0.0)!important;
        }
        body{
            background: url("/Public/img/bag.png") no-repeat;
            background-size:100% 120%;
        }
        .litr{
            border:1px solid #00a4ff;
            box-shadow: inset 0px 0px 15px #00ADFE;
            border-radius:5px;
            width:90%;
            float:left;
            margin-left:5%;
            height:9%;
            margin-top:1%;
            font-size:19px;

        }
        .litd{
            width:25%;
            height: 100%;
            float:left;
            text-align:center;
            color:white;
            font-size:19px;
        }
        .tdwcolor{
            color:#C1331E;
        }
        .tjwraper1{
            height:100%;
            width:44%;
            float:left;
            margin-left:4%;
            border:1px solid #00a4ff;
            box-shadow: inset 0px 0px 15px #00ADFE;
            border-radius:5px;
        }
        .zjq{
            height:20%;
            width:100%;
            text-align:center;
            color:#49FF00;
            font-weight: bold;
            font-size:20px;
        }
        .zjqli{
            height:90%;
            width:30%;
            text-align:center;
            float:left;
            margin-left: 2%;
            color:white;
            font-size: 14px;
        }
        .tjwraper2{
            height:100%;
            width:44%;
            float:left;
            margin-left:4%;
            border:1px solid #00a4ff;
            border-radius:5px;
            box-shadow: inset 0px 0px 15px #00ADFE;
        }
        .ymleft{
            width:25%;
            height:90%;
            float:left;
            text-align:center;
            color:white;
        }
        .ymcenter{
            width:50%;
            float:left;
            height:90%;
            text-align: center;
        }
        #demo{
          overflow:hidden; /*溢出的部分不显示*/
          
          height:90%;/*一定要确切，避免demo1与demo2之间的距离过大*/
          padding:5px;
          margin:50px auto;
        }
    </style>
</head>
<body style="height: auto;min-height:120%">
<div class="wraper1">
    <div class="wraper2">
        <img src="/Public/img/pig.png" style=" position: absolute;left:8%;top:20%;z-index:10">
        <img src="/Public/img/pig.png" style=" position: absolute;right:8%;top:80%;z-index:10">
        <div class="wraper3">
            <div class="header clearfix">
                <div class="fl" style="width: 30%;text-align: left;">
                    <div id="" style="font-size: 12px;margin:10px 0 0 15px;display: inline-block;"></div>
                </div>
                <div class="fl" style="width: 40%;padding-top:1%;font-size: 26px;font-weight: bold;color:#85C7DA"></div>
                <div class="fl" style="width: 30%;text-align: right;height: 30px;line-height: 30px;padding: 5px 15px 0 0;">
                    <div id="goto_mgr" style="display: inline-block;width: 100px;">
                        <!-- <div style="display: inline-block"><img src="/Public/img/mgr.png" style="height: 24px;cursor: pointer;"></div>
                        <div style="display: inline-block;font-size: 12px;line-height: 30px;height: 30px;cursor: pointer;">管理</div> -->
                    </div>
                    <?php if(($backurlflag) == "1"): ?><div id=""  style="display: inline-block;width: 100px;">
                        <button id="goback" onclick="javascript:window.location.href='<?php echo ($backurl); ?>'" class="btn btn-warning btn-sm" style="width: 90px;" type="button">返回</button>
                    </div><?php endif; ?>
                </div>
            </div>
            <div class="content1 clearfix">
                <div class="fl" style="width: 25%;height: 100%;">
                   <!--  <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">投递统计</div>
                                <div id="canyurenshu" style="width: 90%;height: 80%;color:white"></div>
                            </div>
                        </div>
                    </div> -->
                    <div class="module-wraper" style="height:30%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">投递统计</div>
                                <div class="litr">
                                    <div class="litd">类型</div>
                                    <div class="litd">累计总量</div>
                                    <div class="litd">昨日</div>
                                    <div class="litd">今日</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">纸类</div>
                                    <div class="litd"><span id="zhilei_zongliang" class="tdwcolor"><?php echo ($info["zhilei_zongliang"]); ?></span>kg</div>
                                    <div class="litd"><span id="zhilei_zuori" class="tdwcolor"><?php echo ($info["zhilei_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="zhilei_jinri" class="tdwcolor"><?php echo ($info["zhilei_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">塑料类</div>
                                    <div class="litd"><span id="suliao_zongliang" class="tdwcolor"><?php echo ($info["suliao_zongliang"]); ?></span>kg</div>
                                   <div class="litd"><span id="suliao_zuori" class="tdwcolor"><?php echo ($info["suliao_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="suliao_jinri" class="tdwcolor"><?php echo ($info["suliao_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">金属类</div>
                                    <div class="litd"><span id="jinshu_zongliang" class="tdwcolor"><?php echo ($info["jinshu_zongliang"]); ?></span>kg</div>
                                    <div class="litd"><span id="jinshu_zuori" class="tdwcolor"><?php echo ($info["jinshu_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="jinshu_jinri" class="tdwcolor"><?php echo ($info["jinshu_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">纺织类</div>
                                    <div class="litd"><span id="fangzhi_zongliang" class="tdwcolor"><?php echo ($info["fangzhi_zongliang"]); ?></span>kg</div>
                                    <div class="litd"><span id="fangzhi_zuori" class="tdwcolor"><?php echo ($info["fangzhi_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="fangzhi_jinri" class="tdwcolor"><?php echo ($info["fangzhi_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">有害垃圾</div>
                                    <div class="litd"><span id="youdu_zongliang" class="tdwcolor"><?php echo ($info["youdu_zongliang"]); ?></span>kg</div>
                                    <div class="litd"><span id="youdu_zuori" class="tdwcolor"><?php echo ($info["youdu_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="youdu_jinri" class="tdwcolor"><?php echo ($info["youdu_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">其他可回收</div>
                                    <div class="litd"><span id="qita_zongliang" class="tdwcolor"><?php echo ($info["qita_zongliang"]); ?></span>kg</div>
                                    <div class="litd"><span id="qita_zuori" class="tdwcolor"><?php echo ($info["qita_zuori"]); ?></span>kg</div>
                                    <div class="litd"><span id="qita_jinri" class="tdwcolor"><?php echo ($info["qita_jinri"]); ?></span>kg</div>
                                </div>
                                <div class="litr">
                                    <div class="litd">瓶罐类</div>
                                    <div class="litd"><span id="pingguan_zongliang" class="tdwcolor"><?php echo ($info["pingguan_zongliang"]); ?></span>个</div>
                                    <div class="litd"><span id="pingguan_zuori" class="tdwcolor"><?php echo ($info["pingguan_zuori"]); ?></span>个</div>
                                    <div class="litd"><span id="pingguan_jinri" class="tdwcolor"><?php echo ($info["pingguan_jinri"]); ?></span>个</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <!-- <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>垃圾分类参与次数统计</div>
                                <div id="toufangcishu" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div> -->
                    <div class="module-wraper" style="height:20%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">近7天投递量</div>
                                <div id="zongliang7tian" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <!-- <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>全国垃圾分类社区违法统计</div>
                                <div class='clearfix wraper1003'><div class='fl text114'>排名</div><div class='fl text111'>小区</div><div class='fr text113'>违法次数</div></div>
                                <div id="shequweifatongji" class="myscroll"></div>
                            </div>
                        </div>
                    </div> -->
                    <div class="module-wraper" style="height:20%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">近7天投递次数(次)</div>
                                <div id="toufangcishu7tian" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper" style="height:24%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">回收机使用人数分布</div>
                                <div id="hsjcover" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fl" style="width: 50%;height: 100%;">
                    <div style="width: 100%;height: 65.5%;position: relative">
                        <div style="width:82%;height:90%;left:18%;top:10%" id="map">
                        </div>
                        <!--  -->
                        <div class="moduletz" >
                            <div class="module-content">
                                <div class="modulex-title">品类分布</div>
                                <!-- <div class="module-title"><div class="icon11">&nbsp;</div>各类垃圾总量实况</div> -->
                                <div class="clearfix" style="margin: 20px 0;font-size: 14px;height:100%;">
                                    <div class="fl" style="display:none;width: 50%;height:100%;color: #ffffff;">
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #0fffb8;">&nbsp;</div>厨余垃圾</div>
                                            <div class="fr" class="text112">1456</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #03ace9;">&nbsp;</div>塑料类</div>
                                            <div class="fr" class="text112">654</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #4659f0;">&nbsp;</div>纸张类</div>
                                            <div class="fr" class="text112">465</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #ac39c5;">&nbsp;</div>金属类</div>
                                            <div class="fr" class="text112">156</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #c53964;">&nbsp;</div>有害类</div>
                                            <div class="fr" class="text112">45</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #cab556;">&nbsp;</div>织物类</div>
                                            <div class="fr" class="text112">33</div>
                                        </div>
                                    </div>
                                    <div class="fl" style="width: 100%;height:100%;color: #0fffb8;">
                                        <div id="plfbtj" style="width: 100%;height: 85%;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--  -->
                        
                        <div class="modulex" style="height:27%;width:40%;left:14%;margin-top:8%;position:absolute;">
                            <div class="module-content">
                                <div class="modulex-title">用户占比</div>
                                <div  class="myscroll">
                                    <div style="width:50%;height:100%;float:left">
                                        <div style="height:60%;width:100%;text-align:center;"><img src="/Public/img/xcx.png" style="height:100%"></div>
                                        <div style="height:20%;width:100%;text-align:center;">小程序用户</div>
                                        <div style="height:20%;width:100%;text-align:center;"><span id="totalwpuser"><?php echo ($info["totalwpuser"]); ?></span>人</div>
                                    </div>
                                    <div style="width:50%;height:100%;float:left">
                                        <div style="height:60%;width:100%;text-align:center;"><img src="/Public/img/jumin.png" style="height:100%"></div>
                                         <div style="height:20%;width:100%;text-align:center">居民卡用户</div>
                                        <div style="height:20%;width:100%;text-align:center"><span id="totalcarduser"><?php echo ($info["totalcarduser"]); ?></span>人</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modulex" style="height:27%;width:41%;left:55%;margin-top:8%;position:absolute;">
                            <div class="module-content">
                                <div class="modulex-title">性别分布</div>
                                <div  class="myscroll" >
                                    <div style="width:40%;height:100%;float:left;margin-left: 10%;">
                                        <div style="width:50%;float:left;height:80%;margin-top:15%;">
                                            <img src="/Public/img/nv.png" style="width:100%;">
                                        </div>
                                        <div style="width:50%;height:80%;margin-top:15%;float:left;">
                                            <div style="height:40%;width:100%;text-align:left">53.38%</div>
                                            <div style="height:40%;width:100%;text-align:left">女性</div>
                                        </div>
                                    </div>
                                    <div style="width:40%;height:100%;float:left;margin-left: 5%">
                                        <div style="width:50%;float:left;height:80%;margin-top:15%;">
                                            <img src="/Public/img/nan.png" style="width:70%;">
                                        </div>
                                        <div style="width:50%;height:80%;margin-top:15%;float:left;">
                                            <div style="height:40%;width:100%;text-align:left">46.62%</div>
                                            <div style="height:40%;width:100%;text-align:left">男性</div>
                                        </div>
                                    </div>
                                   <!--  <div style="width:50%;height:100%;float:left">
                                         <div style="height:40%;width:100%;margin-top:10%;text-align:left">6.73%</div>
                                        <div style="height:40%;width:100%;margin-top:5%;text-align:left">男性</div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <!--  -->
                        <!-- <div class="btn11x" style="top:10px;">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">用户总数（人）</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">652</div>
                        </div>
                        <div class="btn11x" style="top:10px;left: 13%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">参与人数（人）</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">200</div>
                        </div>
                        <div class="btn11x" style="top:10px;left: 26%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">参与率</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">30%</div>
                        </div>
                         <div class="btn11x" style="top:10px;left: 39%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">正确率</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">98%</div>
                         </div> -->
                         <div class="btnxxx" id="currenttime"  style="top:10px;width:100%;text-align:center">2020年12月20号</div>
                        <!-- <div class="btn11x" style="top:10px;left: 3%"></div>
                        <div class="btn11x" style="top:10px;left: 3%"></div> -->
                        <!-- <div class="btn11" style="top:10px;left: 10px;">设备总数：<span id="totaldev"></span></div>
                        <div class="btn11" style="top:10px;left: 150px;">在线设备：<span id="onlinedev"></span></div> -->
                        <!-- <div class="newbtn11">
                            <div style="width:94%;float:left;margin-left:3%;height:20%;line-height:25px;text-align:left">设备详情</div>
                            
                            <div style="width:94%;float:left;margin-left:3%;height:45%;line-height:55px;font-size:18px;font-weight:bold">设备总数：<span id="totaldev"></span></div>
                            
                            <div style="width:47%;float:left;margin-left:3%;height:29%;font-size:15px;">
                                <div style="width:100%;height:50%;text-align: center;"><span id="onlinedev"></span></div>
                                <div style="width:100%;height:50%;text-align: center;">在线设备</div>
                            </div>
                            <div style="width:47%;float:left;margin-left:3%;height:29%;font-size:15px;">
                                <div style="width:100%;height:50%;text-align: center;">125台</div>
                                <div style="width:100%;height:50%;text-align: center;">离线设备</div>
                            </div>
                        </div> -->

                        <div id="markerinfo" class="btn13" style="top:50px;left: 10px;display: none;">
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">设备名称:</div><div id="devname" class="fr" style="width: 65%;text-align: right;"></div></div>
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">设备编码:</div><div id="devcode" class="fr" style="width: 65%;text-align: right;"></div></div>
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">在线状态:</div><div id="onlinename" class="fr" style="width: 65%;text-align: right;"></div></div>
                        </div>
                    </div>
                    <!-- <div class="space1"></div> -->
                    <div style="width: 8%;height: 78%;position: absolute;top:12%">
                        <div class="module-wraper1 fl" style="width: 100%;">
                            <div class="module" style="margin: 0 2% 0 0;width: 98%;">
                                <div class="module-content" style="padding:0">
                                    <div class="modulex-title">实时投递记录</div>
                                    <!-- <div class="module-title" style="margin-bottom: 2px;"><div class="icon11">&nbsp;</div>实时投递记录</div> -->
                                    <!--<div class='clearfix wraper1003' style="color: #c0e7fe;padding: 0 7px;"><div class='fl' style="width: 40%;text-align: left">用户名</div><div class='fr' style="width: 60%;text-align: right">投递重量</div></div>-->
                                    <div id="demo" style="height:95%;margin-top:1%" >
                                        <div id="demo1">
                                            <div id="shixinren"  style="height: 90%;color:white;font-size:12px"></div>
                                            
                                        </div>
                                        <div id="demo2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="module-wraper1 fr" style="width: 50%;">
                            <div class="module" style="margin: 0 0 0 2%;width: 98%;">
                                <div class="module-content">
                                    <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>垃圾总量控制不达标社区</div>
                                    <div class='clearfix wraper1003' style="color: #c0e7fe;padding: 0 7px;"><div class='fl' style="width: 40%;text-align: left">小区</div><div class='fr' style="width: 60%;text-align: right">本月垃圾量</div></div>
                                    <div id="zongliangbudabiao" class="myscroll"></div>
                                </div>
                            </div>
                        </div> -->
                    </div>
                    <div class="space1"></div>
                    <div style="width: 100%;height: 31.5%;margin-top:21%">
                        <div class="module-wraper1 fl" style="width: 34%;">
                            <div class="module" style="margin: 0 2% 0 0;width: 98%;height: 40%">
                                <div class="module-content">
                                    <div class="module-title" style="margin-top:3%;font-size: 18px">覆盖住户数：<span id="totalfamily"></span></div>
                                    <div  class="myscroll" style="height: 90%;margin-top:10%;text-align:left;font-size: 16px">本月新增：<span id="totalfamily_monthadd"><?php echo ($info["totalfamily_monthadd"]); ?></span>户</div>
                                </div>
                            </div>
                        </div>
                        <div class="module-wraper1 fr" style="width: 33%;">
                            <div class="module" style="margin: 0 0 0 2%;width: 98%;height: 40%">
                                <div class="module-content">
                                    <div class="module-title" style="margin-top:3%;font-size: 18px">覆盖区域：<span id="totalresidential"></span>个</div>
                                    <div  class="myscroll" style="height: 90%;margin-top:10%;text-align:left;font-size: 16px">本月新增：<span id="totalresidential_monthadd"><?php echo ($info["totalresidential_monthadd"]); ?></span>个</div>
                                </div>
                            </div>
                        </div>
                        <div class="module-wraper1 fr" style="width: 33%;">
                            <div class="module" style="margin: 0 0 0 4%;width: 98%;height: 40%">
                                <div class="module-content">
                                    <div class="module-title" style="margin-top:3%;font-size: 18px">使用人数：<span id="totaluser"></span></div>
                                    <div  class="myscroll" style="height: 90%;margin-top:10%;text-align:left;font-size: 16px">本月新增：<span id="totaluser_monthadd"><?php echo ($info["totaluser_monthadd"]); ?></span>人</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="fl" style="width: 25%;height: 100%;">
                    <div class="module-wraper" style="height:13.2%;">
                        <div class="tjwraper1" >
                            <div class="modulex-title">机器总数量</div>
                            <div class="zjq">.&nbsp;&nbsp;<span id="totaldev"></span>台&nbsp;&nbsp;.</div>
                            <div style="width:100%;height:40%;margin-top: 6%">
                                <div class="zjqli" style="margin-left:3%">
                                    <p style="height: 20%;"><span id="onlinedev" style="color:#FF3700;font-size:19px"><?php echo ($info["onlinedev"]); ?></span>台</p>
                                    <p style="height: 20%;">在线设备</p>
                                </div>
                                <div class="zjqli">
                                    <p style="height: 20%;"><span id="offlinedev" style="color:#FF3700;font-size:19px"><?php echo ($info["offlinedev"]); ?></span>台</p>
                                    <p style="height: 20%;">离线设备</p>
                                </div>
                                <div class="zjqli">
                                    <p style="height: 20%;"><span id="alarmdev" style="color:#FF3700;font-size:19px"><?php echo ($info["alarmdev"]); ?></span>台</p>
                                    <p style="height: 20%;">故障设备</p>
                                </div>
                            </div>
                        </div>
                        <div class="tjwraper2" >
                            <div class="modulex-title">设备溢满详情</div>
                            <div style="width:100%;height:60%;margin-top: 5%">
                                <div class="ymleft">
                                    <p style="height:20%;margin-top:15%;font-size:17px">未满仓</p>
                                    <p ><span id="unfulldev" style="color:#FF3700"><?php echo ($info["unfulldev"]); ?></span>台</p>
                                </div>
                                <div class="ymcenter" >
                                    <!-- <img src="/Public/img/bp.png" style="width:100%"> -->
                                    <div id="initBrimx" style="height:150%;width:150%"></div>
                                </div>
                                <div class="ymleft">
                                    <p style="height:20%;margin-top:15%;font-size:17px">已满仓</p>
                                    <p ><span id="fulldev" style="color:#FF3700"><?php echo ($info["fulldev"]); ?></span>台</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space"></div>
                    <div class="module-wraper" style="height:18%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">今日投递时间</div>
                               <!--  <div class="module-title"><div class="icon11">&nbsp;</div>总量变化</div> -->
                                <div id="toudishijian" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper" style="height:18%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">已满时间分布</div>
                               <!--  <div class="module-title"><div class="icon11">&nbsp;</div>总量变化</div> -->
                                <div id="ymsjfb" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper" style="height:44.8%">
                        <div class="module">
                            <div class="module-content" style="padding: 16px 28px">
                                <div class="modulex-title">各社区垃圾量排名</div>
                                <!-- <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>各社区垃圾量排名</div> -->
                                <div class='clearfix wraper1003'><div class='fl text114'>排名</div><div class='fl text111'>小区</div><div class='fr text113'>垃圾量</div></div>
                                <div id="shequlajipaiming" class="myscroll"></div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="space"></div>
                    <div class="module-wraper" style="height:28%">
                        <div class="module">
                            <div class="module-content">
                                <div class="modulex-title">各类垃圾总量实况</div>
                                <div class="clearfix" style="margin: 20px 0;font-size: 14px;height:100%;">
                                    <div class="fl" style="display:none;width: 50%;height:100%;color: #ffffff;">
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #0fffb8;">&nbsp;</div>厨余垃圾</div>
                                            <div class="fr" class="text112">1456</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #03ace9;">&nbsp;</div>塑料类</div>
                                            <div class="fr" class="text112">654</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #4659f0;">&nbsp;</div>纸张类</div>
                                            <div class="fr" class="text112">465</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #ac39c5;">&nbsp;</div>金属类</div>
                                            <div class="fr" class="text112">156</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #c53964;">&nbsp;</div>有害类</div>
                                            <div class="fr" class="text112">45</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #cab556;">&nbsp;</div>织物类</div>
                                            <div class="fr" class="text112">33</div>
                                        </div>
                                    </div>
                                    <div class="fl" style="width: 100%;height:100%;color: #0fffb8;">
                                        <div id="fenleitongji" style="width: 100%;height: 85%;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
   
</div>
<script src="/Public/js/jquery.min.js?v=2.1.4"></script>
<script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
<script src="/Public/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/Public/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
<script src="/Public/js/plugins/layer/layer.min.js"></script>
<script src="/Public/js/hplus.js?v=4.1.0"></script>
<script src="/Public/js/plugins/toastr/toastr.min.js"></script>
<script type="text/javascript" src="/Public/js/contabs.js"></script>
<script type="text/javascript" src="/Public/js/my.js"></script>
<script type="text/javascript" src="/Public/js/common.js"></script>
<script type="text/javascript" src="/Public/js/echarts.min.js"></script>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=5wyVAcV0Kr2V4OUGpOM4WPOibz2gQ8rS&s=1"></script>

<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@4/map/js/china.js"></script>
<script type="text/javascript">
    var titleheight  = $(".modulex-title").height();
    $(".modulex-title").css({
        "line-height":titleheight*0.8+"px",
    })
    var litdh = $(".litd").height();
    $(".litd").css({
        "line-height":litdh+"px",
    })
</script>
<script>
    var markerArr;
    $(document).ready(function(){
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#goto_mgr").on("click",function(){
            window.location.href = "/index.php/Admin/Main/index";
        });
        //标注点数组
        markerArr=new Array();
        postjson("/index.php/Admin/Dashboard/api_getDevlist",null,function (data) {
            if(data){
                markerArr.length=0;
                data.forEach(function (value,index,array) {
                    // console.log(value)
                    markerArr.push({title:null,content:value.name,point:value.lng+"|"+value.lat,isOpen:0,icon:{w:23,h:25,l:46,t:21,x:9,lb:12},devid:value.id,icontype:value.online,devtype:value.devtype});
                })
                initMap();//创建和初始化地图
            }
        })
        regreshTime();
        setInterval("regreshTime()",5000);
        // initCanyurenshu();
        // initToufangcishu();
        // initFenleitongji();
        initPlfbtj();
        initToudishijian();
        initZongliang7tian();
        initToufangcishu7tian();
        intYmsjfu();
        initShequlajipaiming();
        // initData();
        // setInterval(initData,50000);
        // initShequweifatongji();
        initShixinren();
        setInterval(initShixinren,50000);
        initZongliangbudabiao();
        //initMap();
        inithsjcover();
        initBrim();
        setInterval(initBrim,50000);

    });
    
    function regreshTime() {
        postjson("/index.php/Admin/Dashboard/getDashboardTime",null,function (data) {
            $('#currenttime').text(data);
        })
    }

    // function initData() {
    //     postjson("/index.php/Admin/Dashboard/getDashboardData",null,function (data) {
    //         console.log(data);
    //         $('#totaldev').text(data.totaldev);
    //         $('#onlinedev').text(data.onlinedev);
    //         $('#offlinedev').text(data.offlinedev);
    //         $('#alarmdev').text(data.alarmdev);
    //         $('#totalwpuser').text(data.totalwpuser);
    //         $('#totalcarduser').text(data.totalcarduser);
    //         $('#totalfamily').text(data.totalfamily);
    //         $('#totalresidential').text(data.totalresidential);
    //         $('#totaluser').text(data.totaluser);
    //         $('#totalfamily_monthadd').text(data.totalfamily_monthadd);
    //         $('#totalresidential_monthadd').text(data.totalresidential_monthadd);
    //         $('#totaluser_monthadd').text(data.totaluser_monthadd);

    //         $('#zhilei_zongliang').text(data.zhilei_zongliang);
    //         $('#zhilei_zuori').text(data.zhilei_zuori);
    //         $('#zhilei_jinri').text(data.zhilei_jinri);
    //         $('#suliao_zongliang').text(data.suliao_zongliang);
    //         $('#suliao_zuori').text(data.suliao_zuori);
    //         $('#suliao_jinri').text(data.suliao_jinri);
    //         $('#jinshu_zongliang').text(data.jinshu_zongliang);
    //         $('#jinshu_zuori').text(data.jinshu_zuori);
    //         $('#jinshu_jinri').text(data.jinshu_jinri);
    //         $('#fangzhi_zongliang').text(data.fangzhi_zongliang);
    //         $('#fangzhi_zuori').text(data.fangzhi_zuori);
    //         $('#fangzhi_jinri').text(data.fangzhi_jinri);
    //         $('#youdu_zongliang').text(data.youdu_zongliang);
    //         $('#youdu_zuori').text(data.youdu_zuori);
    //         $('#youdu_jinri').text(data.youdu_jinri);

    //         $('#qita_zongliang').text(data.qita_zongliang);
    //         $('#qita_zuori').text(data.qita_zuori);
    //         $('#qita_jinri').text(data.qita_jinri);
    //         $('#pingguan_zongliang').text(data.pingguan_zongliang);
    //         $('#pingguan_zuori').text(data.pingguan_zuori);
    //         $('#pingguan_jinri').text(data.pingguan_jinri);

    //         $('#unfulldev').text(data.unfulldev);
    //         $('#fulldev').text(data.fulldev);
    //     })
    // }
    //溢满
    function initBrim(){
        var dom = document.getElementById("initBrimx");
        var myChart = echarts.init(dom);
        var app = {};
        var val1 = $("#unfulldev").html();
        var val2 = $("#fulldev").html();
        var sum = Number(val1)+Number(val2);
        var spirt = Number(val1)/sum;
        var option;



        option = {
            series: [{
                type: 'gauge',
                startAngle: 180,
                endAngle: 0,
                min: 0,
                max: 100,
                splitNumber: 5,
                axisLine: {
                    lineStyle: {
                        width: 10,
                        color: [
                            [spirt, '#69BCFF'],
                            [1, '#FF00FF'],
                        ],

                        
                    }
                },
                center: ['35%', '45%'],
                pointer: {
                    length: '12%',
                    width: 0,
                    offsetCenter: [0, '-60%'],
                    itemStyle: {
                        color: 'auto'
                    }
                },
                axisTick: {
                    length: 12,
                    lineStyle: {
                        color: 'auto',
                        width: 0,

                    }
                },
                axisLabel:{
                    show: false,
                    fontSize: 0
                },
                splitLine: {
                    length: 10,
                    lineStyle: {
                        color: '#060F54',
                        width: 1
                    },
                    distance:-10
                },
                // title: {
                //     offsetCenter: [0, '-20%'],
                //     fontSize: 30
                // },
                detail: {
                    show: false
                },
                
                
            }]
        };

        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }
    }
    function inithsjcover(){
        var myChart = echarts.init(document.getElementById('hsjcover'));
        /*指定图表的配置项和数据*/
        option = {
            textStyle: {
                color: '#fff',
                fontSize: '16'
            },
            title: {
                textStyle: {
                    color: '#fff',
                },
                left: '50%',
                text: '',
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: { //设置图表位置
                left: '3%',
                right: '4%',
                top: '6%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                splitLine: { //去掉网格线
                    show: false
                },
                position: 'top', //X轴位置
                type: 'value',
                boundaryGap: [0, 0.01],
                // axisLabel: { //坐标轴刻度标签的相关设置
                //     // rotate:'45',//坐标轴文字旋转角度
                //     show: false,
                //     textStyle: {
                //         color: '#00ffd2',
                //         align: 'right',
                //         fontSize: 15
                //     }
                // },
                axisLine: {
                    lineStyle: {
                        color: '#01563B',               
                    },
                     symbol: ['none', 'arrow']
                },
                axisTick: {
                    show: false
                },
            },
            yAxis: {
                type: 'category', //轴的类型分两种 1.category（类别）2.value(值)            
                data: ['200-300','300-500','500-700','700-1000','1000+'],
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: '#00ffd2',
                        align: 'right',
                        fontSize: 15 /*文字大小*/
                    }
                },
                axisLine: {
                    type: 'dotted', //设置Y轴坐标为实线
                    lineStyle: {             
                        color: '#01563B',//轴的颜色      
                    },              
                    symbol: ['arrow'] //添加箭头
                },  
                axisTick: {
                    show: false
                },
            },
            series: [{
                type: 'bar',
                data: ['20', '30', '50', '78', '90', ],
                barWidth: 15, //柱子宽度
                itemStyle: {
                    normal: {
                        color: function(params) {
                            //注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                            var colorList = ['#00C7D5', '#00C7D5', '#00C7D5', '#00C7D5', '#00C7D5'];
                            return colorList[params.dataIndex]
                        },
                    }
                },
            }]
        };
        myChart.setOption(option);
    }

    function initCanyurenshu(){
        postjson("getCanyurenshu",null,function (data) {
            if(!data){
                return;
            }
            var myChart = echarts.init(document.getElementById("canyurenshu"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                    
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                    
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'bar',
                    barWidth: 10,
                    itemStyle:{
                        normal:{
                            color:'#50eae0'
                        }
                    },
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };

            myChart.setOption(option);
        })
    }

    function initToufangcishu(){
        postjson("getToufangcishu",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("toufangcishu"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'bar',
                    barWidth: 10,
                    itemStyle:{
                        normal:{
                            color:'#50eae0'
                        }
                    },
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };

            myChart.setOption(option);
        });
    }

    function initFenleitongji(){
        postjson("getFenleitongji",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("fenleitongji"));
            var option = {
                tooltip : {
                    trigger: 'item',
                    formatter: "{b} : {c} ({d}%)"
                },
                legend: {
                    orient: 'vertical',
                    x: 'left',
                    data:data.rubbishtypenamelist,
                    show:true,
                    itemWidth:10,
                    itemHeight:10,
                    textStyle:{
                        fontSize: 12,
                        color:'#ffffff',
                    }
                },
                series: [
                    {
                        name:'垃圾分类',
                        type:'pie',
                        radius: ['40%', '60%'],
                        center: ['70%', '43%'],
                        avoidLabelOverlap: false,
                        label: {
                            show : false,
                        },
                        // 设置值域的那指向线
                        labelLine: {
                            normal: {
                                show: false   // show设置线是否显示，默认为true，可选值：true ¦ false
                            }
                        },
                        data:data.valuelist
                    }
                ]
            };
            myChart.setOption(option);
        });
    }
    function initPlfbtj(){
        postjson("getFenleitongji",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("plfbtj"));
            var option = {
                tooltip : {
                    trigger: 'item',
                    formatter: "{b} : {c} ({d}%)"
                },
                legend: {
                    orient: 'vertical',
                    x: 'left',
                    data:data.rubbishtypenamelist,
                    show:true,
                    itemWidth:10,
                    itemHeight:10,
                    textStyle:{
                        fontSize: 14,
                        color:'#ffffff',
                    }
                },
                series: [
                    {
                        name:'垃圾分类',
                        type:'pie',
                        radius: ['40%', '60%'],
                        center: ['70%', '43%'],
                        avoidLabelOverlap: false,
                        label: {
                            show : false,
                        },
                        // 设置值域的那指向线
                        labelLine: {
                            normal: {
                                show: false   // show设置线是否显示，默认为true，可选值：true ¦ false
                            }
                        },
                        data:data.valuelist
                    }
                ]
            };
            myChart.setOption(option);
        });
    }

    function initToudishijian() {
        postjson("getToudishijian",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("toudishijian"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    splitLine:{
                        show:false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'line',
                    smooth:true,    //曲线平滑
                    itemStyle : {
                        normal : {
                            areaStyle : {
                                type : 'default',
                                //渐变色实现
                                color : new echarts.graphic.LinearGradient(0, 0, 0, 1,//变化度
                                        //三种由深及浅的颜色
                                        [ {
                                            offset : 0,
                                            color : '#23c94b'
                                        }, {
                                            offset : 0.5,
                                            color : '#1a6686'
                                        }, {
                                            offset : 1,
                                            color : '#12418e'
                                        } ]),
                            },
                            lineStyle : {  //线的颜色
                                color : '#349e85'
                            },
                            //以及在折线图每个日期点顶端显示数字
                            label: {
                                show: true,
                                position: 'top',
                                textStyle: {
                                    color: 'white'
                                }
                            }
                        },
                    },
                    areaStyle: {normal: {}},
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };
            myChart.setOption(option);
        });
    }

    // function initZongliang7tian() {
    //     postjson("getZongliang7tian",null,function (data) {
    //         if (!data) {
    //             return;
    //         }
    //         var myChart = echarts.init(document.getElementById("zongliang7tian"));
    //         var option = {
    //             xAxis: {
    //                 type: 'category',
    //                 data: data.monthlist,
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             yAxis: {
    //                 type: 'value',
    //                 splitLine:{
    //                     show:true,
    //                     lineStyle:{
    //                         color: ['#27495f'],
    //                         width: 1,
    //                         type: 'solid'
    //                     }
    //                 },
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             tooltip : {
    //                 trigger: 'axis'
    //             },
    //             series: [{
    //                 data: data.valuelist,
    //                 type: 'bar',
    //                 barWidth: 10,
    //                 itemStyle:{
    //                     normal:{
    //                         color:'#50eae0'
    //                     }
    //                 },
    //             }],
    //             grid:{
    //                 x:50,
    //                 y:30,
    //                 x2:5,
    //                 y2:20,
    //                 borderWidth:0
    //             }
    //         };
    //         myChart.setOption(option);
    //     });
    // }
    
    function initZongliang7tian() {
        var monthlist = <?php echo ($zongliang7tian["monthlist"]); ?>;
        var valuelist = <?php echo ($zongliang7tian["valuelist"]); ?>;
        var myChart = echarts.init(document.getElementById("zongliang7tian"));
        var option = {
            xAxis: {
                type: 'category',
                data: monthlist,
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            yAxis: {
                type: 'value',
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['#27495f'],
                        width: 1,
                        type: 'solid'
                    }
                },
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            tooltip : {
                trigger: 'axis'
            },
            series: [{
                data: valuelist,
                type: 'bar',
                barWidth: 10,
                itemStyle:{
                    normal:{
                        color:'#50eae0'
                    }
                },
            }],
            grid:{
                x:50,
                y:30,
                x2:5,
                y2:20,
                borderWidth:0
            }
        };
        myChart.setOption(option);
    }

    // function initToufangcishu7tian() {
    //     postjson("getToufangcishu7tian",null,function (data) {
    //         if (!data) {
    //             return;
    //         }
    //         var myChart = echarts.init(document.getElementById("toufangcishu7tian"));
    //         var option = {
    //             xAxis: {
    //                 type: 'category',
    //                 data: data.monthlist,
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             yAxis: {
    //                 type: 'value',
    //                 splitLine:{
    //                     show:true,
    //                     lineStyle:{
    //                         color: ['#27495f'],
    //                         width: 1,
    //                         type: 'solid'
    //                     }
    //                 },
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             tooltip : {
    //                 trigger: 'axis'
    //             },
    //             series: [{
    //                 data: data.valuelist,
    //                 type: 'bar',
    //                 barWidth: 10,
    //                 itemStyle:{
    //                     normal:{
    //                         color:'#50eae0'
    //                     }
    //                 },
    //             }],
    //             grid:{
    //                 x:50,
    //                 y:30,
    //                 x2:5,
    //                 y2:20,
    //                 borderWidth:0
    //             }
    //         };
    //         myChart.setOption(option);
    //     });
    // }
    function initToufangcishu7tian() {
        var monthlist = <?php echo ($toufangcishu7tian["monthlist"]); ?>;
        var valuelist = <?php echo ($toufangcishu7tian["valuelist"]); ?>;
        var myChart = echarts.init(document.getElementById("toufangcishu7tian"));
        var option = {
            xAxis: {
                type: 'category',
                data: monthlist,
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            yAxis: {
                type: 'value',
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['#27495f'],
                        width: 1,
                        type: 'solid'
                    }
                },
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            tooltip : {
                trigger: 'axis'
            },
            series: [{
                data: valuelist,
                type: 'bar',
                barWidth: 10,
                itemStyle:{
                    normal:{
                        color:'#50eae0'
                    }
                },
            }],
            grid:{
                x:50,
                y:30,
                x2:5,
                y2:20,
                borderWidth:0
            }
        };
        myChart.setOption(option);
    }
    // function intYmsjfu(){
    //     postjson("getZongliangbianhua",null,function (data) {
    //         if (!data) {
    //             return;
    //         }
    //         var myChart = echarts.init(document.getElementById("ymsjfb"));
    //         var option = {
    //             xAxis: {
    //                 type: 'category',
    //                 data: data.monthlist,
    //                 splitLine:{
    //                     show:false
    //                 },
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             yAxis: {
    //                 type: 'value',
    //                 splitLine:{
    //                     show:true,
    //                     lineStyle:{
    //                         color: ['#27495f'],
    //                         width: 1,
    //                         type: 'solid'
    //                     }
    //                 },
    //                 axisLabel: {
    //                     textStyle: {
    //                         color: '#FFFFFF'
    //                     }
    //                 },
    //             },
    //             tooltip : {
    //                 trigger: 'axis'
    //             },
    //             series: [{
    //                 data: data.valuelist,
    //                 type: 'line',
    //                 smooth:true,    //曲线平滑
    //                 itemStyle : {
    //                     normal : {
    //                         areaStyle : {
    //                             type : 'default',
    //                             //渐变色实现
    //                             color : new echarts.graphic.LinearGradient(0, 0, 0, 1,//变化度
    //                                     //三种由深及浅的颜色
    //                                     [ {
    //                                         offset : 0,
    //                                         color : '#23c94b'
    //                                     }, {
    //                                         offset : 0.5,
    //                                         color : '#1a6686'
    //                                     }, {
    //                                         offset : 1,
    //                                         color : '#12418e'
    //                                     } ]),
    //                         },
    //                         lineStyle : {  //线的颜色
    //                             color : '#349e85'
    //                         },
    //                         //以及在折线图每个日期点顶端显示数字
    //                         label: {
    //                             show: true,
    //                             position: 'top',
    //                             textStyle: {
    //                                 color: 'white'
    //                             }
    //                         }
    //                     },
    //                 },
    //                 areaStyle: {normal: {}},
    //             }],
    //             grid:{
    //                 x:50,
    //                 y:30,
    //                 x2:5,
    //                 y2:20,
    //                 borderWidth:0
    //             }
    //         };
    //         myChart.setOption(option);
    //     });
    // }
    function intYmsjfu(){
        var monthlist = <?php echo ($ymsjfb["monthlist"]); ?>;
        var valuelist = <?php echo ($ymsjfb["valuelist"]); ?>;
        var myChart = echarts.init(document.getElementById("ymsjfb"));
        var option = {
            xAxis: {
                type: 'category',
                data: monthlist,
                splitLine:{
                    show:false
                },
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            yAxis: {
                type: 'value',
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['#27495f'],
                        width: 1,
                        type: 'solid'
                    }
                },
                axisLabel: {
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
            },
            tooltip : {
                trigger: 'axis'
            },
            series: [{
                data: valuelist,
                type: 'line',
                smooth:true,    //曲线平滑
                itemStyle : {
                    normal : {
                        areaStyle : {
                            type : 'default',
                            //渐变色实现
                            color : new echarts.graphic.LinearGradient(0, 0, 0, 1,//变化度
                                    //三种由深及浅的颜色
                                    [ {
                                        offset : 0,
                                        color : '#23c94b'
                                    }, {
                                        offset : 0.5,
                                        color : '#1a6686'
                                    }, {
                                        offset : 1,
                                        color : '#12418e'
                                    } ]),
                        },
                        lineStyle : {  //线的颜色
                            color : '#349e85'
                        },
                        //以及在折线图每个日期点顶端显示数字
                        label: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: 'white'
                            }
                        }
                    },
                },
                areaStyle: {normal: {}},
            }],
            grid:{
                x:50,
                y:30,
                x2:5,
                y2:20,
                borderWidth:0
            }
        };
        myChart.setOption(option);
        
    }

    function initShequlajipaiming() {
        postjson("getShequlajipaiming",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                    html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text114'>";
                }else{
                    html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text114'>";// 奇数  
                }
                
                html+=data[i].index;
                html+="</div><div class='fl' class='text111'>";
                html+=data[i].propertyname;
                html+="</div><div class='fr' class='text113'>";
                html+=data[i].weight;
                html+="kg</div></div>";
            }
            $('#shequlajipaiming').html(html);
        });
    }

    function initShequweifatongji() {
        // postjson("getShequweifatongji",null,function (data) {
        //     if (!data) {
        //         return;
        //     }
        //     var html="";
        //     for(var i=0;i<data.length;i++){
        //         if( i%2 == 0 ){
        //            html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text114'>";
        //         }else{
        //            html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text114'>"; 
        //         }
                
        //         html+=data[i].index;
        //         html+="</div><div class='fl' class='text111'>";
        //         html+=data[i].propertyname;
        //         html+="</div><div class='fr' class='text113'>";
        //         html+=data[i].num;
        //         html+="</div></div>";
        //     }
        //     $('#shequweifatongji').html(html);
        // });
        postjson("getToufangcishu",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("shequweifatongji"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'bar',
                    barWidth: 10,
                    itemStyle:{
                        normal:{
                            color:'#50eae0'
                        }
                    },
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };

            myChart.setOption(option);
        });
    }

    function initShixinren() {
        postjson("getThrowlog",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                  html+="<div  class='clearfix wraper1003'><div class='fl '>";
                }else{
                  html+="<div  class='clearfix wraper1003'><div class='fl '>";
                }
                
                html+=data[i].time+" 用户"+data[i].name+"在"+data[i].devname;
                html+="投递了";
                html+=data[i].throwweight;
                html+="克"+data[i].rubbishtypename+"</div></div>";
            }
            $('#shixinren').html(html);
        });
    }

    function initZongliangbudabiao() {
        postjson("getZongliangbudabiao",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                  html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text111'>";
                }else{
                  html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text111'>";
                }
                
                html+=data[i].name;
                html+="</div><div class='fr' class='text112'>";
                html+=data[i].weight;
                html+="kg</div></div>";
            }
            $('#zongliangbudabiao').html(html);
        });
    }

    //创建和初始化地图函数：
    function initMap(){
        createMap();//创建地图
        setMapEvent();//设置地图事件
        addMapControl();//向地图添加控件
       addMarker();//向地图中添加marker
    }

    //创建地图函数：
    function createMap(){
        var map = new BMap.Map("map");//在百度地图容器中创建一个地图
        var point = new BMap.Point(109.236222,30.749918);//定义一个中心点坐标
        map.centerAndZoom(point,5);//设定地图的中心点和坐标并将地图显示在地图容器中
        map.setMapStyle({
            styleJson:[
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": {
                        "color": "#021019"
                    }
                },
                {
                    "featureType": "highway",
                    "elementType": "geometry.fill",
                    "stylers": {
                        "color": "#9e7d60ff"
                    }
                },
                {
                    "featureType": "highway",
                    "elementType": "geometry.stroke",
                    "stylers": {
                        "color": "#184a92ff"
                    }
                },
                {
                    "featureType": "arterial",
                    "elementType": "geometry.fill",
                    "stylers": {
                        "color": "#38414eff"
                    }
                },
                {
                    "featureType": "arterial",
                    "elementType": "geometry.stroke",
                    "stylers": {
                        "color": "#554631fa"
                    }
                },
                {
                    "featureType": "local",
                    "elementType": "geometry",
                    "stylers": {
                        "color": "#000000"
                    }
                },
                {
                    "featureType": "land",
                    "elementType": "all",
                    "stylers": {
                        "color": "#1b57abff"
                    }
                },
                {
                    "featureType": "railway",
                    "elementType": "geometry.fill",
                    "stylers": {
                        "visibility": "off",
                        "color": "#000000ff"
                    }
                },
                {
                    "featureType": "railway",
                    "elementType": "geometry.stroke",
                    "stylers": {
                        "visibility": "off",
                        "color": "#08304b"
                    }
                },
                {
                    "featureType": "subway",
                    "elementType": "geometry",
                    "stylers": {
                        "lightness": -70
                    }
                },
                {
                    "featureType": "building",
                    "elementType": "geometry.fill",
                    "stylers": {
                        "color": "#000000"
                    }
                },
                {
                    "featureType": "all",
                    "elementType": "labels.text.fill",
                    "stylers": {
                        "color": "#857f7f"
                    }
                },
                {
                    "featureType": "all",
                    "elementType": "labels.text.stroke",
                    "stylers": {
                        "color": "#000000"
                    }
                },
                {
                    "featureType": "building",
                    "elementType": "geometry",
                    "stylers": {
                        "color": "#022338"
                    }
                },
                {
                    "featureType": "green",
                    "elementType": "geometry",
                    "stylers": {
                        "color": "#263b3eff"
                    }
                },
                {
                    "featureType": "boundary",
                    "elementType": "all",
                    "stylers": {
                        "color": "#8c8a8a"
                    }
                },
                {
                    "featureType": "manmade",
                    "elementType": "geometry",
                    "stylers": {
                        "color": "#184a92ff"
                    }
                },
                {
                    "featureType": "poi",
                    "elementType": "all",
                    "stylers": {
                        "visibility": "off"
                    }
                },
                {
                    "featureType": "all",
                    "elementType": "labels.icon",
                    "stylers": {
                        "visibility": "off"
                    }
                },
                {
                    "featureType": "all",
                    "elementType": "labels.text.fill",
                    "stylers": {
                        "color": "#d69563",
                        "visibility": "on"
                    }
                }
            ]
        });
        window.map = map;//将map变量存储在全局
    }
    // function createMap(){
    //     var dom = document.getElementById("map");
    //     var myChart = echarts.init(dom);
    //     var app = {};
    //     option = null;
    //     option = {
    //         geo: {
    //             map: 'china',
    //             itemStyle: {
    //                 normal:{
    //                     borderWidth:2,//边框大小
    //                     borderColor:'#6A7EBF',
    //                     areaColor: '#002A56',//背景颜色
    //                     label: {
    //                         show: true//显示名称
    //                     }
    //                 },
    //                 emphasis: {
    //                     borderWidth:2,
    //                     areaColor: '#08EAFE',
    //                     label: {
    //                         show: true,
    //                         textStyle: {
    //                             color: '#fff'
    //                         }
    //                     }
    //                  }    
    //             }
    //         },
    //         series : [
    //             {
    //                 name: '设备',
    //                 type: 'scatter',
    //                 coordinateSystem: 'geo',
    //                 mapType: 'china',
    //                 roam: false,
    //                 label: {
    //                     normal: {
    //                         show: false
    //                     },
    //                     emphasis: {
    //                         show: true
    //                     }
    //                 },
                    

    //                 data:[
    //                     {
    //                         name: '江苏',
    //                         value:[119.98,32.2]
    //                     },
    //                     {
    //                         name: '江苏',
    //                         value:[119.85,33.5]
    //                     },
                        
    //                     // {name: '天津',value: 0},
    //                     // {name: '上海',value: 0},
    //                     // {name: '重庆',value: 0},
    //                     // {name: '河北',value: 0},
    //                     // {name: '河南',value: 0},
    //                     // {name: '云南',value: 0},
    //                     // {name: '辽宁',value: 0},
    //                     // {name: '黑龙江',value: 0},
    //                     // {name: '湖南',value: 0},
    //                     // {name: '安徽',value: 2},
    //                     // {name: '山东',value: 3},
    //                     // {name: '新疆',value: 0},
    //                     // {
    //                     //     name: '江苏',
    //                     //     value:[116.18,35.2]
    //                     // },
    //                     // {name: '浙江',value: 0},
    //                     // {name: '江西',value: 0},
    //                     // {name: '湖北',value: 0},
    //                     // {name: '广西',value: 0},
    //                     // {name: '甘肃',value: 0},
    //                     // {name: '山西',value: 0},
    //                     // {name: '内蒙古',value: 0},
    //                     // {name: '陕西',value: 0},
    //                     // {name: '吉林',value: 0},
    //                     // {name: '福建',value: 0},
    //                     // {name: '贵州',value: 0},
    //                     // {name: '广东',value: 0},
    //                     // {name: '青海',value: 0},
    //                     // {name: '西藏',value: 0},
    //                     // {name: '四川',value: 0},
    //                     // {name: '宁夏',value: 0},
    //                     // {name: '海南',value: 0},
    //                     // {name: '台湾',value: 0},
    //                     // {name: '香港',value: 0},
    //                     // {name: '澳门',value: 0},

    //                 ]
    //             }
    //         ]
    //     };;
    //     if (option && typeof option === "object") {
    //         myChart.setOption(option, true);
    //     }
    // }

    //地图事件设置函数：
    function setMapEvent(){
        map.enableDragging();//启用地图拖拽事件，默认启用(可不写)
        map.enableScrollWheelZoom();//启用地图滚轮放大缩小
        map.enableDoubleClickZoom();//启用鼠标双击放大，默认启用(可不写)
        map.enableKeyboard();//启用键盘上下左右键移动地图
        map.addEventListener("click", function(e){
            $('#markerinfo').css('display','none');
        });
    }

    //地图控件添加函数：
    function addMapControl(){
        //向地图中添加缩放控件
        // var ctrl_nav = new BMap.NavigationControl({anchor:BMAP_ANCHOR_TOP_LEFT,type:BMAP_NAVIGATION_CONTROL_SMALL});
        // map.addControl(ctrl_nav);
    }
    //创建marker
    function addMarker1(){
        var mypoints = [];//坐标点,用于计算中心点和缩放比例
        for(var i=0;i<markerArr.length;i++){
            var json = markerArr[i];
            var p0 = json.point.split("|")[0];
            var p1 = json.point.split("|")[1];
            var point = new BMap.Point(p0,p1);
            point.devname=json.name;
            point.devcode=json.devcode;
            mypoints.push(point);

            // if(json.devtype == 1){
            //     alert(json.devtype);
            // }
            
            var iconImg = createIcon(json.icontype);
        }
        var options = {
            size: BMAP_POINT_SIZE_SMALL,    //BMAP_POINT_SIZE_SMALL
            shape: BMAP_POINT_SHAPE_CIRCLE,
            color: '#0fffb8'
        };
        var pointCollection = new BMap.PointCollection(mypoints, options);
        pointCollection.addEventListener('click', function(e){
            var req={
                        id:_devid
                    }
                    postjson("/index.php/Admin/Dashboard/getDevInfo",req,function (data) {
                        if(!data){
                            return;
                        }
                        $('#markerinfo').css('display','block');
                        $('#devname').text(data.name);
                        $('#devcode').text(data.code);
                        $('#onlinename').text(data.onlinename);
                    })
        });
        map.addOverlay(pointCollection);
        var view = map.getViewport(mypoints);// 获取所有坐标中心点以及缩放比例
        map.setZoom(view.zoom);// 设置缩放比例
        map.setCenter(view.center);// 设置中心点
    }

    //创建marker
    function addMarker(){
        // console.log(markerArr);
        var mypoints = [];//坐标点,用于计算中心点和缩放比例
        for(var i=0;i<markerArr.length;i++){
            var json = markerArr[i];
            var p0 = json.point.split("|")[0];
            var p1 = json.point.split("|")[1];
            var point = new BMap.Point(p0,p1);
            mypoints.push(point);
            var iconImg = createIcon(json.icontype,json.devtype);
            var marker = new BMap.Marker(point,{icon:iconImg});
            var iw = createInfoWindow(i);
            var label = new BMap.Label(json.title,{"offset":new BMap.Size(json.icon.lb-json.icon.x+10,-20)});
            var devid=json.devid;

            //marker.setLabel(label);
            map.addOverlay(marker);
            label.setStyle({
                borderColor:"#808080",
                color:"#333",
                cursor:"pointer"
            });

            (function(){
                var index = i;
                var _iw = createInfoWindow(i);
                var _marker = marker;
                var _devid=devid;
                _marker.addEventListener("click",function(){
                    var req={
                        id:_devid
                    }
                    postjson("/index.php/Admin/Dashboard/getDevInfo",req,function (data) {
                        if(!data){
                            return;
                        }
                        $('#markerinfo').css('display','block');
                        $('#devname').text(data.name);
                        $('#devcode').text(data.code);
                        $('#onlinename').text(data.onlinename);
                    })
                });
                _iw.addEventListener("open",function(){
                    // _marker.getLabel().hide();
                })
                _iw.addEventListener("close",function(){
                    // _marker.getLabel().show();
                })
                label.addEventListener("click",function(){
                    // _marker.openInfoWindow(_iw);
                })
                if(!!json.isOpen){
                    label.hide();
                    _marker.openInfoWindow(_iw);
                }
            })()
        }
        var view = map.getViewport(mypoints);// 获取所有坐标中心点以及缩放比例
        map.setZoom(view.zoom-1);// 设置缩放比例
        map.setCenter(view.center);// 设置中心点
    }
    //创建InfoWindow
    function createInfoWindow(i){
        var json = markerArr[i];
        var iw = new BMap.InfoWindow("<div class='iw_poi_content'>"+json.content+"</div>");
        return iw;
    }
    //创建一个Icon
    function createIcon(icontype,devtype){
        // var icon = new BMap.Icon("/Public/img/position.png", new BMap.Size(json.w,json.h),{imageOffset: new BMap.Size(-json.l,-json.t),infoWindowOffset:new BMap.Size(json.lb+5,1),offset:new BMap.Size(json.x,json.h)})
        if(icontype==1){
            if(devtype == 1){
                var filepath="/Public/img/mypoint3.png"; 
            }else{
                var filepath="/Public/img/devon1.png";  
            }
            var myIcon = new BMap.Icon(filepath, new BMap.Size(24, 24), {
                offset: new BMap.Size(0, 0), // 指定定位位置
                imageOffset: new BMap.Size(0, 0) // 设置图片偏移
            });
            
            return myIcon;
        }else{
            if(devtype == 1){
                var filepath="/Public/img/mypoint4.png";   
            }else{
                var filepath="/Public/img/devoff1.png"; 
            }
            
            var myIcon = new BMap.Icon(filepath, new BMap.Size(24, 24), {
                offset: new BMap.Size(0, 0), // 指定定位位置
                imageOffset: new BMap.Size(0, 0) // 设置图片偏移
            });
            return myIcon;
        }

    }

</script>
<script type="text/javascript">
 //获取id=demo,id=demo1,id=demo2的元素对象，并把id=demo1的内容赋值给id=demo2
 var demo=document.getElementById("demo");
 var demo1=document.getElementById("demo1");
 var demo2=document.getElementById("demo2");
 demo2.innerHTML=demo1.innerHTML;
 //给demo1,demo2加相同的高度
 demo1.style.height=demo.offsetHeight+"px";
 demo2.style.height=demo.offsetHeight+"px";
 //定时器，每隔50毫秒调用一次scrollup()函数，来实现文字的滚动
 var timer=window.setInterval("scrollup()",40);
 //定义函数
 function scrollup()
 {
  //如果demo滚上去的高度大于demo的高度，重新给demo赋值从0再开始滑动
  if(demo.scrollTop>=demo.offsetHeight)
  {
   demo.scrollTop=0;
  }else
  {
   demo.scrollTop++;
  }
 }
 //鼠标放上停止滚动，鼠标离开继续滚动
  demo.onmouseover=function(){
         //清除定时器
         clearInterval(timer);
         }
  demo.onmouseout=function(){
         //添加定时器
         timer=window.setInterval("scrollup()",40);
         }
 
</script>
</body>
</html>