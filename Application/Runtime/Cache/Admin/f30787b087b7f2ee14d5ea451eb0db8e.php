<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/szrecycle_mgr/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/szrecycle_mgr/Public/css/animate.css" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_listEventsToolbar"
                                     role="group">
                                    <?php if(($privilege) == "1"): ?><button id="addBtn" type="button"
                                                class="btn btn-outline btn-default" >
                                            <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline btn-default"
                                                style="display: none;">
                                            <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                        </button>
                                        <button id="delete" type="button"
                                                class="btn btn-outline btn-default">
                                            <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                        </button><?php endif; ?>
                                </div>
                                <table id="tb_list">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="lookpasswdModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="qrcodeTitle" class="modal-title">查看密码</h4>
                </div>
                <div class="modal-body">
                    <div id="lookpasswdModal_passwd" style="display: flex;justify-content: center;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <!--<button type="button" class="btn btn-primary">保存</button>-->
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="addRegulatorModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 900px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">添加监管人员</h4>
                </div>
                <div class="modal-body">
                    <form id="addRegulatorModal_form" class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">账号：</label>

                            <div class="col-sm-10">
                                <input id="account" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称：</label>

                            <div class="col-sm-10">
                                <input id="name" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">登录密码：</label>

                            <div class="col-sm-10">
                                <input id="userpwd" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">密码确认：</label>

                            <div class="col-sm-10">
                                <input id="userpwd1" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">地域</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="areaid1" style="float: left;width: 18%;">
                                    <option value="0">选择省份</option>
                                    <?php if(is_array($level1arealist)): $i = 0; $__LIST__ = $level1arealist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["areaid"]); ?>"><?php echo ($vo["areaname"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <select class="form-control m-b" id="areaid2" style="float: left;width: 18%;margin-left: 2px;">
                                </select>
                                <select class="form-control m-b" id="areaid3" style="float: left;width: 18%;margin-left: 2px;">
                                </select>
                                <select class="form-control m-b" id="areaid4" style="float: left;width: 18%;margin-left: 2px;">
                                </select>
                                <button id="addarea4" type="button" class="btn btn-info"  style="float: left;width: 18%;margin-bottom: 6px;">添加街道</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="addRegulatorModal_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="addArea4Modal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">添加街道</h4>
                </div>
                <div class="modal-body">
                    <form id="addArea4Modal_form" class="form-horizontal" >
                        <input id="addArea4Modal_area3id" type="text" class="form-control" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">名称：</label>
                            <div class="col-sm-9">
                                <input id="addArea4Modal_name" type="text" class="form-control">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="addArea4Modal_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 全局js -->
    <script src="/szrecycle_mgr/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/szrecycle_mgr/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/szrecycle_mgr/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/szrecycle_mgr/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/szrecycle_mgr/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/szrecycle_mgr/Public/js/common.js"></script>
    <script src="/szrecycle_mgr/Public/js/sha256.min.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_list').bootstrapTable({
                    url: '/szrecycle_mgr/index.php/Admin/UserMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "userid",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'userid',
                        title: 'ID'
                    }, {
                        field: 'account',
                        title: '账号'
                    }, {
                        field: 'name',
                        title: '名称'
                    }, {
                        field: 'areaid1name',
                        title: '省份'
                    }, {
                        field: 'areaid2name',
                        title: '城市'
                    }, {
                        field: 'areaid3name',
                        title: '区县'
                    }, {
                        field: 'areaid4name',
                        title: '街道'
                    },
                        {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                window.location.href = "devicedetail.do?userid="+this.nextSibling.innerText;
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "/szrecycle_mgr/index.php/Admin/UserMgr/update?userid="+this.previousSibling.innerText;
                            });
                        }

                        var lookpasswdBtns = $("button[name='lookpasswdBtn']");
                        for(var i=0;i<lookpasswdBtns.length;i++){
                            lookpasswdBtns[i].addEventListener("click",function(){
                                // window.location.href = "devicedetail.do?userid="+this.nextSibling.innerText;
                                var pwd=this.nextSibling.innerText;
                                if(!pwd){
                                    pwd='未知';
                                }
                                $('#lookpasswdModal_passwd').text(pwd);
                                $('#lookpasswdModal').modal({keyboard:false})
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                    roleid:<?php echo ($roleid); ?>,
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            if(<?php echo ($privilege); ?>!=1){
                return;
            }
            var html = '';
            html +='<div style="width:150px;text-align:center;">';
            if(<?php echo ($lookpasswd); ?>==1) {
                html += '<button name="lookpasswdBtn" type="button" class="btn btn-info btn-sm" href="#" style="">密码</button>';
                html += '<div style="display:none;">' + row['userpwd1'] + '</div>';
            }
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
            html += '<div style="display:none;">'+row['userid']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">修改</button>';
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].userid);
                    }
                    var req = {
                        idlist:users
                    }

                    $.ajax({
                        url:'/szrecycle_mgr/index.php/Admin/UserMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！"+data.data);
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#addBtn').on('click',function () {
                    $('#addRegulatorModal_form')[0].reset();
                    $('#addRegulatorModal').modal();
                })
                $('#addRegulatorModal_commit').on('click',function () {
                    var userpwd=$("#userpwd").val();
                    var userpwd1=$("#userpwd1").val();
                    if(''==userpwd){
                        toastr.error("请输入密码！ "+data.data);
                        return;
                    }
                    if(userpwd!=userpwd1){
                        toastr.error("确认密码不一致！ "+data.data);
                        return;
                    }

                    var req = {
                        account:$("#account").val(),
                        name:$("#name").val(),
                        userpwd:sha256(userpwd),
                        userpwd1:userpwd,
                        roleid:7,
                        areaid1:$("#areaid1").val(),
                        areaid2:$("#areaid2").val(),
                        areaid3:$("#areaid3").val(),
                        areaid4:$("#areaid4").val(),
                    };
                    $.ajax({
                        url:'/szrecycle_mgr/index.php/Admin/UserMgr/api_add',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("操作失败！ "+data.data);
                                return;
                            }
                            $('#addRegulatorModal').modal('hide');
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                })
                $('#areaid1').on('change',function () {
                    var req={
                        areaid:$('#areaid1').val(),
                    }
                    postjson("/szrecycle_mgr/index.php/Admin/DevMgr/getSubArea",req,function (data) {
                        if(!data||data.length==0){
                            $('#areaid2').html("");
                            return;
                        }
                        var html="<option value='0'>选择城市</option>"
                        for(var i=0;i<data.length;i++){
                            html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                        }

                        $('#areaid2').html(html);
                        $('#areaid3').html('');
                        $('#areaid4').html('');
                    })
                })

                $('#areaid2').on('change',function () {
                    var req={
                        areaid:$('#areaid2').val(),
                    }
                    postjson("/szrecycle_mgr/index.php/Admin/DevMgr/getSubArea",req,function (data) {
                        if(!data||data.length==0){
                            $('#areaid3').html("");
                            return;
                        }
                        var html="<option value='0'>选择区县</option>"
                        for(var i=0;i<data.length;i++){
                            html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                        }

                        $('#areaid3').html(html);
                        $('#areaid4').html('');
                    })
                })

                $('#areaid3').on('change',function () {
                    refreshArea4();
                })

                $('#addarea4').on('click',function () {
                    var areaid3 =$('#areaid3').val();
                    if(!areaid3){
                        toastr.error("请选择区县！");
                        return;
                    }
                    $('#addArea4Modal_area3id').val(areaid3);
                    $('#addArea4Modal').modal();
                })
                $('#addArea4Modal_commit').on('click',function () {
                    var req={
                        area3id:$('#addArea4Modal_area3id').val(),
                        areaname:$('#addArea4Modal_name').val()
                    }
                    postjson('/szrecycle_mgr/index.php/Admin/UserMgr/addarea4',req,function (data) {
                        $('#addArea4Modal').modal('hide');
                        refreshArea4();
                    })
                })
            };

            return oInit;
        };

        function refreshArea4() {
            var req={
                areaid:$('#areaid3').val(),
            }
            postjson("/szrecycle_mgr/index.php/Admin/DevMgr/getSubArea",req,function (data) {
                if(!data||data.length==0){
                    $('#areaid4').html("");
                    return;
                }
                var html="<option value='0'>选择街道</option>"
                for(var i=0;i<data.length;i++){
                    html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                }
                $('#areaid4').html(html);
            })
        }
    </script>
</body>

</html>