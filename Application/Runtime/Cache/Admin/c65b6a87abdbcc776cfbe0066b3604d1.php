<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <style>
        /* 监控按钮样式 */
        .btn-group.dropdown:not(.open) .caret {
            border-top: 4px dashed;
            border-bottom: 0;
        }
        .btn-group.dropdown.open .caret {
            border-top: 0;
            border-bottom: 4px dashed;
        }
    </style>
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>设备列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                     role="group">
                                    <button id="testBtn" type="button"
                                            class="btn btn-outline btn-default"
                                            onclick="script:window.location.href = '/index.php/Admin/DevMgr/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                    <input id="search_devcode" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="设备编码">
                                    <input id="search_devname" class="searchInput" style="margin-left: 3px;width: 100px;" placeholder="设备名称">
                                    <?php if(($search_operatorid_visible) == "1"): ?><select id="search_operatorid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="0">选择运营商</option>
                                            <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>

                                    <?php if(($search_propertyid_visible) == "1"): ?><select id="search_propertyid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="0">选择物业</option>
                                            <?php if(is_array($propertylist)): $i = 0; $__LIST__ = $propertylist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>
                                    <input id="search_communityname" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="社区名称">
                                    <select id="search_alarm" class="searchInput" style="margin-left: 3px;width: 90px;">
                                        <?php if(is_array($alarmlist)): $i = 0; $__LIST__ = $alarmlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["value"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                    </select>
                                    <button id="btn_search" type="button"
                                            class="btn btn-primary" style="margin-left: 3px;">搜索</button>
                                    <button id="btn_setTextMsg" type="button"
                                            class="btn btn-success" style="margin-left: 3px;">设置欢迎语</button>
                                    <button id="btn_setPlayID" type="button"
                                            class="btn btn-white" style="margin-left: 3px;">设置播放列表</button>
                                    <button id="btn_reboot" type="button"
                                            class="btn btn-danger" style="margin-left: 3px;">重启</button>
                                </div>
                                <table id="tb_bootstraptblist">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="qrcodeModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="qrcodeTitle" class="modal-title">二维码</h4>
                </div>
                <div class="modal-body">
                    <div id="qrcode" style="display: flex;justify-content: center;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <!--<button type="button" class="btn btn-primary">保存</button>-->
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="setTextMsgModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">设置欢迎语</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">内容：</label>
                            <div class="col-sm-10">
                                <textarea rows="5" id="setTextMsg_content" type="text" class="form-control"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="setTextMsg_commit" type="button" class="btn btn-primary">发送</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="setPlayIDModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">设置播放列表</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">播放列表：</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="setPlayID_playid">
                                    <option value="0">请选择</option>
                                    <?php if(is_array($playlist)): $i = 0; $__LIST__ = $playlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["id"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="setPlayIDModal_commit" type="button" class="btn btn-primary">发送</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 全局js -->
    <script src="/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/Public/js/qrcode.min.js"></script>
    <script src="/Public/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var qrcode = new QRCode('qrcode', {
            text: ' ',
            width: 256,
            height: 256,
            colorDark : '#000000',
            colorLight : '#ffffff',
            correctLevel : QRCode.CorrectLevel.H
        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_bootstraptblist').bootstrapTable({
                    url: '/index.php/Admin/DevMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 50,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: 'ID'
                    }, {
                        field: 'code',
                        title: '设备编码'
                    }, {
                        field: 'name',
                        title: '设备名称'
                    }, {
                        field: 'onlinestatusname',
                        title: '是否在线',
                        formatter: onlinestatusFormatter
                    }, {
                        field: 'isalarm',
                        title: '告警',
                        formatter: alarmstatusFormatter
                    },
                    //     {
                    //     field: 'collectusername',
                    //     title: '回收员'
                    // },
                        {
                            field: 'playname',
                            title: '播放列表'
                        },
                        {
                        field: 'propertyname',
                        title: '物业'
                    }, {
                        field: 'operatorname',
                        title: '运营商'
                    }, {
                        field: 'areaname',
                        title: '地域'
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                window.location.href = "DevMgr/detail?id="+this.nextSibling.innerText;
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "DevMgr/update?id="+this.previousSibling.innerText;
                            });
                        }

                        var alarmBtns = $("button[name='alarmBtn']");
                        for(var i=0;i<alarmBtns.length;i++){
                            alarmBtns[i].addEventListener("click",function(){
                                window.location.href = "AlarmMgr/index?devcode="+this.previousSibling.innerText;
                            });
                        }

                        var mkQRCodeBtn = $("button[name='mkQRCodeBtn']");
                        for(var i=0;i<mkQRCodeBtn.length;i++){
                            mkQRCodeBtn[i].addEventListener("click",function(){
                                // window.location.href = "DevMgr/update?id="+this.previousSibling.innerText;
                                var devcode=this.previousSibling.innerText;
                                var title=this.nextSibling.innerText;
                                var req = {
                                    devcode:devcode
                                }

                                $.ajax({
                                    url:'/index.php/Admin/DevMgr/getQRRandCode',
                                    type:'POST',
                                    async:true,
                                    data:JSON.stringify(req),
                                    timeout:5000,
                                    dataType:'json',
                                    beforeSend:function(xhr){
                                    },
                                    success:function(data,textStatus,jqXHR){
                                        console.log(data);
                                        if("0"!=data.retCode){
                                            toastr.error("获取失败！");
                                            return;
                                        }
                                        var code=data.data;
                                        $('#qrcodeTitle').text(title);
                                        qrcode.clear();
                                        qrcode.makeCode("https://recycle.jifu.club/jifurecycle/opendoor/"+devcode+"-"+code);
                                        $('#qrcodeModal').modal({keyboard:false})
                                        return;
                                    },
                                    error:function(xhr,textStatus){
                                        toastr.error("服务器异常！");
                                    },
                                });
                            });
                        }
                        
                        // 为监控下拉菜单中的按钮添加事件监听
                        var realTimeMonitorBtns = $("a[name='realTimeMonitorBtn']");
                        for(var i=0;i<realTimeMonitorBtns.length;i++){
                            realTimeMonitorBtns[i].addEventListener("click",function(e){
                                e.preventDefault();
                                var devcode = $(this).closest('.btn-group').prev().text();
                                toastr.info("实时监控功能即将上线，设备编码：" + devcode);
                                // 实际接口实现后替换下面的代码
                                // window.location.href = "MonitorMgr/realtime?devcode=" + devcode;
                            });
                        }
                        
                        var playbackBtns = $("a[name='playbackBtn']");
                        for(var i=0;i<playbackBtns.length;i++){
                            playbackBtns[i].addEventListener("click",function(e){
                                e.preventDefault();
                                var devcode = $(this).closest('.btn-group').prev().text();
                                toastr.info("回放功能即将上线，设备编码：" + devcode);
                                // 实际接口实现后替换下面的代码
                                // window.location.href = "MonitorMgr/playback?devcode=" + devcode;
                            });
                        }
                        
                        var screenshotBtns = $("a[name='screenshotBtn']");
                        for(var i=0;i<screenshotBtns.length;i++){
                            screenshotBtns[i].addEventListener("click",function(e){
                                e.preventDefault();
                                var devcode = $(this).closest('.btn-group').prev().text();
                                toastr.info("截屏功能即将上线，设备编码：" + devcode);
                                // 实际接口实现后替换下面的代码
                                // var req = {
                                //     devcode: devcode
                                // };
                                // $.ajax({
                                //     url:'/index.php/Admin/DevMgr/getScreenshot',
                                //     type:'POST',
                                //     async:true,
                                //     data:JSON.stringify(req),
                                //     timeout:5000,
                                //     dataType:'json',
                                //     success:function(data){
                                //         if("0"!=data.retCode){
                                //             toastr.error("截屏失败！");
                                //             return;
                                //         }
                                //         // 处理截屏结果
                                //     },
                                //     error:function(xhr,textStatus){
                                //         toastr.error("服务器异常！");
                                //     },
                                // });
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    devcode :$('#search_devcode').val(),
                    devname :$('#search_devname').val(),
                    operatorid :$('#search_operatorid').val(),
                    propertyid :$('#search_propertyid').val(),
                    alarm :$('#search_alarm').val(),
                    communityname :$('#search_communityname').val()
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:200px;text-align:center;">';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
            html += '<div style="display:none;">'+row['id']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">查看</button>';

            html += '<div style="display:none;">'+row['code']+'</div>';
            html += '<button name="mkQRCodeBtn" type="button" class="btn btn-info btn-sm" href="#" style="margin-left:3px;display: inline-block;">二维码</button>';
            html += '<div style="display:none;">'+row['name']+'</div>';

            html += '<div style="display:none;">'+row['code']+'</div>';
            html += '<button name="alarmBtn" type="button" class="btn btn-danger btn-sm" href="#" style="margin-left:3px;display: inline-block;">告警</button>';
            
            html += '<div style="display:none;">'+row['code']+'</div>';
            html += '<div class="btn-group dropdown" style="margin-left:3px;display: inline-block;" onmouseover="$(this).addClass(\'open\');" onmouseout="$(this).removeClass(\'open\');">';
            html += '  <button name="monitorBtn" type="button" class="btn btn-primary btn-sm">监控 <span class="caret"></span></button>';
            html += '  <ul class="dropdown-menu dropdown-menu-right">';
            html += '    <li><a href="#" name="realTimeMonitorBtn"><i class="fa fa-video-camera"></i> 实时监控</a></li>';
            html += '    <li><a href="#" name="playbackBtn"><i class="fa fa-play-circle"></i> 回放</a></li>';
            html += '    <li><a href="#" name="screenshotBtn"><i class="fa fa-camera"></i> 截屏</a></li>';
            html += '  </ul>';
            html += '</div>';

            html +='</div>';
            return html;
        }

        function onlinestatusFormatter(value, row, index) {
            var html = '';
            if(row['onlinestatus']==1){
                html += '<div style="width:40px;text-align:center;color: green">在线</div>';
            }else{
                html += '<div style="width:40px;text-align:center;color:gray">离线</div>';
            }
            return html;
        }

        function alarmstatusFormatter(value, row, index) {
            var html = '';
            if(row['isalarm']==1){
                html += '<div style="width:40px;text-align:center;color: red">有</div>';
            }else{
                html += '<div style="width:40px;text-align:center;color:gray">无</div>';
            }
            return html;
        }

        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }

                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].id);
                    }
                    var deleteReq = {
                        idlist:devs
                    }

                    $.ajax({
                        url:'/index.php/Admin/DevMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(deleteReq),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_bootstraptblist').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#search_operatorid').on('change',function () {
                    var operatorid = $('#search_operatorid').val();
                    if(operatorid){
                        getjson('/index.php/Admin/DevMgr/api_getPropertys?operatorid='+operatorid,function (data) {
                            // console.log(data)
                            if(!data||data.length==0){
                                $('#search_propertyid').html("");
                                return;
                            }
                            var html="<option value='0'>请选择物业</option>"
                            for(var i=0;i<data.length;i++){
                                html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                            }

                            $('#search_propertyid').html(html);
                        })
                    }
                })



                $('#btn_search').on("click",function () {
                    var opt = {
                        query:{
                            offset: 0,
                        }
                    };
                    $('#tb_bootstraptblist').bootstrapTable('refresh',opt);
                })

                $('#btn_setTextMsg').on('click',function () {
                    $('#setTextMsgModal').modal();
                })

                $('#btn_setPlayID').on('click',function () {
                    $('#setPlayIDModal').modal();
                })

                $('#setTextMsg_commit').on('click',function () {
                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:devs,
                        msgcontent:$('#setTextMsg_content').val()
                    }
                    postjson("/index.php/Admin/DevMgr/setTextMsg",req,function (data) {
                        toastr.success("发送成功！");
                        $('#setTextMsgModal').modal('hide');
                    })
                })

                $('#setPlayIDModal_commit').on('click',function () {
                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:devs,
                        playid:$('#setPlayID_playid').val()
                    }
                    postjson("/index.php/Admin/DevMgr/setPlayID",req,function (data) {
                        toastr.success("发送成功！");
                        $('#setPlayIDModal').modal('hide');
                    })
                })

                $('#btn_reboot').on('click',function () {
                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    if(!selectDevs||selectDevs.length==0){
                        toastr.error("请选择设备！");
                        return;
                    }
                    var r=confirm("确认重启?")
                    if (r!=true){
                        return;
                    }

                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].id);
                    }

                    var req = {
                        idlist:devs,
                    }
                    postjson("/index.php/Admin/DevMgr/reboot",req,function (data) {
                        toastr.success("发送成功！");
                    })
                })
            };

            return oInit;
        };
    </script>
</body>

</html>