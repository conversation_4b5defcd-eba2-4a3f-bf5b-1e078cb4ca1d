<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>投放记录</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                     role="group">
                                    <?php if(($search_operatorid_visible) == "1"): ?><button id="testBtn" type="button" style=""
                                            class="btn btn-outline btn-default"
                                            onclick="script:window.location.href = '/index.php/Admin/ThrowlogMgr/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button><?php endif; ?>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                    <select id="search_rubbishtype" class="searchInput" style="margin-left: 3px;width: 56px;">
                                        <option value="0">类型</option>
                                        <?php if(is_array($rubbishtypelist)): $i = 0; $__LIST__ = $rubbishtypelist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["rubbishtype"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                    </select>
                                    <input id="search_devcode" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="设备编码">
                                    <!--<input id="search_devname" class="searchInput" style="margin-left: 3px;width: 100px;" placeholder="设备名称">-->
                                    <input id="search_account" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="用户账号">
                                    <input id="search_cardnum" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="卡号">

                                    <input id="search_starttime" class="layer-date searchInput" style="margin-left: 3px;width: 116px;" placeholder="YYYY-MM-DD hh:mm:ss" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                                    <input id="search_endtime" class="layer-date searchInput" style="margin-left: 3px;width: 116px;" placeholder="YYYY-MM-DD hh:mm:ss" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                                    <?php if(($search_operatorid_visible) == "1"): ?><select id="search_operatorid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="0">选择运营商</option>
                                            <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>

                                    <?php if(($search_propertyid_visible) == "1"): ?><select id="search_propertyid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="0">选择物业</option>
                                            <?php if(is_array($propertylist)): $i = 0; $__LIST__ = $propertylist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>

                                    <select id="search_violationflag" class="searchInput" style="margin-left: 3px;width: 70px;">
                                        <option value="-1">选择违规</option>
                                        <?php if(is_array($violationflaglist)): $i = 0; $__LIST__ = $violationflaglist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["value"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                    </select>

                                    <button id="btn_search" type="button"
                                            class="btn btn-primary" style="margin-left: 3px;">搜索</button>
                                    <button id="btn_export" type="button"
                                            class="btn btn-default" style="margin-left: 3px;">导出</button>
                                </div>
                                <table id="tb_bootstraptblist">
                                </table>

                                <div>总积分：<span id="totalpoint">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;总重量：<span id="totalweight">0</span>克</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="qrcodeModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="qrcodeTitle" class="modal-title">二维码</h4>
                </div>
                <div class="modal-body">
                    <div id="qrcode" style="display: flex;justify-content: center;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <!--<button type="button" class="btn btn-primary">保存</button>-->
                </div>
            </div>
        </div>
    </div>
    <!-- 全局js -->
    <script src="/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/Public/js/plugins/layer/laydate/laydate.js"></script>
    <script src="/Public/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();
            initDate();
        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_bootstraptblist').bootstrapTable({
                    url: '/index.php/Admin/ThrowlogMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 20,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: false,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: 'ID'
                    }, {
                        field: 'devcode',
                        title: '设备编码'
                    }, {
                        field: 'devname',
                        title: '设备名称'
                    }, {
                        field: 'cardnum',
                        title: '卡号'
                    }, {
                        field: 'account',
                        title: '微信用户'
                    }, {
                        field: 'point',
                        title: '获得积分'
                    },  {
                        field: 'throwweight',
                        title: '重量或次数'
                    }, {
                        field: 'rubbishtypename',
                        title: '垃圾类型'
                    }, {
                        field: 'violationflagname',
                        title: '是否违规'
                    }, {
                        field: 'throwtime',
                        title: '投放时间'
                    }
                    // ,{
                    //     field: 'operate',
                    //     title: '操作',
                    //     width:'100px',
                    //     formatter: operateFormatter //自定义方法，添加操作按钮
                    // }
                    ],
                    onLoadSuccess:function(data){
                        // console.log(data)
                        $('#totalpoint').text(data.totalpoint);
                        $('#totalweight').text(data.totalweight);
                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "/index.php/Admin/ThrowlogMgr/update?id="+this.previousSibling.innerText;
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // name: params.search,
                    rubbishtype :$('#search_rubbishtype').val(),
                    devcode :$('#search_devcode').val(),
                    account :$('#search_account').val(),
                    cardnum :$('#search_cardnum').val(),
                    starttime :$('#search_starttime').val(),
                    endtime :$('#search_endtime').val(),
                    operatorid :$('#search_operatorid').val(),
                    propertyid :$('#search_propertyid').val(),
                    violationflag :$('#search_violationflag').val(),
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:160px;text-align:center;">';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
            html += '<div style="display:none;">'+row['id']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">查看</button>';
            html +='</div>';
            return html;
        }

        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }

                    var selectIds = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var ids = new Array();
                    for(var i=0;i<selectIds.length;i++){
                        ids.push(selectIds[i].id);
                    }
                    var deleteReq = {
                        idlist:ids
                    }

                    $.ajax({
                        url:'/index.php/Admin/ThrowlogMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(deleteReq),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_bootstraptblist').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#search_operatorid').on('change',function () {
                    var operatorid = $('#search_operatorid').val();
                    if(operatorid){
                        getjson('/index.php/Admin/ThrowlogMgr/api_getPropertys?operatorid='+operatorid,function (data) {
                            // console.log(data)
                            if(!data||data.length==0){
                                $('#search_propertyid').html("");
                                return;
                            }
                            var html="<option value='0'>请选择物业</option>"
                            for(var i=0;i<data.length;i++){
                                html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                            }

                            $('#search_propertyid').html(html);
                        })
                    }
                })



                $('#btn_search').on("click",function () {
                    var opt = {
                        query:{
                            offset: 0,
                        }
                    };
                    $('#tb_bootstraptblist').bootstrapTable('refresh',opt);
                })



                $('#btn_export').on("click",function () {
                    var param="";
                    param=param+"rubbishtype="+$('#search_rubbishtype').val();
                    param=param+"&devcode="+$('#search_devcode').val();
                    param=param+"&account="+$('#search_account').val();
                    param=param+"&cardnum="+$('#search_cardnum').val();
                    param=param+"&starttime="+$('#search_starttime').val();
                    param=param+"&endtime="+$('#search_endtime').val();
                    param=param+"&operatorid="+$('#search_operatorid').val();
                    param=param+"&propertyid="+$('#search_propertyid').val();
                    window.open("/index.php/Admin/ThrowlogMgr/api_export?"+param);
                });
            };

            return oInit;
        };
        function initDate() {
            $('#search_starttime').val(laydate.now(-90, 'YYYY-MM-DD hh:mm:ss'));
            $('#search_endtime').val(laydate.now(1, 'YYYY-MM-DD hh:mm:ss'));
        }
    </script>
</body>

</html>