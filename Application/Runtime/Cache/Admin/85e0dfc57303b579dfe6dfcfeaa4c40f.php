<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改密码</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;display: none;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form id="modpasswdform" class="form-horizontal">
                        <div class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">用户ID：</label>

                            <div class="col-sm-10">
                                <input id="userid" name="userid" type="text" readonly="readonly" class="form-control" value="<?php echo ($userid); ?>">
                            </div>
                        </div>
                        <div class="form-group" style="">
                            <label class="col-sm-2 control-label">用户账号：</label>

                            <div class="col-sm-10">
                                <input id="account" name="account" type="text" readonly="readonly" class="form-control" value="<?php echo ($account); ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">旧登录密码：</label>

                            <div class="col-sm-10">
                                <input id="olduserpwd" name="olduserpwd" type="password" class="form-control" placeholder="请输入旧密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">新登录密码：</label>

                            <div class="col-sm-10">
                                <input id="userpwd" name="userpwd" type="password" class="form-control" placeholder="请输入新密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">密码确认：</label>

                            <div class="col-sm-10">
                                <input id="userpwd1" name="userpwd1" type="password" class="form-control" placeholder="请重复输入新密码">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>

                    <div id="modresult" style="display: none;">
                        <span class="fa fa-check" style="color:green"></span>
                        修改成功
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="/Public/js/jquery.min.js?v=2.1.4"></script>
<script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="/Public/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="/Public/js/plugins/iCheck/icheck.min.js"></script>
<script src="/Public/js/sha256.min.js"></script>
<script src="/Public/js/plugins/toastr/toastr.min.js"></script>
<script src="/Public/js/md5.js"></script>
<script src="/Public/js/sha256.min.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var olduserpwd=$("#olduserpwd").val();
            var userpwd=$("#userpwd").val();
            var userpwd1=$("#userpwd1").val();
            if(olduserpwd.length==0){
                toastr.error("请输入老密码");
                return;
            }
            if(userpwd!=userpwd1){
                toastr.error("密码确认不一致");
                return;
            }

            if(userpwd.length<6){
                toastr.error("密码最短6位");
                return;
            }

            var req = {
                userid:$("#userid").val(),
                olduserpwd:sha256($("#olduserpwd").val()),
                userpwd:sha256($("#userpwd").val()),
                userpwd1:$("#userpwd").val(),
            };
            $.ajax({
                url:'api_modpasswd',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    toastr.success("修改成功");
                    $('#modpasswdform').css("display","none");
                    $('#modresult').css("display","block");
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

</script>


</body>

</html>