<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>卡用户列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_listEventsToolbar"
                                     role="group">
                                    <?php if(($_SESSION['roleid']) != "6"): ?><button id="testBtn" type="button"
                                            class="btn btn-outline btn-default"
                                            onclick="script:window.location.href = '/index.php/Admin/CardMgr/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button" style="margin-left: 5px;"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                    <button id="btn_import" type="button" class="btn btn-info"
                                            style="margin-left: 5px;">批量导入</button>
                                    <div style="float: left; margin-left: 5px;"><a href="/Public/template/template_importcard1.xlsx">模板文件下载</a></div>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="btn_exportqrcode" type="button" class="btn btn-primary"
                                            style="margin-left: 15px;display:none;">导出二维码</button><?php endif; ?>
                                    <button id="btn_exportdudaotoudiqrcode" type="button" class="btn btn-primary"
                                            style="margin-left: 15px;display: none">导出督导投递二维码</button>
                                    </neq>
                                    <input id="search_cardnumber" class="searchInput" style="margin-left: 3px;width: 90px;" placeholder="卡号">
                                    <?php if(($search_operatorid_visible) == "1"): ?><select id="search_operatorid" class="searchInput" style="margin-left: 3px;width: 130px;">
                                            <option value="0">选择运营商</option>
                                            <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>

                                    <?php if(($search_propertyid_visible) == "1"): ?><select id="search_propertyid" class="searchInput" style="margin-left: 3px;width: 90px;">
                                            <option value="0">选择物业</option>
                                            <?php if(is_array($propertylist)): $i = 0; $__LIST__ = $propertylist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                        </select><?php endif; ?>

                                    <?php if(($search_visible) == "1"): ?><button id="btn_search" type="button"
                                            class="btn btn-primary" style="margin-left: 3px;">搜索</button><?php endif; ?>
                                    <?php if(($_SESSION['roleid']) != "6"): ?><button id="addViolationBtn" class="btn btn-danger" type="button"
                                            style="margin-left: 3px;display:none;">添加违规</button>
                                    <button id="setDishonestflagBtn" class="btn btn-success" type="button"
                                            style="margin-left: 3px;">设为失信人</button>
                                    <button id="clearDishonestflagBtn" class="btn btn-default" type="button"
                                            style="margin-left: 3px;">取消失信人</button><?php endif; ?>
                                </div>
                                <table id="tb_list">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal_uploadfile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">批量导入</h4>
                </div>
                <div class="ibox-content">
                <form class="form-horizontal" >
                    <?php if(($search_operatorid_visible) == "1"): ?><div class="form-group">
                            <label class="col-sm-2 control-label">运营商：</label>
                            <div class="col-sm-10">
                                <select id="operatorid" class="form-control m-b" >
                                    <option value="0">请选择运营商</option>
                                    <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div><?php endif; ?>

                    <?php if(($search_propertyid_visible) == "1"): ?><div class="form-group">
                            <label class="col-sm-2 control-label">物业：</label>
                            <div class="col-sm-10">
                                <select id="propertyid" class="form-control m-b" >
                                    <option value="0">选择物业</option>
                                    <?php if(is_array($propertylist)): $i = 0; $__LIST__ = $propertylist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>"><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div><?php endif; ?>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">上传文件：</label>
                        <div class="col-sm-10">
                            <input id="file" name="file" type="file" class="form-control m-b" >
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align: center">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button id="btn_uploadfile" type="button" class="btn btn-primary">提交</button>
                    </div>
                </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>

    <div class="modal fade" id="myModal">
        <div class="modal-dialog modal-sm" style="text-align: center;margin:205px auto">

            <img alt="" src="/Public/img/wite.gif" style="width:70px;"/>
            <p id="uploadprogress" style="padding-top:10px;font-size:14px;color:#FF9600"></p>
        </div>
    </div>

    <div class="modal inmodal fade" id="qrcodeModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="qrcodeTitle" class="modal-title">二维码</h4>
                </div>
                <div class="modal-body">
                    <div id="qrcode" style="display: flex;justify-content: center;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <!--<button type="button" class="btn btn-primary">保存</button>-->
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="addViolationModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">添加违规记录</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">违规描述：</label>
                            <div class="col-sm-10">
                                <textarea rows="5" id="addViolation_remark" type="text" class="form-control"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">计分：</label>
                            <div class="col-sm-10">
                                <input id="addViolation_point" type="text" class="form-control">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="addViolation_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="costPointModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">消费积分</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <input id="costPointModal_id" value="" style="display: none" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">本次消费积分：</label>
                            <div class="col-sm-10">
                                <input id="costPointModal_point" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>
                            <div class="col-sm-10">
                                <textarea rows="5" id="costPointModal_remark" type="text" class="form-control"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="costPointModal_commit" type="button" class="btn btn-primary">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/Public/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="/Public/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="/Public/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="/Public/js/demo/bootstrap-table-demo.js"></script>
    <script src="/Public/js/plugins/toastr/toastr.min.js"></script>
    <script src="/Public/js/qrcode.min.js"></script>
    <script src="/Public/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

            $('#operatorid').on('change',function () {
                var operatorid = $('#operatorid').val();
                if(operatorid){
                    getjson('/index.php/Admin/CardMgr/api_getPropertys?operatorid='+operatorid,function (data) {
                        // console.log(data)
                        if(!data||data.length==0){
                            $('#propertyid').html("");
                            return;
                        }
                        var html="<option value='0'>请选择物业</option>"
                        for(var i=0;i<data.length;i++){
                            html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                        }

                        $('#propertyid').html(html);
                    })
                }
            })

            $("#btn_exportqrcode").on("click",function(){
                var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                var users = new Array();
                for(var i=0;i<selectDevs.length;i++){
                    users.push(selectDevs[i].id);
                }
                var req = {
                    idlist:users
                }
                postjson('/index.php/Admin/CardMgr/api_exportqrzipfile',req,function (data) {
                    console.log(data)
                    window.open(data);
                })
            });

            $("#btn_exportdudaotoudiqrcode").on("click",function(){
                var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                var users = new Array();
                for(var i=0;i<selectDevs.length;i++){
                    users.push(selectDevs[i].cardnumber);
                }
                var req = {
                    cardnumberlist:users
                }
                postjson('/index.php/Admin/CardMgr/api_exportdudaotoudiqrzipfile',req,function (data) {
                    console.log(data)
                    window.open(data);
                })
            });

            $("#btn_import").on("click",function(){
                $('#file').val("");
                $('#modal_uploadfile').modal({
                    keyboard: false
                });
            });
            $("#btn_uploadfile").on("click",function(){
                UpladFile();
            });

            var xhr;
            function UpladFile() {
                $('#myModal').modal({backdrop:'static',keyboard:false});

                var fileObj = document.getElementById("file").files[0]; // js 获取文件对象
                var url =  "/index.php/Admin/CardMgr/importFile"; // 接收上传文件的后台地址

                var form = new FormData(); // FormData 对象
                form.append("picture", fileObj); // 文件对象
                form.append("operatorid", $('#operatorid').val()); // 文件对象
                form.append("propertyid", $('#propertyid').val()); // 文件对象

                xhr = new XMLHttpRequest();  // XMLHttpRequest 对象
                xhr.open("post", url, true); //post方式，url为服务器请求地址，true 该参数规定请求是否异步处理。
                xhr.onload = uploadComplete; //请求完成
                xhr.onerror =  uploadFailed; //请求失败

                // xhr.upload.onprogress = progressFunction;//【上传进度调用方法实现】
                // xhr.upload.onloadstart = function(){
                //     ot = new Date().getTime();   //设置上传开始时间
                //     oloaded = 0;//设置上传开始时，以上传的文件大小为0
                // };

                xhr.send(form); //开始上传，发送form数据
            }
            //上传成功响应
            function uploadComplete(evt) {
                $('#myModal').modal('hide');
                //服务断接收完文件返回的结果
                console.log(evt.target.responseText);
                var data = JSON.parse(evt.target.responseText);
                if(data.retCode=="0") {
                    $('#modal_uploadfile').modal('hide');
                    toastr.success("导入成功！");
                    $('#tb_list').bootstrapTable('refresh');
                }else{
                    toastr.error("导入失败！");
                }
            }
            //上传失败
            function uploadFailed(evt) {
                $('#myModal').modal('hide');
                toastr.error("上传失败！");
            }
            //取消上传
            function cancleUploadFile(){
                xhr.abort();
            }
        });

        var qrcode = new QRCode('qrcode', {
            text: ' ',
            width: 256,
            height: 256,
            colorDark : '#000000',
            colorLight : '#ffffff',
            correctLevel : QRCode.CorrectLevel.H
        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_list').bootstrapTable({
                    url: '/index.php/Admin/CardMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 100,                       //每页的记录行数（*）
                    pageList: [100, 200, 500, 1000],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: false,                  //是否显示所有的列
                    showRefresh: false,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: 'ID'
                    }, {
                        field: 'cardnumber',
                        title: '卡号'
                    }, {
                        field: 'name',
                        title: '名称'
                    }, {
                        field: 'phonenum',
                        title: '手机号'
                    }, {
                        field: 'point',
                        title: '积分'
                    }, {
                        field: 'operatorname',
                        title: '所属运营商'
                    }, {
                        field: 'propertyname',
                        title: '所属物业'
                    },
                    //     {
                    //     field: 'featurename',
                    //     title: '人脸是否存在'
                    // },
                        {
                        field: 'dishonestflagname',
                        title: '是否失信'
                    },
                        {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                window.location.href = "devicedetail.do?id="+this.nextSibling.innerText;
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "/index.php/Admin/CardMgr/update?id="+this.previousSibling.innerText;
                            });
                        }

                        var mkQRCodeBtn = $("button[name='mkQRCodeBtn']");
                        for(var i=0;i<mkQRCodeBtn.length;i++){
                            mkQRCodeBtn[i].addEventListener("click",function(){
                                // window.location.href = "DevMgr/update?id="+this.previousSibling.innerText;
                                var cardqrcode=this.previousSibling.innerText;
                                var title=this.nextSibling.innerText;
                                $('#qrcodeTitle').text(title);
                                qrcode.clear();
                                qrcode.makeCode(cardqrcode);
                                $('#qrcodeModal').modal({keyboard:false})

                            });
                        }

                        var costBtn = $("button[name='costBtn']");
                        for(var i=0;i<costBtn.length;i++){
                            costBtn[i].addEventListener("click",function(){
                                // window.location.href = "DevMgr/update?id="+this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                $('#costPointModal_id').val(id);
                                $('#costPointModal').modal({keyboard:false})
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                    operatorid :$('#search_operatorid').val(),
                    propertyid :$('#search_propertyid').val(),
                    cardnumber :$('#search_cardnumber').val(),
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:160px;text-align:center;">';
            if(<?php echo (session('roleid')); ?>!=6){
                html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
                html += '<div style="display:none;">'+row['id']+'</div>';
                html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">修改</button>';

                html += '<div style="display:none;">'+row['cardqrcode']+'</div>';
                html += '<button name="mkQRCodeBtn" type="button" class="btn btn-info btn-sm" href="#" style="margin-left:3px;display: none;">二维码</button>';
                html += '<div style="display:none;">'+row['name']+'</div>';
            }else{
                html += '<button name="costBtn" type="button" class="btn btn-info btn-sm" href="#" style="">消费积分</button>';
                html += '<div style="display:none;">'+row['id']+'</div>';
            }
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:users
                    }

                    $.ajax({
                        url:'/index.php/Admin/CardMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#search_operatorid').on('change',function () {
                    var operatorid = $('#search_operatorid').val();
                    if(operatorid){
                        getjson('/index.php/Admin/CardMgr/api_getPropertys?operatorid='+operatorid,function (data) {
                            // console.log(data)
                            if(!data||data.length==0){
                                $('#search_propertyid').html("");
                                return;
                            }
                            var html="<option value='0'>请选择物业</option>"
                            for(var i=0;i<data.length;i++){
                                html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                            }

                            $('#search_propertyid').html(html);
                        })
                    }
                })

                $('#btn_search').on("click",function () {
                    var opt = {
                        query:{
                            offset: 0,
                        }
                    };
                    $('#tb_list').bootstrapTable('refresh',opt);
                })
                $('#addViolationBtn').on('click',function () {
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    if(selectDevs.length==0){
                        toastr.error("请选择用户！");
                        return;
                    }
                    $('#addViolationModal').modal();
                })
                $('#addViolation_commit').on('click',function () {
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:users,
                        remark:$('#addViolation_remark').val(),
                        point:$('#addViolation_point').val(),
                        usertype:1
                    }

                    $.ajax({
                        url:'/index.php/Admin/ViolationMgr/api_addViolation',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("操作失败！");
                                return;
                            }

                            toastr.success("操作成功！");
                            $('#addViolationModal').modal('hide');
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                })

                $('#setDishonestflagBtn').on('click',function () {
                    setDishonestflag(1);
                })

                $('#clearDishonestflagBtn').on('click',function () {
                    setDishonestflag(0);
                })

                $('#costPointModal_commit').on('click',function () {
                    var req={
                        id:$('#costPointModal_id').val(),
                        point:$('#costPointModal_point').val(),
                        remark:$('#costPointModal_remark').val(),
                    }
                    postjson('/index.php/Admin/CardMgr/costPoint',req,function (data) {
                        $('#costPointModal').modal('hide');
                        $('#tb_list').bootstrapTable('refresh');
                    })
                })
            };

            return oInit;
        };

        function setDishonestflag(flag) {
            var selectDevs = $('#tb_list').bootstrapTable('getSelections');
            if(selectDevs.length==0){
                toastr.error("请选择用户！");
                return;
            }
            var users = new Array();
            for(var i=0;i<selectDevs.length;i++){
                users.push(selectDevs[i].id);
            }
            var req = {
                idlist:users,
                flag:flag,
                usertype:1
            }

            $.ajax({
                url:'/index.php/Admin/ViolationMgr/api_setDishonestflag',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                beforeSend:function(xhr){
                },
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！");
                        return;
                    }

                    toastr.success("操作成功！");
                    $('#tb_list').bootstrapTable('refresh');
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        }
    </script>
</body>

</html>