<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    <title>积富回收平台</title>
    <meta name="keywords" content="管理登录">
    <meta name="description" content="管理登录">
    <link href="/Public/css/bootstrap.min.css" rel="stylesheet">
    <link href="/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/Public/css/animate.css" rel="stylesheet">
    <link href="/Public/css/style.css" rel="stylesheet">
    <link href="/Public/css/login.css" rel="stylesheet">
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <script>
        if (window.top !== window.self) {
            window.top.location = window.location;
        }
    </script>

</head>

<body class="signin">
    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-7">
                <div class="signin-info"  style="display: none">
                    <div class="logopanel m-b">
                        <h1>[ H+ ]</h1>
                    </div>
                    <div class="m-b"></div>
                    <h4>欢迎使用 <strong>积富回收平台</strong></h4>
                    <ul class="m-b">
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 优势一</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 优势二</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 优势三</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 优势四</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 优势五</li>
                    </ul>
                    <strong>还没有账号？ <a href="#">立即注册&raquo;</a></strong>
                </div>
            </div>
            <div class="col-sm-5">
                <form id="loginForm" method="post" action="/index.php/Admin/Login/login">
                    <h3 class="no-margins">积富回收平台</h3>
                    <!--<p class="m-t-md">废多多后台管理系统</p>-->
                    <input name="account" type="text" class="form-control uname" placeholder="账号" />
                    <input id="password" name="password" type="password" class="form-control pword m-b" placeholder="密码" />
                    <!--<a href="">忘记密码了？</a>-->
                    <?php if(!empty($errorMsg)){ ?>
                        <div style="color: #ff0000"><?php echo ($errorMsg); ?></div>
                    <?php } ?>
                    <button id="loginBtn" class="btn btn-success btn-block" type="button">登录</button>
                </form>
            </div>
        </div>
        <div class="signup-footer">
            <div class="pull-left">
                &copy; 2018 All Rights Reserved.
            </div>
        </div>
    </div>

    <script src="/Public/js/jquery.min.js?v=2.1.4"></script>
    <script src="/Public/js/sha256.min.js"></script>
    <script>
        $("#loginBtn").on("click",function(){
            $("#password").val(sha256($("#password").val()));
            $("#loginForm").submit();
        });
    </script>
</body>

</html>