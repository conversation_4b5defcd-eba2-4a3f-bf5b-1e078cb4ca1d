<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="/szrecycle_mgr/Public/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/animate.css" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="/szrecycle_mgr/Public/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改物业用户</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-3 control-label">ID：</label>

                            <div class="col-sm-9">
                                <input id="userid" type="text" readonly="readonly" class="form-control" value="<?php echo ($user["userid"]); ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账号：</label>

                            <div class="col-sm-9">
                                <input id="account" type="text" class="form-control" value="<?php echo ($user["account"]); ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">名称：</label>

                            <div class="col-sm-9">
                                <input id="name" type="text" class="form-control" value="<?php echo ($user["name"]); ?>">
                            </div>
                        </div>
                        <?php if(($canmodoperator) == "1"): ?><div class="form-group">
                            <label class="col-sm-3 control-label">运营商：</label>
                            <div class="col-sm-9">
                                <select id="operatoruserid" class="form-control m-b" disabled="disabled">
                                <option value="0">请选择运营商</option>
                                <?php if(is_array($operatorlist)): $i = 0; $__LIST__ = $operatorlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo ($vo["userid"]); ?>" <?php if(($user["operatoruserid"]) == $vo["userid"]): ?>selected<?php endif; ?>><?php echo ($vo["name"]); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div><?php endif; ?>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动开关机时间段：</label>
                            <div class="col-sm-9">
                                <input id="autopoweronoff" type="text" class="form-control" value="<?php echo ($user["autopoweronoff"]); ?>" placeholder="示例：5:00-10:00,16:00-23:00">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">自动开关垃圾箱投口时间段：</label>
                            <div class="col-sm-9">
                                <input id="autodooropenclose" type="text" class="form-control" value="<?php echo ($user["autodooropenclose"]); ?>" placeholder="示例：7:00-9:00,17:00-20:00">
                            </div>
                        </div>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动调节屏幕亮度时间点：</label>
                            <div class="col-sm-9">
                                <input id="autoadjustlight" type="text" class="form-control" value="<?php echo ($user["autoadjustlight"]); ?>" placeholder="示例：10:00-90,18:00-60">
                            </div>
                        </div>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动调节音量时间点：</label>
                            <div class="col-sm-9">
                                <input id="autoadjustvolume" type="text" class="form-control" value="<?php echo ($user["autoadjustvolume"]); ?>" placeholder="示例：10:00-90,18:00-60">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">登录密码：</label>

                            <div class="col-sm-9">
                                <input id="userpwd" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">密码确认：</label>

                            <div class="col-sm-9">
                                <input id="userpwd1" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <div class="modal inmodal fade" id="orgTreeModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">选择所属组织</h4>
                </div>
                <div class="modal-body">
                    <div id="orgTreeview" class="test"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">取消</button>
                    <button id="btnOrgSave" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function initOrgzTree() {
            postjson("/szrecycle_mgr/index.php/Admin/OrgzMgr/api_getOrgs","",function (data) {
                // console.log(JSON.stringify(data));
                $('#orgTreeview').treeview({
                    data: data,
                    showCheckbox: false,
                    onNodeSelected: function (event, node) {
                    }
                });
            });
        }

        function getAllParent(node) {
            if(!node){
                return;
            }
            var parentNode = $('#orgTreeview').treeview('getParent', node);
            // console.log(parentNode)
            if(parentNode&&parentNode.dataid){
                var parentNode1 = getAllParent(parentNode);
                // console.log(parentNode1)
                if(parentNode1){
                    return parentNode1+" > "+node.text;
                }else{
                    return parentNode.text+" > "+node.text;
                }
            }else{
                return node.text;
            }
        }
    </script>


<!-- 全局js -->
<script src="/szrecycle_mgr/Public/js/jquery.min.js?v=2.1.4"></script>
<script src="/szrecycle_mgr/Public/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="/szrecycle_mgr/Public/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="/szrecycle_mgr/Public/js/plugins/iCheck/icheck.min.js"></script>
<script src="/szrecycle_mgr/Public/js/sha256.min.js"></script>
<script src="/szrecycle_mgr/Public/js/plugins/toastr/toastr.min.js"></script>
<script src="/szrecycle_mgr/Public/js/md5.js"></script>
<script src="/szrecycle_mgr/Public/js/plugins/treeview/bootstrap-treeview.js"></script>
<script src="/szrecycle_mgr/Public/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        initSelect($('#roleid'));
        $("#saveBtn").on("click",function(){
            var userpwd=$("#userpwd").val();
            var userpwd1=$("#userpwd1").val();
            if(userpwd!=userpwd1){
                toastr.error("确认密码不一致！ ");
                return;
            }
            if(userpwd.length==0){
                userpwd='';
            }else{
                userpwd=sha256(userpwd);
            }
            // var selected_orgId = $('#selected_orgId').val();
            // if(!selected_orgId){
            //     toastr.error("请选择所属组织结构！");
            //     return;
            // }

            var req = {
                userid:$("#userid").val(),
                account:$("#account").val(),
                name:$("#name").val(),
                userpwd:userpwd,
                userpwd1:$("#userpwd").val(),
                roleid:$("#roleid").val(),
                operatoruserid:$("#operatoruserid").val(),
                autopoweronoff:$("#autopoweronoff").val(),
                autodooropenclose:$("#autodooropenclose").val(),
                autoadjustlight:$("#autoadjustlight").val(),
                autoadjustvolume:$("#autoadjustvolume").val(),
            };
            $.ajax({
                url:'api_update_property',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        $('#roleid').on('change',function () {
            initSelect($(this));
        });
        $('#btn_setorg').on('click',function () {
            $('#orgTreeModal').modal({keyboard:false})
            initOrgzTree();
        })
        $('#btnOrgSave').on('click',function () {
            var select = $('#orgTreeview').treeview('getSelected');
            if(select.length!=1){
                toastr.error("请先选择一个节点！");
                return;
            }
            var id=select[0].dataid;
            var name=select[0].text;
            // console.log(getAllParent(select[0]))
            $('#orgTreeModal').modal('hide');

            // console.log("/szrecycle_mgr/index.php/Admin/OrgzMgr/api_getOrgPath?id="+id)
            var req={
                id:id
            }
            postjson("/szrecycle_mgr/index.php/Admin/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
            $('#selected_orgId').val(id);
        })

        var id=$('#selected_orgId').val();
        if(id&&id!=0){
            var req={
                id:id
            }
            postjson("/szrecycle_mgr/index.php/Admin/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
        }
    });

    function initSelect($roleid) {
        if($roleid.val()=='1'||$roleid.val()=='2'||$roleid.val()=='7'){
            $('#div_orgid').css("display",'none');
            $('#selected_orgId').val("");
            // $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='3'){
            $('#div_orgid').css("display",'none');
            $('#selected_orgId').val("");
            // $('#div_manufacturerid').css("display",'block');
        }else if($roleid.val()=='4'){
            $('#div_orgid').css("display",'none');
            $('#selected_orgId').val("");
            // $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='5'){
            $('#div_orgid').css("display",'block');
            // $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='6'){
            $('#div_orgid').css("display",'block');
            // $('#div_manufacturerid').css("display",'none');
        }else{
            $('#div_orgid').css("display",'none');
            $('#selected_orgId').val("");
            // $('#div_manufacturerid').css("display",'none');
        }
    }

</script>


</body>

</html>