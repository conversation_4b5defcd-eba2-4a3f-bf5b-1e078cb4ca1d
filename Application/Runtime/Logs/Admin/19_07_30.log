[ 2019-07-30T09:08:05+08:00 ] 127.0.0.1 /szrecycle_mgr/
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.177000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.130000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.130000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.576000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.576000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.020000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.020000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.008000s ]
INFO: [ app_end ] --END-- [ RunTime:0.008000s ]

[ 2019-07-30T09:08:31+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Login/login
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0360s ]
SQL: SELECT * FROM `think_user` WHERE ( account='admin' ) LIMIT 1   [ RunTime:0.0270s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0660s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=1 ) LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid='1' ) LIMIT 1   [ RunTime:0.0150s ]
INFO: 后台登录成功 admin

[ 2019-07-30T09:08:32+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0140s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0170s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0370s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0400s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0360s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0420s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0700s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0430s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0410s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0360s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.044000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.044000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T09:08:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.067000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.068000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T09:08:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/favicon.ico
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T10:31:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0140s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.1330s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.1160s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.1210s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0910s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0210s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.1430s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0540s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0070s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.012000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.012000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T10:31:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.008000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.008000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:56:23+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.0500s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0610s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0140s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.046000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.046000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.002000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:56:24+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0150s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0340s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374iK1s8YI_HgDTcA0ZBexeAw","lat":null,"lng":null,"createtime":"2019-07-19 15:52","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374rQcFCLMaaACbVd3R9_wgWw","lat":null,"lng":null,"createtime":"2019-07-17 11:16","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374tnjKRHULu8Y1EDDekhrq28","lat":null,"lng":null,"createtime":"2019-07-16 19:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-30T14:56:26+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/point?id=*********
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_register` WHERE ( id='*********' ) LIMIT 1   [ RunTime:0.0150s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.073000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.074000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:56:26+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getpointlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.0110s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_registerproperty` WHERE `userid` = ********* LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_registerproperty` WHERE `userid` = ********* ORDER BY id desc LIMIT 0,10   [ RunTime:0.0130s ]
INFO: {"total":"0","rows":[]}

[ 2019-07-30T14:56:27+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0140s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0170s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374iK1s8YI_HgDTcA0ZBexeAw","lat":null,"lng":null,"createtime":"2019-07-19 15:52","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374rQcFCLMaaACbVd3R9_wgWw","lat":null,"lng":null,"createtime":"2019-07-17 11:16","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374tnjKRHULu8Y1EDDekhrq28","lat":null,"lng":null,"createtime":"2019-07-16 19:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-30T14:56:31+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0180s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 10,10   [ RunTime:0.0200s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374oMSMZXu09xh7n3gCRFu2Yg","lat":null,"lng":null,"createtime":"2019-07-12 09:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hSZ8hagJ7Bsk23jc_N2008","lat":null,"lng":null,"createtime":"2019-07-11 17:28","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374uBJTPh5zMXAYk5xJ5UUHmc","lat":null,"lng":null,"createtime":"2019-07-11 10:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374l82sBxouM6mMSvJMvJKI9s","lat":null,"lng":null,"createtime":"2019-07-07 20:29","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374gHtVwW2GzRfY8MlAP6efFs","lat":null,"lng":null,"createtime":"2019-07-06 17:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374ttC0tLEjiDTO-z58LCUjQs","lat":null,"lng":null,"createtime":"2019-07-06 16:58","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374kD1g_xq1ZD68ACSwKhfBq0","lat":null,"lng":null,"createtime":"2019-07-06 11:47","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-30T14:56:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/CardMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=46 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0300s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0170s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c48db5bf41f875a614d869570d42ce7d.php 第 58 行.
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c48db5bf41f875a614d869570d42ce7d.php 第 104 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.031000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.031000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:56:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/CardMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0150s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_card` WHERE `status` = 0 LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_card` WHERE `status` = 0 ORDER BY id desc LIMIT 0,100   [ RunTime:0.0510s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0370s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0340s ]
INFO: {"total":"2","rows":[{"id":"565","cardnumber":"0011046394","operatorid":"38","propertyid":"39","status":"0","point":"5","updatetimepoint":"1563501202","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"fingerprint":null,"createtime":"2019-07-19 09:53","updatetime":"1563501202","name":"anxin01","dishonestflag":"0","operatorname":"op1","propertyname":"pt1","cardqrcode":"000000565001","dishonestflagname":"\u6b63\u5e38","featurename":"\u5426"},{"id":"564","cardnumber":"0011069186","operatorid":"38","propertyid":"39","status":"0","point":"975","updatetimepoint":"1562893899","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"fingerprint":null,"createtime":"2019-07-12 09:11","updatetime":"1562893899","name":"lai","dishonestflag":"0","operatorname":"op1","propertyname":"pt1","cardqrcode":"000000564001","dishonestflagname":"\u6b63\u5e38","featurename":"\u5426"}]}

[ 2019-07-30T14:56:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/point?id=*********
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0350s ]
SQL: SELECT * FROM `think_register` WHERE ( id='*********' ) LIMIT 1   [ RunTime:0.0150s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.017000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:56:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getpointlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.0130s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_registerproperty` WHERE `userid` = ********* LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_registerproperty` WHERE `userid` = ********* ORDER BY id desc LIMIT 0,10   [ RunTime:0.0140s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_card` WHERE ( id=564 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
NOTIC: [8] Undefined index: cardmasterflag D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\RegisterMgrController.class.php 第 214 行.
INFO: {"total":"1","rows":[{"id":"7","userid":"*********","propertyid":"39","operatorid":"38","carduserid":"564","point":"-89","roleid":"1","selected":"1","cardmasterflag":"1","rolename":"\u56de\u6536\u5458","cardnum":"0011069186","propertyname":"pt1","operatorname":"op1","cardmasterflagname":"\u5426"}]}

[ 2019-07-30T14:57:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0210s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0130s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374iK1s8YI_HgDTcA0ZBexeAw","lat":null,"lng":null,"createtime":"2019-07-19 15:52","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374rQcFCLMaaACbVd3R9_wgWw","lat":null,"lng":null,"createtime":"2019-07-17 11:16","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374tnjKRHULu8Y1EDDekhrq28","lat":null,"lng":null,"createtime":"2019-07-16 19:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-30T14:57:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.1190s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 10,10   [ RunTime:0.0120s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374oMSMZXu09xh7n3gCRFu2Yg","lat":null,"lng":null,"createtime":"2019-07-12 09:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hSZ8hagJ7Bsk23jc_N2008","lat":null,"lng":null,"createtime":"2019-07-11 17:28","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374uBJTPh5zMXAYk5xJ5UUHmc","lat":null,"lng":null,"createtime":"2019-07-11 10:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374l82sBxouM6mMSvJMvJKI9s","lat":null,"lng":null,"createtime":"2019-07-07 20:29","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374gHtVwW2GzRfY8MlAP6efFs","lat":null,"lng":null,"createtime":"2019-07-06 17:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374ttC0tLEjiDTO-z58LCUjQs","lat":null,"lng":null,"createtime":"2019-07-06 16:58","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374kD1g_xq1ZD68ACSwKhfBq0","lat":null,"lng":null,"createtime":"2019-07-06 11:47","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-30T14:57:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/point?id=*********
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` WHERE ( id='*********' ) LIMIT 1   [ RunTime:0.0330s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.019000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.020000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-30T14:57:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getpointlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.0110s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_registerproperty` WHERE `userid` = ********* LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_registerproperty` WHERE `userid` = ********* ORDER BY id desc LIMIT 0,10   [ RunTime:0.0470s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_card` WHERE ( id=564 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0180s ]
NOTIC: [8] Undefined index: cardmasterflag D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\RegisterMgrController.class.php 第 214 行.
INFO: {"total":"1","rows":[{"id":"7","userid":"*********","propertyid":"39","operatorid":"38","carduserid":"564","point":"-89","roleid":"1","selected":"1","cardmasterflag":"1","rolename":"\u56de\u6536\u5458","cardnum":"0011069186","propertyname":"pt1","operatorname":"op1","cardmasterflagname":"\u5426"}]}

