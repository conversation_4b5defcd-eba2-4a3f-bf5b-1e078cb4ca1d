[ 2019-08-10T16:37:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.009000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.009000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:37:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0130s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.0120s ]

[ 2019-08-10T16:37:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-08-10T16:37:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0190s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1533890276 group by throwmonth [ RunTime:0.0130s ]

[ 2019-08-10T16:37:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0410s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1533890276 group by throwmonth [ RunTime:0.0170s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0220s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1533890276 group by rubbishtype order by weight desc [ RunTime:0.0160s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0100s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0140s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1562834277 group by throwdate order by throwdate asc [ RunTime:0.0200s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0120s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1533890277 group by propertyid order by weight desc [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0140s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0190s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0100s ]
SQL: select count(1) as num from think_dev where heartbeattime>1565426157 [ RunTime:0.0090s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0110s ]
SQL: select count(1) as num,propertyid from think_throwlog where violationflag=1 and createtime>1533890277 group by propertyid order by num desc [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0170s ]

[ 2019-08-10T16:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0100s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0110s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1533890277 order by id desc [ RunTime:0.0130s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1533890277 order by id desc [ RunTime:0.0090s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0100s ]

[ 2019-08-10T16:37:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0210s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_dev` WHERE `status` = 1 ORDER BY id asc  [ RunTime:0.0210s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='19070217Y401' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='19070217Y402' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='18123017Y801' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0090s ]

[ 2019-08-10T16:38:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:39:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0110s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0090s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0220s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u79ef\u5206\u6d88\u8d39","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.012000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.012000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:39:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.008000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.008000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.002000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-08-10T16:39:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ThrowlogMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rubbishtypepoint` ORDER BY rubbishtype asc  [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0120s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\94da3bd6e4d2d066fbd0e09154af74ee.php 第 63 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:39:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ThrowlogMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0110s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_throwlog` WHERE (  throwtime >=1557650379 and throwtime<1565426479  ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_throwlog` WHERE (  throwtime >=1557650379 and throwtime<1565426479  ) ORDER BY id desc LIMIT 0,20   [ RunTime:0.0120s ]
SQL: SELECT SUM(point) AS tp_sum FROM `think_throwlog` WHERE (  throwtime >=1557650379 and throwtime<1565426479  ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE (  throwtime >=1557650379 and throwtime<1565426479  ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0090s ]
INFO: {"total":"57","totalpoint":"5989","totalweight":"29649","rows":[{"id":"2241","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"3","throwtime":"2019-08-07 14:18:28","totalweight":"0","throwweight":"30","doorno":"3","throwmode":"5","createtime":"2019-08-07 14:18:29","rubbishtype":"A","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u5851\u6599\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2240","devcode":"19070217Y401","userid":"*********","carduserid":"0","point":"25","throwtime":"2019-08-06 10:23:11","totalweight":"0","throwweight":"50","doorno":"2","throwmode":"5","createtime":"2019-08-06 10:23:11","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2239","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"30","throwtime":"2019-08-01 09:22:25","totalweight":"0","throwweight":"60","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:22:26","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2238","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"15","throwtime":"2019-08-01 09:22:13","totalweight":"0","throwweight":"30","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:22:15","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2237","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"20","throwtime":"2019-08-01 09:21:47","totalweight":"0","throwweight":"40","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:21:48","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2236","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"4","throwtime":"2019-07-26 16:46:43","totalweight":"0","throwweight":"20","doorno":"1","throwmode":"5","createtime":"2019-07-26 16:46:43","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2235","devcode":"19070217Y401","userid":"*********","carduserid":"0","point":"35","throwtime":"2019-07-26 15:33:37","totalweight":"0","throwweight":"70","doorno":"4","throwmode":"5","createtime":"2019-07-26 15:33:36","rubbishtype":"6","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u7eb8\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2234","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"20","throwtime":"2019-07-24 15:19:11","totalweight":"0","throwweight":"40","doorno":"2","throwmode":"5","createtime":"2019-07-24 15:19:11","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2233","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"325","throwtime":"2019-07-24 15:18:56","totalweight":"0","throwweight":"650","doorno":"2","throwmode":"5","createtime":"2019-07-24 15:18:56","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2232","devcode":"18092717Y909","userid":"*********","carduserid":"0","point":"0","throwtime":"2019-07-24 15:17:24","totalweight":"0","throwweight":"196","doorno":"3","throwmode":"5","createtime":"2019-08-12 03:44:04","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"1","violationreportuserid":"*********","violationreporttime":"**********","rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u8fdd\u89c4"},{"id":"2231","devcode":"18092717Y909","userid":"*********","carduserid":"0","point":"0","throwtime":"2019-07-24 13:09:44","totalweight":"0","throwweight":"203","doorno":"0","throwmode":"0","createtime":"2019-08-12 01:36:24","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"1","violationreportuserid":"*********","violationreporttime":"**********","rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u8fdd\u89c4"},{"id":"2230","devcode":"18092717Y909","userid":"*********","carduserid":"0","point":"0","throwtime":"2019-07-23 15:01:14","totalweight":"0","throwweight":"200","doorno":"0","throwmode":"7","createtime":"2019-08-09 00:35:47","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2229","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"8","throwtime":"2019-07-23 14:13:54","totalweight":"0","throwweight":"40","doorno":"1","throwmode":"5","createtime":"2019-07-23 14:13:53","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2228","devcode":"19070217Y401","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 16:48:05","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 16:48:05","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"},{"id":"2227","devcode":"19070217Y401","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 16:44:06","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 16:44:06","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"},{"id":"2226","devcode":"19070217Y402","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 15:53:16","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 15:53:17","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"},{"id":"2225","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"4","throwtime":"2019-07-19 15:52:04","totalweight":"0","throwweight":"20","doorno":"1","throwmode":"5","createtime":"2019-07-19 15:52:04","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u73bb\u7483\u7c7b","violationflagname":"\u6b63\u5e38"},{"id":"2224","devcode":"19070217Y402","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 15:47:54","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 15:47:54","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"},{"id":"2223","devcode":"19070217Y402","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 15:15:21","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 15:15:22","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"},{"id":"2222","devcode":"19070217Y401","userid":"0","carduserid":"0","point":"0","throwtime":"2019-07-19 14:58:19","totalweight":"0","throwweight":"0","doorno":"0","throwmode":"0","createtime":"2019-07-19 14:58:19","rubbishtype":"","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":null,"cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"","violationflagname":"\u6b63\u5e38"}]}

[ 2019-08-10T16:39:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.009000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.009000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:39:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0130s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.0120s ]

[ 2019-08-10T16:39:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:39:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0190s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1533890390 group by throwmonth [ RunTime:0.0210s ]

[ 2019-08-10T16:39:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0130s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1533890390 group by throwmonth [ RunTime:0.0150s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0200s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1533890390 group by rubbishtype order by weight desc [ RunTime:0.0170s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0120s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0190s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1562834391 group by throwdate order by throwdate asc [ RunTime:0.0130s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0110s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1533890391 group by propertyid order by weight desc [ RunTime:0.0140s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0230s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0130s ]
SQL: select count(1) as num from think_dev where heartbeattime>1565426271 [ RunTime:0.0110s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0160s ]
SQL: select count(1) as num,propertyid from think_throwlog where violationflag=1 and createtime>1533890391 group by propertyid order by num desc [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0130s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0140s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0160s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1533890391 order by id desc [ RunTime:0.0130s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1533890391 order by id desc [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]

[ 2019-08-10T16:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_dev` WHERE `status` = 1 ORDER BY id asc  [ RunTime:0.0120s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='19070217Y401' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='19070217Y402' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE ( devcode='18123017Y801' and createtime>=1565280000 and createtime<1565366400 ) LIMIT 1   [ RunTime:0.0110s ]

[ 2019-08-10T16:40:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0120s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u79ef\u5206\u6d88\u8d39","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.021000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.021000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T16:40:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-08-10T17:16:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0110s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0070s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u79ef\u5206\u6d88\u8d39","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.013000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T17:16:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T17:16:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_play` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_play` ORDER BY id desc  [ RunTime:0.0250s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c65b6a87abdbcc776cfbe0066b3604d1.php 第 54 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.044000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.044000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-10T17:16:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0120s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_dev` LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_dev` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_alarm` [ RunTime:0.0490s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18123017Y801' and status=0 )  [ RunTime:0.0100s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0200s ]
SQL: SHOW COLUMNS FROM `think_play` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y402' and status=0 )  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y401' and status=0 )  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y909' and status=0 )  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y901' and status=0 )  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
INFO: {"total":"5","rows":[{"id":"104","code":"18123017Y801","name":"18123017Y801","status":"1","lat":null,"lng":null,"address":"888","heartbeattime":"1564301438","createtime":"2019-07-20 09:25:42","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"1935","areaid2":"1959","areaid3":"1962","areaid4":"0","areaname":"\u5357\u5c71\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":"100","playid":"23","onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"103","code":"19070217Y402","name":"\u5b89\u6b46\u56db\u7bb12\u53f7","status":"1","lat":"31.303858000000","lng":"120.666560000000","address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1565175184","createtime":"2019-07-08 17:11:40","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"150","playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1"},{"id":"102","code":"19070217Y401","name":"\u5b89\u6b46\u56db\u7bb11\u53f7","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1565175194","createtime":"2019-07-08 17:10:58","georefreshtime":null,"isfull":"1","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"100","playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":1,"propertyname":"pt1","operatorname":"op1"},{"id":"46","code":"18092717Y909","name":"18092717Y909","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u5bbf\u8fc1\u5e02","heartbeattime":"1565428628","createtime":"2019-06-03 11:31:46","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"0","areaid2":"0","areaid3":"0","areaid4":"0","areaname":"","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5475\u5475\uff0c\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u4f60\uff0c\u5475\u5475","households":"0","playid":"23","onlinestatusname":"\u5728\u7ebf","onlinestatus":1,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"9","code":"18092717Y901","name":"\u5bbf\u8fc1\u6d4b\u8bd51","status":"1","lat":"33.893920000000","lng":"118.337630000000","address":"\u6c5f\u82cf\u5bbf\u8fc1","heartbeattime":"1561697348","createtime":"2019-03-18 14:08:57","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"areaname":null,"autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":null,"playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1"}]}

[ 2019-08-10T17:16:41+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/getQRRandCode
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_dev` WHERE ( code='18092717Y909' ) LIMIT 1   [ RunTime:0.0100s ]
INFO: getrandcode res={"msg":"success","randcode":"35569764","responseId":"123","status":"success"}

[ 2019-08-10T17:28:14+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/getQRRandCode
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_dev` WHERE ( code='18092717Y909' ) LIMIT 1   [ RunTime:0.0120s ]
INFO: getrandcode res={"msg":"success","randcode":"55710202","responseId":"123","status":"success"}

[ 2019-08-10T17:31:27+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/getQRRandCode
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_dev` WHERE ( code='18092717Y909' ) LIMIT 1   [ RunTime:0.0160s ]
INFO: getrandcode res={"msg":"success","randcode":"92337503","responseId":"123","status":"success"}

