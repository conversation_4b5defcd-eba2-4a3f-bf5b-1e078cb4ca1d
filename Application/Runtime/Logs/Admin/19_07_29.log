[ 2019-07-29T09:06:01+08:00 ] 127.0.0.1 /szrecycle_mgr/
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.021000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.003000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.096000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.096000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T09:06:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Login/login
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0560s ]
SQL: SELECT * FROM `think_user` WHERE ( account='admin' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=1 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid='1' ) LIMIT 1   [ RunTime:0.0130s ]
INFO: 后台登录成功 admin

[ 2019-07-29T09:06:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0100s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0210s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.052000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.052000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T09:06:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.078000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.078000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T09:06:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/favicon.ico
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T10:40:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr?roleid=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0420s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=32 ) LIMIT 1   [ RunTime:0.0160s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.046000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.046000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T10:40:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 2 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0510s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=2 ) LIMIT 1   [ RunTime:0.0410s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0100s ]
INFO: {"total":"1","rows":[{"userid":"38","account":"op1","name":"op1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"2","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:48:56","orgid":null,"operatoruserid":null,"userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u8fd0\u8425\u5546","flagname":"\u6b63\u5e38"}]}

[ 2019-07-29T10:58:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.030000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.030000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0140s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.0130s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.003000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0170s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1532833089 group by rubbishtype order by weight desc [ RunTime:0.0180s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0130s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0160s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1532833089 group by throwmonth [ RunTime:0.0150s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0120s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1561777089 group by throwdate order by throwdate asc [ RunTime:0.0150s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0140s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1532833089 group by throwmonth [ RunTime:0.0190s ]

[ 2019-07-29T10:58:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0140s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1532833089 group by propertyid order by weight desc [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]

[ 2019-07-29T10:58:10+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0200s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0190s ]
SQL: select count(1) as num from think_dev where heartbeattime>1564368969 [ RunTime:0.0150s ]

[ 2019-07-29T10:58:10+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0150s ]
SQL: select count(1) as num,propertyid from think_violation where createtime>1532833090 group by propertyid order by num desc [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0370s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=77 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=49 ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=47 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0080s ]

[ 2019-07-29T10:58:10+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0140s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1532833090 order by id desc [ RunTime:0.0180s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1532833090 order by id desc [ RunTime:0.0540s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]

[ 2019-07-29T10:58:10+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0220s ]
SQL: select userid,name from think_user where flag=1 and roleid=3 order by userid desc [ RunTime:0.0170s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0240s ]

[ 2019-07-29T10:59:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:00:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:01:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:02:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:03:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:04:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:05:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:06:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T11:07:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T11:08:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:09:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:10:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:11:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:12:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:13:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:14:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:15:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:16:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:17:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:18:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:19:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:20:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:21:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:22:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:23:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:24:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:25:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:26:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:27:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:28:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:29:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:30:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:31:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:32:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:33:05+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:34:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:35:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:36:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:37:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:38:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:39:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:40:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:41:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:42:04+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:43:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:44:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:45:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:46:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:47:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:48:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:49:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:50:03+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:51:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:52:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:53:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:54:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:55:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:56:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:57:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T11:58:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T11:59:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:00:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:01:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:02:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:03:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:04:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:05:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:06:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:07:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:08:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:09:01+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:10:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:11:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:12:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:13:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:14:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:15:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:16:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:17:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:18:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:19:00+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:19:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:20:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:21:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:22:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:23:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:24:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:25:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:26:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:27:59+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:28:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:29:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:30:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:31:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:32:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:33:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:34:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:35:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:36:58+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:37:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:38:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:39:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:40:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:41:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:42:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:43:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:44:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:45:57+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T12:46:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:47:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:48:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:49:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:50:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:51:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:52:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:53:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:54:56+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:55:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:56:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T12:57:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:58:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T12:59:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:00:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:01:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:02:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:03:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:04:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:05:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:06:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:07:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:08:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:09:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:10:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:11:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:12:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:13:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:14:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:15:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:16:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:17:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:18:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:19:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:20:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:21:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:22:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:23:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:24:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:25:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:26:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:27:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:28:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.002000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:29:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:30:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:31:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:32:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:33:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:34:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:35:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:36:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:37:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:38:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:39:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:40:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:41:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:42:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:43:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:44:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:45:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:46:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:47:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:48:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:49:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:50:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:51:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:52:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:53:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:54:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:55:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T13:56:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:57:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:58:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T13:59:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:00:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:01:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:02:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:03:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:04:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:05:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:06:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T14:07:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:08:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:09:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:10:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:11:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:12:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:13:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:14:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:15:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:16:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:17:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:18:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:19:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:20:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:21:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:22:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:23:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:24:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T14:25:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:26:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:27:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:28:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:29:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:30:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:31:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:32:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:33:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:34:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:35:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:36:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:37:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:38:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:39:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:40:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:41:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:42:45+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:43:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:44:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:45:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:46:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:47:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:48:44+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:49:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:49:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0400s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.0440s ]

[ 2019-07-29T14:49:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:49:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0510s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1532846976 group by throwmonth [ RunTime:0.0220s ]

[ 2019-07-29T14:49:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0160s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1532846976 group by throwmonth [ RunTime:0.0110s ]

[ 2019-07-29T14:49:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0200s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1561790976 group by throwdate order by throwdate asc [ RunTime:0.0360s ]

[ 2019-07-29T14:49:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0240s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1532846976 group by rubbishtype order by weight desc [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0110s ]

[ 2019-07-29T14:49:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0150s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1532846977 group by propertyid order by weight desc [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0180s ]

[ 2019-07-29T14:49:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0180s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0130s ]
SQL: select count(1) as num from think_dev where heartbeattime>1564382857 [ RunTime:0.0140s ]

[ 2019-07-29T14:49:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0140s ]
SQL: select count(1) as num,propertyid from think_violation where createtime>1532846977 group by propertyid order by num desc [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0330s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=77 ) LIMIT 1   [ RunTime:0.0400s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=49 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=47 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0140s ]

[ 2019-07-29T14:49:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0170s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0530s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1532846977 order by id desc [ RunTime:0.0230s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1532846977 order by id desc [ RunTime:0.0210s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]

[ 2019-07-29T14:49:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0270s ]
SQL: select userid,name from think_user where flag=1 and roleid=3 order by userid desc [ RunTime:0.0270s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0470s ]

[ 2019-07-29T14:50:36+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:51:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:52:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:53:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:54:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:55:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:56:35+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T14:56:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.013000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:56:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:56:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0150s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.1530s ]

[ 2019-07-29T14:56:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0360s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1561791412 group by throwdate order by throwdate asc [ RunTime:0.0370s ]

[ 2019-07-29T14:56:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0110s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1532847412 group by throwmonth [ RunTime:0.0100s ]

[ 2019-07-29T14:56:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0130s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1532847413 group by throwmonth [ RunTime:0.0460s ]

[ 2019-07-29T14:56:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0200s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1532847413 group by rubbishtype order by weight desc [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0620s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0490s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0300s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0320s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0100s ]

[ 2019-07-29T14:56:53+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0350s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1532847413 group by propertyid order by weight desc [ RunTime:0.0720s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0140s ]

[ 2019-07-29T14:56:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0940s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0160s ]
SQL: select count(1) as num from think_dev where heartbeattime>1564383293 [ RunTime:0.0110s ]

[ 2019-07-29T14:56:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0180s ]
SQL: select count(1) as num,propertyid from think_violation where createtime>1532847414 group by propertyid order by num desc [ RunTime:0.0400s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0570s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0540s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=77 ) LIMIT 1   [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=49 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=47 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0320s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0810s ]

[ 2019-07-29T14:56:54+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0210s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0190s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1532847414 order by id desc [ RunTime:0.0270s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1532847414 order by id desc [ RunTime:0.0300s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0160s ]

[ 2019-07-29T14:56:55+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0310s ]
SQL: select userid,name from think_user where flag=1 and roleid=3 order by userid desc [ RunTime:0.0440s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0150s ]

[ 2019-07-29T14:57:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:58:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T14:59:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:00:52+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:01:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:02:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:03:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:04:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:05:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:06:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:07:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:08:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:09:51+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:10:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:11:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:12:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:13:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:14:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:15:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:16:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:17:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:18:50+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:19:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:20:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:21:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:22:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:23:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:24:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:25:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:26:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:27:49+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:28:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:29:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:30:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:31:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:32:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:33:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:34:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:35:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:36:48+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:37:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:38:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/dashboard
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: SESSION检测成功 
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.010000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.010000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:38:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/api_getDevlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0140s ]
SQL: SELECT `id`,`name`,`lat`,`lng`,`heartbeattime` FROM `think_dev` WHERE ( lat is not null and lng is not null and lat!=0 and lng!=0 )  [ RunTime:0.0180s ]

[ 2019-07-29T15:38:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getCanyurenshu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0120s ]
SQL: select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>1532849918 group by throwmonth [ RunTime:0.0110s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getFenleitongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0130s ]
SQL: select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>1532849919 group by rubbishtype order by weight desc [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='0' ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint`  [ RunTime:0.0130s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getToufangcishu
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0140s ]
SQL: select count(1) as cishu,throwmonth from think_throwlog where createtime>1532849919 group by throwmonth [ RunTime:0.0130s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbianhua
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0120s ]
SQL: select sum(throwweight) as weight,throwdate from think_throwlog where createtime>1561793919 group by throwdate order by throwdate asc [ RunTime:0.0120s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequlajipaiming
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0250s ]
SQL: select sum(throwweight) as weight,propertyid from think_throwlog where createtime>1532849919 group by propertyid order by weight desc [ RunTime:0.0310s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0300s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0440s ]

[ 2019-07-29T15:38:39+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardData
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0240s ]
SQL: select count(1) as num from think_dev [ RunTime:0.0540s ]
SQL: select count(1) as num from think_dev where heartbeattime>1564385799 [ RunTime:0.0130s ]

[ 2019-07-29T15:38:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShequweifatongji
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0130s ]
SQL: select count(1) as num,propertyid from think_violation where createtime>1532849919 group by propertyid order by num desc [ RunTime:0.0240s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=77 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=49 ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=47 ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0090s ]

[ 2019-07-29T15:38:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getShixinren
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0250s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0130s ]
SQL: select name,propertyid from think_register where dishonestflag=1 and createtime>1532849920 order by id desc [ RunTime:0.0130s ]
SQL: select name,propertyid from think_card where dishonestflag=1 and createtime>1532849920 order by id desc [ RunTime:0.0360s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]

[ 2019-07-29T15:38:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getZongliangbudabiao
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0240s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_dev` WHERE `status` = 1 ORDER BY id asc  [ RunTime:0.0130s ]
ERR: 1054:Unknown column 'throwweight' in 'field list'
 [ SQL语句 ] : SELECT SUM(throwweight) AS tp_sum FROM `think_user` WHERE ( devcode='19070217Y401' and createtime>=1564243200 and createtime<1564329600 ) LIMIT 1  
ERR: 1054:Unknown column 'throwweight' in 'field list'
 [ SQL语句 ] : SELECT SUM(throwweight) AS tp_sum FROM `think_user` WHERE ( devcode='19070217Y401' and createtime>=1564243200 and createtime<1564329600 ) LIMIT 1  

[ 2019-07-29T15:39:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:40:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:41:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:42:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:43:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:44:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:45:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:46:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:47:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:48:37+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Dashboard/getDashboardTime
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:49:24+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0320s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0200s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0330s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0780s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0780s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0820s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.1180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.1000s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0760s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0850s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0630s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0620s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.1060s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0540s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:49:24+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.026000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.026000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:49:26+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/logout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:49:26+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Login/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.028000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.028000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:50:20+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Login/login
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( account='admin' ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=1 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid='1' ) LIMIT 1   [ RunTime:0.0150s ]
INFO: 后台登录成功 admin

[ 2019-07-29T15:50:20+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0130s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0270s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0360s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0080s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.026000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.026000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.002000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:50:21+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:50:29+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0470s ]
SQL: SHOW COLUMNS FROM `think_play` [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_play` ORDER BY id desc  [ RunTime:0.0280s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c65b6a87abdbcc776cfbe0066b3604d1.php 第 54 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.034000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.034000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:50:30+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0360s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_dev` LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_dev` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_alarm` [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18123017Y801' and status=0 )  [ RunTime:0.0180s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_play` [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0220s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y402' and status=0 )  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y401' and status=0 )  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0290s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y909' and status=0 )  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0550s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y901' and status=0 )  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0140s ]
INFO: {"total":"5","rows":[{"id":"104","code":"18123017Y801","name":"18123017Y801","status":"1","lat":null,"lng":null,"address":"888","heartbeattime":"1564301438","createtime":"2019-07-20 09:25:42","georefreshtime":null,"isfull":"0","collectuserid":null,"propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"1935","areaid2":"1959","areaid3":"1962","areaid4":null,"areaname":"\u5357\u5c71\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":"100","playid":"23","onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"103","code":"19070217Y402","name":"\u5b89\u6b46\u56db\u7bb12\u53f7","status":"1","lat":"31.303974000000","lng":"120.666530000000","address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1564386672","createtime":"2019-07-08 17:11:40","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"150","playid":null,"onlinestatusname":"\u5728\u7ebf","onlinestatus":1,"isalarm":0,"propertyname":"pt1","operatorname":"op1"},{"id":"102","code":"19070217Y401","name":"\u5b89\u6b46\u56db\u7bb11\u53f7","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1564133534","createtime":"2019-07-08 17:10:58","georefreshtime":null,"isfull":"1","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"100","playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":1,"propertyname":"pt1","operatorname":"op1"},{"id":"46","code":"18092717Y909","name":"18092717Y909","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u5bbf\u8fc1\u5e02","heartbeattime":"1564020899","createtime":"2019-06-03 11:31:46","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"0","areaid2":"0","areaid3":"0","areaid4":"0","areaname":"","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5475\u5475\uff0c\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u4f60\uff0c\u5475\u5475","households":"0","playid":"23","onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"9","code":"18092717Y901","name":"\u5bbf\u8fc1\u6d4b\u8bd51","status":"1","lat":"33.893920000000","lng":"118.337630000000","address":"\u6c5f\u82cf\u5bbf\u8fc1","heartbeattime":"1561697348","createtime":"2019-03-18 14:08:57","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"areaname":null,"autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":null,"playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1"}]}

[ 2019-07-29T15:50:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/update?id=104
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_dev` WHERE ( id='104' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.0470s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( roleid=2 )  [ RunTime:0.0130s ]
SQL: SELECT DISTINCT  `userid` FROM `think_registerproperty` WHERE ( roleid=1 )  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=-1 )  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=1935 )  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=1959 )  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=1962 )  [ RunTime:0.0200s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.064000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.064000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.003000s ]

[ 2019-07-29T15:50:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getdevparam
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_dev` WHERE ( id=104 ) LIMIT 1   [ RunTime:0.0170s ]
INFO: getdevparam res={"retCode":"1001","retMessage":"设备不在线"}

[ 2019-07-29T15:50:33+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getPropertys?operatorid=38
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( operatoruserid=38 )  [ RunTime:0.0110s ]

[ 2019-07-29T15:50:38+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0160s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_dev` LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_dev` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_alarm` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18123017Y801' and status=0 )  [ RunTime:0.0160s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_play` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y402' and status=0 )  [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0260s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='19070217Y401' and status=0 )  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y909' and status=0 )  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_play` WHERE ( id=23 ) LIMIT 1   [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_alarm` WHERE ( devcode='18092717Y901' and status=0 )  [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0340s ]
INFO: {"total":"5","rows":[{"id":"104","code":"18123017Y801","name":"18123017Y801","status":"1","lat":null,"lng":null,"address":"888","heartbeattime":"1564301438","createtime":"2019-07-20 09:25:42","georefreshtime":null,"isfull":"0","collectuserid":null,"propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"1935","areaid2":"1959","areaid3":"1962","areaid4":null,"areaname":"\u5357\u5c71\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":"100","playid":"23","onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"103","code":"19070217Y402","name":"\u5b89\u6b46\u56db\u7bb12\u53f7","status":"1","lat":"31.303974000000","lng":"120.666530000000","address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1564386672","createtime":"2019-07-08 17:11:40","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"150","playid":null,"onlinestatusname":"\u5728\u7ebf","onlinestatus":1,"isalarm":0,"propertyname":"pt1","operatorname":"op1"},{"id":"102","code":"19070217Y401","name":"\u5b89\u6b46\u56db\u7bb11\u53f7","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u7701\u82cf\u5dde\u5e02","heartbeattime":"1564133534","createtime":"2019-07-08 17:10:58","georefreshtime":null,"isfull":"1","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"areaname":"\u5434\u4e2d\u533a","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u60a8","households":"100","playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":1,"propertyname":"pt1","operatorname":"op1"},{"id":"46","code":"18092717Y909","name":"18092717Y909","status":"1","lat":null,"lng":null,"address":"\u6c5f\u82cf\u5bbf\u8fc1\u5e02","heartbeattime":"1564020899","createtime":"2019-06-03 11:31:46","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":"0","areaid2":"0","areaid3":"0","areaid4":"0","areaname":"","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":"\u5475\u5475\uff0c\u5b89\u6b46\u56de\u6536\u5b9d\u6b22\u8fce\u4f60\uff0c\u5475\u5475","households":"0","playid":"23","onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1","playname":"10010"},{"id":"9","code":"18092717Y901","name":"\u5bbf\u8fc1\u6d4b\u8bd51","status":"1","lat":"33.893920000000","lng":"118.337630000000","address":"\u6c5f\u82cf\u5bbf\u8fc1","heartbeattime":"1561697348","createtime":"2019-03-18 14:08:57","georefreshtime":null,"isfull":"0","collectuserid":"0","propertyid":"39","operatorid":"38","rublishtypes":"9,8,A,6","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"areaname":null,"autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"welcomemsg":null,"households":null,"playid":null,"onlinestatusname":"\u79bb\u7ebf","onlinestatus":0,"isalarm":0,"propertyname":"pt1","operatorname":"op1"}]}

[ 2019-07-29T15:50:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/update?id=103
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_dev` WHERE ( id='103' ) LIMIT 1   [ RunTime:0.0240s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0230s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( roleid=2 )  [ RunTime:0.0130s ]
SQL: SELECT DISTINCT  `userid` FROM `think_registerproperty` WHERE ( roleid=1 )  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_register` WHERE ( id=********* ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=-1 )  [ RunTime:0.0280s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=810 )  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=849 )  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_area` WHERE ( parentid=851 )  [ RunTime:0.0120s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.025000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.025000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.003000s ]

[ 2019-07-29T15:50:41+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getdevparam
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_dev` [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_dev` WHERE ( id=103 ) LIMIT 1   [ RunTime:0.0160s ]
INFO: getdevparam res={"retCode":"0","retMessage":"success","data":{"apkversion":"3.3.1 HY_SuZhouAnXin 20190716.2047","autodooropenclose":"00:02-23:59","createTime":1564386691206,"devcode":"19070217Y402","devcodeReport":"19070217Y402","devversion":"eng.wangxingli.20190228.172954","heartbeatinterval":30,"httpip":"*************","httpport":18052,"tcpip":"*************","tcpport":8077}}

[ 2019-07-29T15:50:41+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/DevMgr/api_getPropertys?operatorid=38
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0420s ]
SQL: SELECT * FROM `think_user` WHERE ( operatoruserid=38 )  [ RunTime:0.0120s ]

[ 2019-07-29T15:51:07+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/moduserinfo
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0130s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.049000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.050000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T15:51:20+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr?roleid=3
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=30 ) LIMIT 1   [ RunTime:0.0100s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.007000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:51:21+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0160s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0160s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0400s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0340s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0430s ]
INFO: {"total":"2","rows":[{"userid":"93","account":"pt2","name":"pt2","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":null,"cityid":null,"lat":null,"lng":null,"createtime":"2019-07-24 16:55:02","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"},{"userid":"39","account":"pt1","name":"pt1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:49:24","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"00:02-23:59","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"}]}

[ 2019-07-29T15:51:23+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr?roleid=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=32 ) LIMIT 1   [ RunTime:0.0110s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:51:23+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 2 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=2 ) LIMIT 1   [ RunTime:0.0200s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0160s ]
INFO: {"total":"1","rows":[{"userid":"38","account":"op1","name":"op1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"2","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:48:56","orgid":null,"operatoruserid":null,"userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":null,"autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u8fd0\u8425\u5546","flagname":"\u6b63\u5e38"}]}

[ 2019-07-29T15:52:21+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr?roleid=3
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=30 ) LIMIT 1   [ RunTime:0.0160s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.002000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.002000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:52:22+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0170s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0220s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0140s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0140s ]
INFO: {"total":"2","rows":[{"userid":"93","account":"pt2","name":"pt2","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":null,"cityid":null,"lat":null,"lng":null,"createtime":"2019-07-24 16:55:02","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"},{"userid":"39","account":"pt1","name":"pt1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:49:24","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"00:02-23:59","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"}]}

[ 2019-07-29T15:53:11+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr?roleid=3
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=30 ) LIMIT 1   [ RunTime:0.0160s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.007000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T15:53:11+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0080s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
INFO: {"total":"2","rows":[{"userid":"93","account":"pt2","name":"pt2","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":null,"cityid":null,"lat":null,"lng":null,"createtime":"2019-07-24 16:55:02","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"},{"userid":"39","account":"pt1","name":"pt1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:49:24","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"00:02-23:59","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"}]}

[ 2019-07-29T15:53:13+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/update?userid=93
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid='93' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0210s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_user` WHERE ( roleid=2 )  [ RunTime:0.0110s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.055000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.055000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.002000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T15:53:16+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/UserMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0140s ]
SQL: SELECT COUNT(userid) AS tp_count FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 3 AND `account` <> 'admin' ORDER BY userid desc LIMIT 0,10   [ RunTime:0.0900s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0240s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0250s ]
SQL: SHOW COLUMNS FROM `think_area` [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=3 ) LIMIT 1   [ RunTime:0.0280s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0150s ]
INFO: {"total":"2","rows":[{"userid":"93","account":"pt2","name":"pt2","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":null,"cityid":null,"lat":null,"lng":null,"createtime":"2019-07-24 16:55:02","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"},{"userid":"39","account":"pt1","name":"pt1","userpwd":"8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92","roleid":"3","status":"1","phonenumber":"***********","cityid":null,"lat":null,"lng":null,"createtime":"2019-04-20 13:49:24","orgid":null,"operatoruserid":"38","userpwd1":"123456","flag":"0","autopoweronoff":null,"autodooropenclose":"00:02-23:59","autoadjustlight":null,"autoadjustvolume":null,"propertyid":null,"areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"rolename":"\u7269\u4e1a","flagname":"\u6b63\u5e38","operatorname":"op1"}]}

[ 2019-07-29T23:49:15+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0470s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.0510s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0450s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.057000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.057000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.002000s ]
INFO: [ app_end ] --END-- [ RunTime:0.002000s ]

[ 2019-07-29T23:49:16+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0460s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0460s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374iK1s8YI_HgDTcA0ZBexeAw","lat":null,"lng":null,"createtime":"2019-07-19 15:52","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374rQcFCLMaaACbVd3R9_wgWw","lat":null,"lng":null,"createtime":"2019-07-17 11:16","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374tnjKRHULu8Y1EDDekhrq28","lat":null,"lng":null,"createtime":"2019-07-16 19:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-29T23:49:28+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0610s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0590s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 10,10   [ RunTime:0.0600s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374oMSMZXu09xh7n3gCRFu2Yg","lat":null,"lng":null,"createtime":"2019-07-12 09:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hSZ8hagJ7Bsk23jc_N2008","lat":null,"lng":null,"createtime":"2019-07-11 17:28","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374uBJTPh5zMXAYk5xJ5UUHmc","lat":null,"lng":null,"createtime":"2019-07-11 10:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374l82sBxouM6mMSvJMvJKI9s","lat":null,"lng":null,"createtime":"2019-07-07 20:29","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374gHtVwW2GzRfY8MlAP6efFs","lat":null,"lng":null,"createtime":"2019-07-06 17:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374ttC0tLEjiDTO-z58LCUjQs","lat":null,"lng":null,"createtime":"2019-07-06 16:58","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374kD1g_xq1ZD68ACSwKhfBq0","lat":null,"lng":null,"createtime":"2019-07-06 11:47","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":"39","operatorid":"38","dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-29T23:49:40+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/point?id=*********
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0690s ]
SQL: SELECT * FROM `think_register` WHERE ( id='*********' ) LIMIT 1   [ RunTime:0.0730s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.041000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.041000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T23:49:41+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getpointlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_registerproperty` [ RunTime:0.2690s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_registerproperty` WHERE `userid` = ********* LIMIT 1   [ RunTime:0.0400s ]
SQL: SELECT * FROM `think_registerproperty` WHERE `userid` = ********* ORDER BY id desc LIMIT 0,10   [ RunTime:0.0350s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0350s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0340s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0360s ]
INFO: {"total":"1","rows":[{"id":"7","userid":"*********","propertyid":"39","operatorid":"38","carduserid":null,"point":"-89","roleid":"1","selected":"0","cardmasterflag":"0","rolename":"\u56de\u6536\u5458","propertyname":"pt1","operatorname":"op1"}]}

[ 2019-07-29T23:54:14+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0710s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.0660s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0680s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0660s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.008000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.008000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T23:54:15+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.001000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.2460s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.2040s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.2450s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.2000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.008000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.008000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-07-29T23:54:17+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0500s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0450s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0470s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0520s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0490s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0470s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0510s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0520s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0480s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0470s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0570s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0440s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0480s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0640s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0440s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0480s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0570s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0460s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0500s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0450s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0470s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0530s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.046000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.046000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-07-29T23:54:19+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.2020s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.1970s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.2000s ]
INFO: {"total":"17","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374iK1s8YI_HgDTcA0ZBexeAw","lat":null,"lng":null,"createtime":"2019-07-19 15:52","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374rQcFCLMaaACbVd3R9_wgWw","lat":null,"lng":null,"createtime":"2019-07-17 11:16","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374tnjKRHULu8Y1EDDekhrq28","lat":null,"lng":null,"createtime":"2019-07-16 19:00","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-07-29T23:54:19+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.053000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.053000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

