[ 2019-08-09T08:43:15+08:00 ] 127.0.0.1 /szrecycle_mgr/
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.020000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.003000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.117000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.117000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:43:20+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Login/login
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( account='admin' ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_role` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid=1 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_role` WHERE ( roleid='1' ) LIMIT 1   [ RunTime:0.0100s ]
INFO: 后台登录成功 admin

[ 2019-08-09T08:43:21+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0120s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0100s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0180s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.039000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.039000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:43:21+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.078000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.078000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-08-09T08:43:28+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ViolationMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0200s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=51 ) LIMIT 1   [ RunTime:0.0170s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: search_operatorid_visible D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\62aad1b689aacbb2da03bd527fff0a86.php 第 49 行.
NOTIC: [8] Undefined variable: search_propertyid_visible D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\62aad1b689aacbb2da03bd527fff0a86.php 第 54 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-08-09T08:43:29+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ViolationMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined index: operatorid D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\ViolationMgrController.class.php 第 80 行.
NOTIC: [8] Undefined index: propertyid D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\ViolationMgrController.class.php 第 81 行.
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0140s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_violation` WHERE (  createtime >=1557535408 and createtime<1565311508  ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_violation` WHERE (  createtime >=1557535408 and createtime<1565311508  ) ORDER BY id desc LIMIT 0,10   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0250s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=53 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=74 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0210s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0340s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0120s ]
INFO: {"total":"33","rows":[{"id":"122","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012","point":"1","operatorid":"38","propertyid":"39","createtime":"2019-06-28 15:36:30","operatorname":"op1","propertyname":"pt1"},{"id":"121","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"1","point":"11","operatorid":"53","propertyid":"54","createtime":"2019-06-27 23:10:09","operatorname":"","propertyname":""},{"id":"120","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"44","point":"4","operatorid":"74","propertyid":"75","createtime":"2019-06-27 23:09:47","operatorname":"","propertyname":""},{"id":"119","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u5475\u5475","point":"1","operatorid":"38","propertyid":"39","createtime":"2019-06-21 22:55:35","operatorname":"op1","propertyname":"pt1"},{"id":"118","userid":null,"account":null,"name":"\u6d4b\u8bd52","carduserid":"18","cardnumber":"**********","remark":"\u8fdd\u53cd\u89c4\u5b9a\u6295\u653e","point":"2","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:30:22","operatorname":"op1","propertyname":"pt1"},{"id":"117","userid":null,"account":null,"name":"\u6d4b\u8bd51","carduserid":"19","cardnumber":"**********","remark":"\u8fdd\u53cd\u89c4\u5b9a\u6295\u653e","point":"2","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:30:22","operatorname":"op1","propertyname":"pt1"},{"id":"116","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"115","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"114","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"108","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u5176\u4ed6\u70ed\u53bb\u592a\u70ed\u5176\u4ed6\u70ed\u53bb\u4ed6\u5982\u540c\u4e00","point":"11","operatorid":null,"propertyid":"1","createtime":"2019-06-21 20:22:39","operatorname":"","propertyname":"\u8d85\u7ea7\u7ba1\u7406\u5458"}]}

[ 2019-08-09T08:43:46+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ThrowlogMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_rubbishtypepoint` ORDER BY rubbishtype asc  [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0150s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\94da3bd6e4d2d066fbd0e09154af74ee.php 第 63 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.044000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.044000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-08-09T08:43:47+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ThrowlogMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_throwlog` [ RunTime:0.0150s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_throwlog` WHERE (  throwtime >=1557535427 and throwtime<1565311527  ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_throwlog` WHERE (  throwtime >=1557535427 and throwtime<1565311527  ) ORDER BY id desc LIMIT 0,10   [ RunTime:0.0200s ]
SQL: SELECT SUM(point) AS tp_sum FROM `think_throwlog` WHERE (  throwtime >=1557535427 and throwtime<1565311527  ) LIMIT 1   [ RunTime:0.0180s ]
SQL: SELECT SUM(throwweight) AS tp_sum FROM `think_throwlog` WHERE (  throwtime >=1557535427 and throwtime<1565311527  ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SHOW COLUMNS FROM `think_rubbishtypepoint` [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='A' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='6' ) LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='8' ) LIMIT 1   [ RunTime:0.0090s ]
SQL: SELECT * FROM `think_rubbishtypepoint` WHERE ( rubbishtype='9' ) LIMIT 1   [ RunTime:0.0090s ]
INFO: {"total":"58","totalpoint":"6489","totalweight":"30649","rows":[{"id":"2241","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"3","throwtime":"2019-08-07 14:18:28","totalweight":"0","throwweight":"30","doorno":"3","throwmode":"5","createtime":"2019-08-07 14:18:29","rubbishtype":"A","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u5851\u6599\u7c7b"},{"id":"2240","devcode":"19070217Y401","userid":"*********","carduserid":"0","point":"25","throwtime":"2019-08-06 10:23:11","totalweight":"0","throwweight":"50","doorno":"2","throwmode":"5","createtime":"2019-08-06 10:23:11","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2239","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"30","throwtime":"2019-08-01 09:22:25","totalweight":"0","throwweight":"60","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:22:26","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2238","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"15","throwtime":"2019-08-01 09:22:13","totalweight":"0","throwweight":"30","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:22:15","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2237","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"20","throwtime":"2019-08-01 09:21:47","totalweight":"0","throwweight":"40","doorno":"2","throwmode":"5","createtime":"2019-08-01 09:21:48","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201908","throwdate":"********","areaid1":"810","areaid2":"849","areaid3":"851","areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2236","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"4","throwtime":"2019-07-26 16:46:43","totalweight":"0","throwweight":"20","doorno":"1","throwmode":"5","createtime":"2019-07-26 16:46:43","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u73bb\u7483\u7c7b"},{"id":"2235","devcode":"19070217Y401","userid":"*********","carduserid":"0","point":"35","throwtime":"2019-07-26 15:33:37","totalweight":"0","throwweight":"70","doorno":"4","throwmode":"5","createtime":"2019-07-26 15:33:36","rubbishtype":"6","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u7eb8\u7c7b"},{"id":"2234","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"20","throwtime":"2019-07-24 15:19:11","totalweight":"0","throwweight":"40","doorno":"2","throwmode":"5","createtime":"2019-07-24 15:19:11","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2233","devcode":"19070217Y402","userid":"*********","carduserid":"0","point":"325","throwtime":"2019-07-24 15:18:56","totalweight":"0","throwweight":"650","doorno":"2","throwmode":"5","createtime":"2019-07-24 15:18:56","rubbishtype":"8","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb12\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"0","violationreportuserid":null,"violationreporttime":null,"rubbishtypename":"\u91d1\u5c5e\u7c7b"},{"id":"2232","devcode":"18092717Y909","userid":"*********","carduserid":"0","point":"0","throwtime":"2019-07-24 15:17:24","totalweight":"0","throwweight":"196","doorno":"3","throwmode":"5","createtime":"2019-08-12 03:44:04","rubbishtype":"9","propertyid":"39","operatorid":"38","devname":"\u5b89\u6b46\u56db\u7bb11\u53f7","account":"***********","cardnum":null,"throwmonth":"201907","throwdate":"********","areaid1":null,"areaid2":null,"areaid3":null,"areaid4":null,"violationflag":"1","violationreportuserid":"*********","violationreporttime":"**********","rubbishtypename":"\u73bb\u7483\u7c7b"}]}

[ 2019-08-09T08:45:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0160s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0120s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.018000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.018000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:45:02+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0120s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0140s ]
INFO: {"total":"20","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lQfN6mMVT61RLevOFs2Kdc","lat":null,"lng":null,"createtime":"2019-08-07 13:15","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374oMzbAG4Vce7pGUBi7zieGw","lat":null,"lng":null,"createtime":"2019-08-05 22:53","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hs9-OrLiqSBv9dcLJDuJY4","lat":null,"lng":null,"createtime":"2019-08-01 09:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-08-09T08:47:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Main/index.html
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: SESSION检测成功 
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0120s ]
SQL: SELECT `menuid` FROM `think_rolemenu` WHERE ( roleid=1 )  [ RunTime:0.0150s ]
SQL: SHOW COLUMNS FROM `think_menu` [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=-1 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=39 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=46 ) ORDER BY menuorder asc  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=47 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=32 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=30 ) ORDER BY menuorder asc  [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=40 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=31 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=41 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=42 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=33 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=43 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=44 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=34 ) ORDER BY menuorder asc  [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=54 ) ORDER BY menuorder asc  [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=53 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=55 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=45 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=35 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=56 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=57 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=60 ) ORDER BY menuorder asc  [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=51 ) ORDER BY menuorder asc  [ RunTime:0.0190s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=59 ) ORDER BY menuorder asc  [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=58 ) ORDER BY menuorder asc  [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_menu` WHERE ( parentid=38 ) ORDER BY menuorder asc  [ RunTime:0.0160s ]
INFO: menuTrees [{"menuid":"39","menutitle":"\u5c0f\u7a0b\u5e8f\u7528\u6237","url":"Admin\/RegisterMgr","iconhtml":null,"subnodes":null},{"menuid":"46","menutitle":"\u5361\u7528\u6237","url":"Admin\/CardMgr","iconhtml":null,"subnodes":null},{"menuid":"47","menutitle":"\u6295\u653e\u8bb0\u5f55","url":"Admin\/ThrowlogMgr","iconhtml":null,"subnodes":null},{"menuid":"32","menutitle":"\u8fd0\u8425\u5546","url":"Admin\/UserMgr?roleid=2","iconhtml":null,"subnodes":null},{"menuid":"30","menutitle":"\u7269\u4e1a\u7ba1\u7406","url":"Admin\/UserMgr?roleid=3","iconhtml":null,"subnodes":null},{"menuid":"40","menutitle":"\u8bbe\u5907\u7ba1\u7406","url":null,"iconhtml":null,"subnodes":[{"menuid":"31","menutitle":"\u8bbe\u5907\u5217\u8868","url":"Admin\/DevMgr","iconhtml":null,"subnodes":null},{"menuid":"41","menutitle":"\u7248\u672c\u7ba1\u7406","url":"Admin\/ApppkgMgr","iconhtml":null,"subnodes":null},{"menuid":"42","menutitle":"\u8bbe\u5907\u5730\u56fe","url":"Admin\/PositionMgr","iconhtml":null,"subnodes":null}]},{"menuid":"33","menutitle":"\u5546\u57ce\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"43","menutitle":"\u5546\u54c1\u7ba1\u7406","url":"Admin\/GoodsMgr","iconhtml":null,"subnodes":null},{"menuid":"44","menutitle":"\u5151\u6362\u8ba2\u5355","url":"Admin\/OrderMgr","iconhtml":null,"subnodes":null}]},{"menuid":"34","menutitle":"\u5e7f\u544a\u7ba1\u7406","url":"","iconhtml":null,"subnodes":[{"menuid":"54","menutitle":"\u64ad\u653e\u5b9e\u4f8b\u7ba1\u7406","url":"Admin\/PlayMgr","iconhtml":null,"subnodes":null},{"menuid":"53","menutitle":"\u5e7f\u544a\u6587\u4ef6\u7ba1\u7406","url":"Admin\/AdsMgr","iconhtml":null,"subnodes":null},{"menuid":"55","menutitle":"\u5e03\u5c40\u6a21\u677f","url":"Admin\/LayoutMgr","iconhtml":null,"subnodes":null}]},{"menuid":"45","menutitle":"\u7ba1\u7406\u914d\u7f6e","url":null,"iconhtml":null,"subnodes":[{"menuid":"35","menutitle":"\u7ba1\u7406\u5458","url":"Admin\/UserMgr?roleid=1","iconhtml":null,"subnodes":null},{"menuid":"56","menutitle":"\u8d22\u52a1\u4eba\u5458","url":"Admin\/UserMgr?roleid=4","iconhtml":null,"subnodes":null},{"menuid":"57","menutitle":"\u5e7f\u544a\u5546\u7ba1\u7406","url":"Admin\/UserMgr?roleid=5","iconhtml":null,"subnodes":null},{"menuid":"60","menutitle":"\u76d1\u7ba1\u5458\u7ba1\u7406","url":"Admin\/UserMgr?roleid=7","iconhtml":null,"subnodes":null}]},{"menuid":"51","menutitle":"\u8fdd\u89c4\u7ba1\u7406","url":"Admin\/ViolationMgr","iconhtml":null,"subnodes":null},{"menuid":"59","menutitle":"\u7ebf\u4e0b\u6d88\u8d39\u79ef\u5206","url":"Admin\/CostpointlogMgr","iconhtml":null,"subnodes":null},{"menuid":"58","menutitle":"\u8d26\u6237\u4fe1\u606f","url":"Admin\/Main\/moduserinfo","iconhtml":null,"subnodes":null},{"menuid":"38","menutitle":"\u4fee\u6539\u5bc6\u7801","url":"Admin\/Main\/modpasswd","iconhtml":null,"subnodes":null}]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:47:06+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/Homepage
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.007000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:47:08+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0220s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0270s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0110s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.001000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:47:09+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/RegisterMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_register` [ RunTime:0.0140s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_register` LIMIT 1   [ RunTime:0.0170s ]
SQL: SELECT * FROM `think_register` ORDER BY id desc LIMIT 0,10   [ RunTime:0.0130s ]
INFO: {"total":"20","rows":[{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lQfN6mMVT61RLevOFs2Kdc","lat":null,"lng":null,"createtime":"2019-08-07 13:15","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374oMzbAG4Vce7pGUBi7zieGw","lat":null,"lng":null,"createtime":"2019-08-05 22:53","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hs9-OrLiqSBv9dcLJDuJY4","lat":null,"lng":null,"createtime":"2019-08-01 09:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lIjJvaFdtYPH6ULKG9KY80","lat":null,"lng":null,"createtime":"2019-07-29 11:20","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374jCqp40vGWhKlZA5eX9miy4","lat":null,"lng":null,"createtime":"2019-07-29 11:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374hJZvlSHW6gW3_EV5N0gJdk","lat":null,"lng":null,"createtime":"2019-07-25 23:06","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374qAfxIVvSicKVkHGzHJ_L1Q","lat":null,"lng":null,"createtime":"2019-07-24 16:21","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374lCA-_i4oQrwD6kaviA8T9Q","lat":null,"lng":null,"createtime":"2019-07-24 16:18","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374pP7N1vt9Z0oSE0B3fSGTeg","lat":null,"lng":null,"createtime":"2019-07-24 13:54","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"},{"id":"*********","account":"***********","name":"***********","userpwd":" ","roleid":"0","status":"0","balance":null,"openid":"oJ1374sjtbdbfdlcmMYPrStX23Hg","lat":null,"lng":null,"createtime":"2019-07-24 13:09","headimgurl":null,"updatetime":"**********","fingerprint":null,"point":"0","updatetimepoint":"**********","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"phonenum":null,"receiptname":null,"address":null,"carduserid":null,"propertyid":null,"operatorid":null,"dishonestflag":"0","dishonestflagname":"\u6b63\u5e38"}]}

[ 2019-08-09T08:47:11+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/CardMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=46 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE `roleid` = 2  [ RunTime:0.0110s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.001000s ]
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c48db5bf41f875a614d869570d42ce7d.php 第 58 行.
NOTIC: [8] Undefined variable: propertylist D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\c48db5bf41f875a614d869570d42ce7d.php 第 104 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.017000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000000s ]
INFO: [ app_end ] --END-- [ RunTime:0.000000s ]

[ 2019-08-09T08:47:11+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/CardMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
SQL: SHOW COLUMNS FROM `think_card` [ RunTime:0.0140s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_card` WHERE `status` = 0 LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_card` WHERE `status` = 0 ORDER BY id desc LIMIT 0,100   [ RunTime:0.0130s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0230s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0150s ]
INFO: {"total":"2","rows":[{"id":"565","cardnumber":"0011046394","operatorid":"38","propertyid":"39","status":"0","point":"5","updatetimepoint":"1563501202","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"fingerprint":null,"createtime":"2019-07-19 09:53","updatetime":"1563501202","name":"anxin01","dishonestflag":"0","operatorname":"op1","propertyname":"pt1","cardqrcode":"000000565001","dishonestflagname":"\u6b63\u5e38","featurename":"\u5426"},{"id":"564","cardnumber":"0011069186","operatorid":"38","propertyid":"39","status":"0","point":"975","updatetimepoint":"1562893899","qrcode":null,"feature":null,"facefeaturetime":null,"registersn":null,"fingerprint":null,"createtime":"2019-07-12 09:11","updatetime":"1562893899","name":"lai","dishonestflag":"0","operatorname":"op1","propertyname":"pt1","cardqrcode":"000000564001","dishonestflagname":"\u6b63\u5e38","featurename":"\u5426"}]}

[ 2019-08-09T08:48:18+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ViolationMgr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.000000s ]
SQL: SHOW COLUMNS FROM `think_rolemenu` [ RunTime:0.0130s ]
SQL: SELECT * FROM `think_rolemenu` WHERE ( roleid=1 and menuid=51 ) LIMIT 1   [ RunTime:0.0110s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000000s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000000s ]
NOTIC: [8] Undefined variable: search_operatorid_visible D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\62aad1b689aacbb2da03bd527fff0a86.php 第 49 行.
NOTIC: [8] Undefined variable: search_propertyid_visible D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Runtime\Cache\Admin\62aad1b689aacbb2da03bd527fff0a86.php 第 54 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.012000s ]
INFO: [ view_parse ] --END-- [ RunTime:0.012000s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ view_filter ] --END-- [ RunTime:0.001000s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.001000s ]
INFO: [ app_end ] --END-- [ RunTime:0.001000s ]

[ 2019-08-09T08:48:19+08:00 ] 127.0.0.1 /szrecycle_mgr/index.php/Admin/ViolationMgr/api_getlist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.000000s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.001000s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001000s ]
NOTIC: [8] Undefined index: operatorid D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\ViolationMgrController.class.php 第 80 行.
NOTIC: [8] Undefined index: propertyid D:\004_workspaces\059_szrecycle\szrecycle_mgr\Application\Admin\Controller\ViolationMgrController.class.php 第 81 行.
SQL: SHOW COLUMNS FROM `think_violation` [ RunTime:0.0150s ]
SQL: SELECT COUNT(id) AS tp_count FROM `think_violation` WHERE (  createtime >=1557535698 and createtime<1565311798  ) LIMIT 1   [ RunTime:0.0140s ]
SQL: SELECT * FROM `think_violation` WHERE (  createtime >=1557535698 and createtime<1565311798  ) ORDER BY id desc LIMIT 0,10   [ RunTime:0.0190s ]
SQL: SHOW COLUMNS FROM `think_user` [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=53 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=54 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=74 ) LIMIT 1   [ RunTime:0.0300s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=75 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0100s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0120s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=38 ) LIMIT 1   [ RunTime:0.0110s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=39 ) LIMIT 1   [ RunTime:0.0150s ]
SQL: SELECT * FROM `think_user` WHERE ( userid=1 ) LIMIT 1   [ RunTime:0.0110s ]
INFO: {"total":"33","rows":[{"id":"122","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012\u8fdd\u89c4\u6295\u9012","point":"1","operatorid":"38","propertyid":"39","createtime":"2019-06-28 15:36:30","operatorname":"op1","propertyname":"pt1"},{"id":"121","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"1","point":"11","operatorid":"53","propertyid":"54","createtime":"2019-06-27 23:10:09","operatorname":"","propertyname":""},{"id":"120","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"44","point":"4","operatorid":"74","propertyid":"75","createtime":"2019-06-27 23:09:47","operatorname":"","propertyname":""},{"id":"119","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u5475\u5475","point":"1","operatorid":"38","propertyid":"39","createtime":"2019-06-21 22:55:35","operatorname":"op1","propertyname":"pt1"},{"id":"118","userid":null,"account":null,"name":"\u6d4b\u8bd52","carduserid":"18","cardnumber":"**********","remark":"\u8fdd\u53cd\u89c4\u5b9a\u6295\u653e","point":"2","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:30:22","operatorname":"op1","propertyname":"pt1"},{"id":"117","userid":null,"account":null,"name":"\u6d4b\u8bd51","carduserid":"19","cardnumber":"**********","remark":"\u8fdd\u53cd\u89c4\u5b9a\u6295\u653e","point":"2","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:30:22","operatorname":"op1","propertyname":"pt1"},{"id":"116","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"115","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"114","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u4e71\u6254\u4e71\u653e","point":"5","operatorid":"38","propertyid":"39","createtime":"2019-06-21 20:29:50","operatorname":"op1","propertyname":"pt1"},{"id":"108","userid":"*********","account":"***********","name":"***********","carduserid":null,"cardnumber":null,"remark":"\u5176\u4ed6\u70ed\u53bb\u592a\u70ed\u5176\u4ed6\u70ed\u53bb\u4ed6\u5982\u540c\u4e00","point":"11","operatorid":null,"propertyid":"1","createtime":"2019-06-21 20:22:39","operatorname":"","propertyname":"\u8d85\u7ea7\u7ba1\u7406\u5458"}]}

