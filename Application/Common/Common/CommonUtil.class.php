<?php
namespace Common\Common;
class CommonUtil{
    const server_url = "http://127.0.0.1:18052/jifurecycle/api/recycle/";

    public static function makeFileName($filename){
        if(strpos($filename,'.')!==false){
            $filename=str_replace(strrchr($filename, "."),"",$filename);
        }
        return date('YmdHis',time())."_".$filename;
    }

    public static function updateUserNotify(){
        $url = self::server_url."updateUserNotify.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,"");
        $res = curl_exec($curl);
        $res = json_decode($res,true);
        curl_close($curl);
    }
}