<?php
namespace Common\Common;
class CommonDao{
    public static function time2str($time){
        if(!empty($time)){
            return date('Y-m-d H:i:s',$time);
        }
        return "";
    }
    public static function getCityInfo($cityid){
        $city=M('city');
        return $city->where("cityid=".$cityid)->find();
    }
    public static function getAllCitys(){
        $city=M('city');
        return $city->order("cityid asc")->select();
    }

    public static function getMaintenanceInfo($maintenanceid){
        if(empty($maintenanceid)){
            return null;
        }
        $maintenance=M('maintenance');
        return $maintenance->where("maintenanceid=".$maintenanceid)->find();
    }
    public static function getAllMaintenances(){
        $maintenance=M('maintenance');
        return $maintenance->order("maintenanceid desc")->select();
    }

    public static function getAllPartnerList(){
        $user=M('user');
        return $user->where("roleid=2")->order("userid desc")->select();
    }

    public static function getManufacturerInfo($manufacturerid){
        if(empty($manufacturerid)){
            return null;
        }
//        $manufacturer=M('manufacturer');
//        return $manufacturer->where("manufacturerid=".$manufacturerid)->find();
        $user=M('user');
        return $user->where("userid=".$manufacturerid)->find();
    }
    public static function getAllManufacturer(){
//        $manufacturer=M('manufacturer');
//        return $manufacturer->order("manufacturerid desc")->select();
        $user=M('user');
        return $user->where("roleid=3")->order("userid desc")->select();
    }
    public static function getAllStation(){
        $station=M('station');
        return $station->order("stationid desc")->select();
    }
    public static function getStattionInfo($stationid){
        if(empty($stationid)){
            return null;
        }
        $station=M('station');
        return $station->where("stationid=".$stationid)->find();
    }

    public static function getUserInfo($userid){
        $user=M('user');
        return $user->where("userid=".$userid)->find();
    }

    public static function getUserName($userid){
        if(empty($userid)){
            return "";
        }
        $user=M('user');
        $userdata=$user->where("userid=".$userid)->find();
        if(empty($userdata)){
            return "";
        }
        return $userdata['name'];
    }
    public static function getAllUser(){
        $user=M('user');
        return $user->order("userid desc")->select();
    }

    public static function getRoleName($roleid){
        $role=M('role');
        $result=$role->where("roleid=".$roleid)->find();
        if(empty($result)){
            return $roleid;
        }
        return $result['rolename'];
    }
    public static function getAllRole(){
        $role=M('role');
        return $role->order("roleid asc")->select();
    }

    public static function getStatusName($status){
        switch ($status){
            case 1:
                return "待发货";
            case 2:
                return "待收货";
            case 3:
                return "已完成";
            case 4:
                return "已取消";
        }
        return $status;
    }
    public static function getAllStatus(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="待发货";
        $list[1]["value"]="2";
        $list[1]["name"]="待收货";
        $list[2]["value"]="3";
        $list[2]["name"]="已完成";
        $list[3]["value"]="4";
        $list[3]["name"]="已取消";
        return $list;
    }

    public static function getTypeName($addressType){
        switch ($addressType){
            case 1:
                return "图像";
            case 2:
                return "视频";
            case 3:
                return "GIF";
        }
        return $addressType;
    }

    public static function getTypeFromTypename($typename){
        if($typename=='图像'){
            return "image";
        }else if($typename=='视频'){
            return "video";
        }else if($typename=='GIF'){
            return "gif";
        }else{
            return "";
        }
    }

    public static function getAllType(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="图像";
        $list[1]["value"]="2";
        $list[1]["name"]="视频";
        $list[2]["value"]="3";
        $list[2]["name"]="GIF";
        return $list;
    }

    public static function getAlarmTypeName($addressType){
        switch ($addressType){
            case 1:
                return "电量低";
            case 2:
                return "垃圾箱满";
            case 3:
                return "设备故障";
            case 4:
                return "垃圾无桶";
            case 5:
                return "电机故障";
            case 6:
                return "感应头故障";
        }
        return $addressType;
    }

    public static function getAlarmStatusName($addressType){
        switch ($addressType){
            case 0:
                return "未解除";
            case 1:
                return "已解除";
        }
        return $addressType;
    }

    public static function getConfirmflagName($value){
        switch ($value){
            case 0:
                return "未解除";
            case 1:
                return "已解除";
        }
        return $value;
    }

    public static function getChargeStatusName($status){
        switch ($status){
            case 0:
                return "充电启动中";
            case 1:
                return "充电中";
            case 2:
                return "充电完成";
            case 3:
                return "充电失败";
        }
        return $status;
    }
    public static function getAllChargeStatus(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="充电启动中";
        $list[1]["value"]="1";
        $list[1]["name"]="充电中";
        $list[2]["value"]="2";
        $list[2]["name"]="充电完成";
        $list[3]["value"]="3";
        $list[3]["name"]="充电失败";
        return $list;
    }

    public static function getStartchargemodeName($tasktype){
        switch ($tasktype){
            case 0:
                return "本地刷卡启动";
            case 1:
                return "后台启动";
            case 2:
                return "本地管理员启动";
        }
        return $tasktype;
    }
    public static function getAllStartchargemode(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="本地刷卡启动";
        $list[1]["value"]="1";
        $list[1]["name"]="后台启动";
        $list[2]["value"]="2";
        $list[2]["name"]="本地管理员启动";
        return $list;
    }

    public static function getItemTree($type){
        $item=M('item');
        $result=$item->where("type=".$type)->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]['id']=intval($result[$i]['id']);
                $result[$i]['parentid']=intval($result[$i]['parentid']);
            }
        }
        return $result;
    }

    public static function getRefundstatusName($targetlevel){
        switch ($targetlevel){
            case 0:
                return "未返还";
            case 1:
                return "已返还";
        }
        return $targetlevel;
    }
    public static function getAllRefundstatus(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="未返还";
        $list[1]["value"]="1";
        $list[1]["name"]="已返还";
        return $list;
    }

    public static function getChargestrategyName($type){
        switch ($type){
            case 0:
                return "自动充满";
            case 1:
                return "按时间充满";
            case 2:
                return "定金额";
            case 3:
                return "按电量充满";
        }
        return $type;
    }

    public static function getSolveStatusName($status){
        switch ($status){
            case 0:
                return "未发布";
            case 1:
                return "待处理";
            case 2:
                return "厂商协助";
            case 3:
                return "已处理";
        }
        return $status;
    }
    public static function getAllSolveStatus(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="未发布";
        $list[1]["value"]="1";
        $list[1]["name"]="待处理";
        $list[2]["value"]="2";
        $list[2]["name"]="厂商协助";
        $list[3]["value"]="3";
        $list[3]["name"]="已处理";
        return $list;
    }

    public static function getServlogStatusName($status){
        switch ($status){
            case -1:
                return "待提交";
            case 0:
                return "待省公司确认";
            case 1:
                return "延保单位维修";
            case 2:
                return "运维单位确认";
            case 3:
                return "完成";
        }
        return $status;
    }

    public static function getSolveTypeName($status){
        switch ($status){
            case 0:
                return "原厂处理";
            case 1:
                return "延保厂处理";
        }
        return $status;
    }

    public static function getSolveLevelName($level){
        switch ($level){
            case 1:
                return "一般";
            case 2:
                return "严重";
            case 3:
                return "紧急";
        }
        return $level;
    }
    public static function getAllSolveLevel(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="一般";
        $list[1]["value"]="2";
        $list[1]["name"]="严重";
        $list[2]["value"]="3";
        $list[2]["name"]="紧急";
        return $list;
    }

    public static function getUserStatusName($value){
        switch ($value){
            case 1:
                return "正常";
            case 2:
                return "冻结";
        }
        return $value;
    }
    public static function getAllUserStatus(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="正常";
        $list[1]["value"]="2";
        $list[1]["name"]="冻结";
        return $list;
    }
/////////////////////////////////////////////////////////////////


// 桩
    public static function getPileTypeName($value){
        switch ($value){
            case 1:
                return "公用";
            case 2:
                return "专用";
        }
        return $value;
    }
    public static function getAllPileTypes(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="公用";
        $list[1]["value"]="2";
        $list[1]["name"]="专用";
        return $list;
    }

    public static function getPileStatusName($value){
        switch ($value){
            case 1:
                return "待投运";
            case 2:
                return "停运";
            case 3:
                return "退运";
            case 4:
                return "运行";
        }
        return $value;
    }
    public static function getAllPileStatus(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="待投运";
        $list[1]["value"]="2";
        $list[1]["name"]="停运";
        $list[2]["value"]="3";
        $list[2]["name"]="退运";
        $list[3]["value"]="4";
        $list[3]["name"]="运行";
        return $list;
    }

    public static function getPileAcdcName($value){
        switch ($value){
            case 1:
                return "直流";
            case 2:
                return "交流";
        }
        return $value;
    }
    public static function getAllPileAcdc(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="直流";
        $list[1]["value"]="2";
        $list[1]["name"]="交流";
        return $list;
    }

    public static function getMsgTypeName($value){
        switch ($value){
            case 1:
                return "群发消息";
            case 2:
                return "单点消息";
        }
        return $value;
    }
    public static function getAllMsgType(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="群发消息";
        $list[1]["value"]="2";
        $list[1]["name"]="单点消息";
        return $list;
    }

    public static function getLoginEntranceName($value){
        switch ($value){
            case 0:
                return "PC端";
            case 1:
                return "移动端";
            case 2:
                return "PC端和移动端";
        }
        return $value;
    }
    public static function getAllLoginEntrance(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="PC端";
        $list[1]["value"]="1";
        $list[1]["name"]="移动端";
        $list[2]["value"]="2";
        $list[2]["name"]="PC端和移动端";
        return $list;
    }

    public static function getReplaceflagName($value){
        switch ($value){
            case 0:
                return "否";
            case 1:
                return "是";
        }
        return $value;
    }
    public static function getAllReplaceflag(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="否";
        $list[1]["value"]="1";
        $list[1]["name"]="是";
        return $list;
    }



    public static function getRegisterRolename($value){
        switch ($value){
            case 0:
                return "普通";
            case 1:
                return "回收员";
            case 2:
                return "督导员";
        }
        return $value;
    }

    public static function getDishonestflagname($value){
        switch ($value){
            case 0:
                return "正常";
            case 1:
                return "失信人";
        }
        return $value;
    }
    public static function getAllRegisterRole(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="普通";
        $list[1]["value"]="1";
        $list[1]["name"]="回收员";
        $list[2]["value"]="2";
        $list[2]["name"]="督导员";
        return $list;
    }

    public static function getSubOrgids($orgid){
        $res=array();
        if(empty($orgid)){
            return $res;
        }
        $org=M('org');
        array_push($res,$orgid);
        $suborgs=$org->where("parentid=".$orgid)->select();
        if(empty($suborgs)){
            return $res;
        }else{
            foreach ($suborgs as $suborg){
                $temp=self::getSubOrgids($suborg['id']);
                $res=array_merge($res,$temp);
            }
            return $res;
        }
    }

    public static function getOrderstatusName($value){
        switch ($value){
            case 1:
                return "待发货";
            case 2:
                return "待收货";
            case 3:
                return "已完成";
            case 4:
                return "已取消";
        }
        return $value;
    }
    public static function getAllOrderstatus(){
        $list=array();
        $list[0]["value"]="1";
        $list[0]["name"]="待发货";
        $list[1]["value"]="2";
        $list[1]["name"]="待收货";
        $list[2]["value"]="3";
        $list[2]["name"]="已完成";
        $list[3]["value"]="4";
        $list[3]["name"]="已取消";
        return $list;
    }

    public static function getRubbishtypeName($value){
        $rubbishtypepoint=M('rubbishtypepoint');
        $rubbishtypepointdata=$rubbishtypepoint->where("rubbishtype='".$value."'")->find();
        if(empty($rubbishtypepointdata)){
            return $value;
        }
        return $rubbishtypepointdata['name'];
    }

    public static function getAllRubbishtype(){
        $rubbishtypepoint=M('rubbishtypepoint');
        $rubbishtypepointdata=$rubbishtypepoint->order("rubbishtype asc")->select();
        return $rubbishtypepointdata;
    }

    public static function getPoint($registerdata){
        $point=$registerdata['point'];
        if(!empty($registerdata['carduserid'])){
            $card=M('card');
            $carddata=$card->where("id=".$registerdata['carduserid'])->find();
            if(!empty($carddata)){
                $point=$carddata['point'];
            }
        }
        return $point;
    }

    public static function minusPoint($userid,$costpoint){
        $register=M('register');
        $registerdata=$register->where("id=".$userid)->find();
        if(empty($registerdata)){
            return;
        }
        if(!empty($registerdata['carduserid'])){
            $card=M('card');
            $carddata=$card->where("id=".$registerdata['carduserid'])->find();
            if(!empty($carddata)){
                $card->execute("update think_card set point=point-".$costpoint." where id=".$carddata['id']);
                return;
            }
        }
        $register->execute("update think_registerproperty set point=point-".$costpoint." where userid=".$userid." and selected=1");
        return;
    }

    public static function addPoint($userid,$costpoint){
        $register=M('register');
        $registerdata=$register->where("id=".$userid)->find();
        if(empty($registerdata)){
            return;
        }
        if(!empty($registerdata['carduserid'])){
            $card=M('card');
            $carddata=$card->where("id=".$registerdata['carduserid'])->find();
            if(!empty($carddata)){
                $card->execute("update think_card set point=point+".$costpoint." where id=".$carddata['id']);
                return;
            }
        }
        $register->execute("update think_registerproperty set point=point+".$costpoint." where userid=".$userid." and selected=1");
        return;
    }

    public static function getFiletaskStatusName($value){
        switch ($value){
            case 0:
                return "未开始";
            case 1:
                return "已下发";
            case 2:
                return "开始下载";
            case 3:
                return "已完成";
            case 4:
                return "下载失败";
            case 5:
                return "校验出错";
        }
        return $value;
    }

    public static function getUserFlagName($value){
        switch ($value){
            case 0:
                return "正常";
            case 1:
                return "总量未达标";
        }
        return $value;
    }

    public static function getAdsStatusName($status){
        switch ($status){
            case 0:
                return "待内容审核";
            case 1:
                return "待财务审核";
            case 2:
                return "审核完成";
        }
        return $status;
    }

    public static function getCardmasterflagname($value){
        switch ($value){
            case 0:
                return "否";
            case 1:
                return "是";
        }
        return $value;
    }

    public static function getViolationflagname($value){
        switch ($value){
            case 0:
                return "正常";
            case 1:
                return "违规";
        }
        return $value;
    }
    public static function getAllViolationflag(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="正常";
        $list[1]["value"]="1";
        $list[1]["name"]="违规";
        return $list;
    }

    public static function getAllAlarm(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="全部";
        $list[1]["value"]="1";
        $list[1]["name"]="告警";
        return $list;
    }

    public static function getPointrulename($value){
        switch ($value){
            case 0:
                return "按重量(每一百克)";
            case 1:
                return "按次数";
        }
        return $value;
    }
    public static function getAllPointrule(){
        $list=array();
        $list[0]["value"]="0";
        $list[0]["name"]="按重量(每一百克)";
        $list[1]["value"]="1";
        $list[1]["name"]="按次数";
        return $list;
    }

    public static function getRate(){
        $config=M('config');
        $configdata=$config->where("configkey='scorerate'")->find();
        if(empty($configdata)){
            return 0.01;
        }else{
            return $configdata['configvalue'];
        }
    }

    public static function getUserPoint($userid){
        if(empty($userid)){
            return 0;
        }
        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('userid='.$userid.' and selected=1')->find();
        if(empty($registerpropertydata)){
            return 0;
        }
        return $registerpropertydata['point'];
    }

    public static function getCashStatusName($status){
        switch ($status){
            case 0:
                return "未处理";
            case 1:
                return "已处理";
            case 2:
                return "已驳回";
        }
        return $status;
    }

    public static function getCashtypeName($status){
        switch ($status){
            case 1:
                return "微信钱包";
            case 2:
                return "支付宝";
            case 3:
                return "储蓄卡";
        }
        return $status;
    }
}