<?php
namespace Api\Common;
use Think\Controller;
class CommonController extends Controller {
    const server_url = "http://127.0.0.1:18052/jifurecycle/api/recycle/";

    public function checkLogin(){
        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');
        if(empty($userid)||empty($account)||empty($roleid)){
            \Think\Log::record("没有登录，立即退出 ".$account,"INFO");
            $this->logout();
            return;
        }
    }

    public function logout(){
        session('userid',null);
        session('account',null);
        session('roleid',null);
        $this->redirect('Login/index');
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_data($datas) {
        $data = array();
        $data['retCode'] = '0';
        $data['retMsg'] = 'success';
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_error($datas,$retCode,$retMsg) {
        $data = array();
        $data['retCode'] = $retCode;
        $data['retMsg'] = $retMsg;
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    /**
     * 返回json正确数据
     * @param $str
     * @return string
     */
    function output_commonerror($datas) {
        $data = array();
        $data['retCode'] = '1';
        $data['retMsg'] = '错误';
        $data['data'] = $datas;
        $res = json_encode($data);
        echo $res;die;
    }

    function getDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6367000; //approximate radius of earth in meters

        /*
        Convert these degrees to radians
        to work with the formula
        */

        $lat1 = ($lat1 * pi() ) / 180;
        $lng1 = ($lng1 * pi() ) / 180;

        $lat2 = ($lat2 * pi() ) / 180;
        $lng2 = ($lng2 * pi() ) / 180;

        /*
        Using the
        Haversine formula

        http://en.wikipedia.org/wiki/Haversine_formula

        calculate the distance
        */

        $calcLongitude = $lng2 - $lng1;
        $calcLatitude = $lat2 - $lat1;
        $stepOne = pow(sin($calcLatitude / 2), 2) + cos($lat1) * cos($lat2) * pow(sin($calcLongitude / 2), 2);
        $stepTwo = 2 * asin(min(1, sqrt($stepOne)));
        $calculatedDistance = $earthRadius * $stepTwo;

        return round($calculatedDistance);
    }

//    function my_sort($arrays,$sort_key,$sort_order=SORT_ASC,$sort_type=SORT_NUMERIC ){
//        if(is_array($arrays)){
//            foreach ($arrays as $array){
//                if(is_array($array)){
//                    $key_arrays[] = $array[$sort_key];
//                }else{
//                    return false;
//                }
//            }
//        }else{
//            return false;
//        }
//        array_multisort($key_arrays,$sort_order,$sort_type,$arrays);
//        return $arrays;
//    }


}