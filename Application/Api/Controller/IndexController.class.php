<?php
namespace Api\Controller;
use Api\Common\CommonController;
use Common\Common\CommonDao;
use Common\Common\SmsUtil;
class IndexController extends CommonController {
    public function index(){
    }

    public function getUserInfo(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $register=M('register');
        $result=$register->where("id=".$userid)->find();

        $result=$this->makeRegisterInfo($result);

        $this->output_data($result);
    }

    public function  login(){
        $code = $_REQUEST["code"];
        if( $code == ""){
            $this->output_commonerror("code is empty");
            return;
        }
        \Think\Log::record("login code=".$code,"INFO");

        $data = $this->wx_jscode2session($code);
        //error_log($data['errcode']);
        $openid = $data['openid'];
        $session_key = $data['session_key'];
        if(empty($openid)||empty($session_key)){
            error_log("login error, jscode2session return openid is null");
            return;
        }

        $map['openid']=$openid;
        $res = M("register")->where($map)->find();

        if(empty($res)){
            $account=$this->getRandChar(9);
            $resdb = M("register")->where("account='".$account."'")->find();
            while(!empty($resdb)){
                $account=$this->getRandChar(9);
                $resdb = M("register")->where("account='".$account."'")->find();
            }

            $this->doReg($account,$openid,$session_key,1);
            $M_register=M('register');
            $res=$M_register->where("account='".$account."'")->find();

//            $this->output_error(null,"1002","首次登录，需要绑定昵称");
//            return;
        }

        if($res){
            if($res['status']!=0){
                $this->output_error("",'1002',"用户冻结");
                return;
            }else{
                $res=$this->makeRegisterInfo($res);

                $res['session_key'] = $session_key;
                if(!empty($res)){
                    $res['throwqrcode']=sprintf('%09d', $res['id'])."003";
                    $res['collectqrcode']=sprintf('%09d', $res['id'])."004";
                }
                $config=M('config');
                $configdata=$config->where("configkey='customerphone'")->find();
                if(!empty($configdata)){
                    $res['customerphone']=$configdata['configvalue'];
                }

                $this->output_data($res);
                return;
            }
        }else{
//            $register=$this->autoReg($openid,$session_key);
//            $res['session_key'] = $session_key;
            $this->output_error(null,"1001","用户不存在");
            return;
        }
    }

    private function makeRegisterInfo($registerinfo){
        if(empty($registerinfo)) {
            return $registerinfo;
        }
        $registerinfo['throwqrcode']=sprintf('%09d', $registerinfo['id'])."003";
        $registerinfo['collectqrcode']=sprintf('%09d', $registerinfo['id'])."004";

        $carduserid=$registerinfo['carduserid'];
        if(empty($carduserid)){
            $cond="userid=".$registerinfo['id'];
        }else{
            $cond="userid=".$registerinfo['id']." or carduserid=".$carduserid;
        }

        $throwlog=M('throwlog');
        $thrownum=$throwlog->where($cond)->count();
        $throwweight=$throwlog->where($cond)->sum("throwweight");
        if(empty($throwweight)){
            $throwweight=0;
        }

        $order=M('order');
        $ordernum=$order->where("registerid=".$registerinfo['id'])->count();

        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('userid='.$registerinfo['id'].' and selected=1')->find();
        if(!empty($registerpropertydata)){
            $user=M('user');
            $propertydata=$user->where('userid='.$registerpropertydata['propertyid'])->find();
            if(!empty($propertydata)){
                $registerinfo['propertyname']=$propertydata['name'];
            }

            $registerinfo['point']=$registerpropertydata['point'];
            if(!empty($registerpropertydata['carduserid'])){
                $registerinfo['cardmasterflag']=$registerpropertydata['cardmasterflag'];
            }
            $registerinfo['accumulatedpoint']=$registerpropertydata['accumulatedpoint'];

            if(!empty($registerpropertydata['carduserid'])){
                $card=M('card');
                $carddata=$card->where("id=".$registerpropertydata['carduserid'])->find();
                if(!empty($carddata)){
                    $registerinfo['cardnum']=$carddata['cardnumber'];
                    $registerinfo['point']=$carddata['point'];
                }
            }else{
                $registerinfo['cardnum']="";
            }

            $operatordata=$user->where('userid='.$registerpropertydata['operatorid'])->find();
            if(!empty($operatordata)){
                $registerinfo['homephone']=$operatordata['homephone'];
            }
        }else{
            $registerinfo['point']=0;
            $registerinfo['cardmasterflag']=0;
        }

        $registerinfo['thrownum']=$thrownum;
        $registerinfo['throwweight']=round($throwweight/1000,2);
        $registerinfo['ordernum']=$ordernum;

//            $registerproperty=M('registerproperty');
//            $registerpropertydata=$registerproperty->where('userid='.$userid)->order('selected desc')->find();
        if(!empty($registerpropertydata)&&$registerpropertydata['roleid']==1){
            $registerinfo['collectFlag']=1;
        }else{
            $registerinfo['collectFlag']=0;
        }
        if(!empty($registerpropertydata)&&$registerpropertydata['roleid']==2){
            $registerinfo['dudaoflag']=1;
        }else{
            $registerinfo['dudaoflag']=0;
        }
        if(empty($registerinfo['point'])){
            $registerinfo['point']=0;
            $registerinfo['hunbaojin']=0;
        }else{
            $registerinfo['hunbaojin']=round($registerinfo['point']/100,2);
        }

        if(empty($registerinfo['accumulatedpoint'])){
            $registerinfo['accumulatedpoint']=0;
            $registerinfo['accumulatedhunbaojin']=0;
        }else{
            $registerinfo['accumulatedhunbaojin']=round($registerinfo['accumulatedpoint']/100,2);
        }

        if(empty($registerinfo['cardnum'])){
            $registerinfo['cardnum']="";
        }
        if(empty($registerinfo['cardmasterflag'])){
            $registerinfo['cardmasterflag']=0;
        }
        return $registerinfo;
    }

    private function doReg($account,$openid,$session_key,$auto){
        $register['account']=$account;
        $register['name']=$account;
        $register['openid']=$openid;
        $register['status']=0;
        $register['score']=0;
        $register['chargeTime']=0;
        $register['userpwd']=' ';
        $register['roleid']=0;
        $register['point']=0;
        $time=time();
        $register['createtime']=$time;
        $register['updatetime']=$time;
        $register['updatetimepoint']=$time;
        if($auto==0){
            $register['bindphoneflag']=1;
        }
        //$register['bindphoneflag']=1;
        M("register")->add($register);
        $register['session_key']=$session_key;

        return $register;
    }

    public function getVerCode(){
        $phoneNumber = $_REQUEST["phoneNumber"];
        if( $phoneNumber == ""){
            $this->output_commonerror("phoneNumber is empty");
            return;
        }
        \Think\Log::record("getVerCode".$phoneNumber,"INFO");

//        include("SmsDemo.php");
        //生成验证码
        $code = rand(1000,9999);
        //发送短信
        $sms = new SmsUtil();
        //测试模式
        $status = $sms->send_verify($phoneNumber,$code);
        $status=false;
        \Think\Log::record("send_verify status=".$status,"INFO");
        if(!$status){
//            $error= $sms->error;
            $error='OK';
            \Think\Log::record("send_verify error=".$error,"INFO");
            if($error=='OK'){
//                $model = new Model();
//                $model->execute("insert into think_vercode(phoneNumber,vercode,vercodetime) values('".$phoneNumber."','".$code."',".time().")");
                $vercode=M('vercode');
                $data['phoneNumber']=$phoneNumber;
                $data['vercode']=$code;
                $data['vercodetime']=time();
                $vercode->add($data);
                $this->output_data($code);
            }else{
                $this->output_commonerror($error);
            }
        }

//        Vendor('Alisms#class');
//        $accessKeyId = 'LTAIVqx9g08HKe3o'; //阿里云申请的 Access Key ID
//        $accessKeySecret = '4i5BIp4vrVPFcGRgEk1Gnjd7SIw6fT'; //阿里云申请的 Access Key Secret
//        $alisms = new \Common\Model\Alisms($accessKeyId,$accessKeySecret);
//        $code = 'SMS_137710215'; //短信模板的模板CODE
//        $paramString = '{"code":"'.$code.'"}'; //短信模板中的变量；,参数格式{"no": "123456"}
//        $res = $alisms->smsend($phoneNumber,$code,$paramString);
//        error_log(json_encode($re));

        return;
    }

    public function sendFullMsg(){
        $devid = $_REQUEST["devid"];
        if(empty($devid)){
            $this->output_commonerror("devid is empty");
            return;
        }
        \Think\Log::record("sendFullMsg devid=".$devid,"INFO");

        $dev=M('dev');
        $devdata=$dev->where('id='.$devid)->find();
        if(empty($devdata)){
            $this->output_commonerror("设备不存在");
            return;
        }
        $propertyid=$devdata['propertyid'];
        if(empty($propertyid)){
            $this->output_commonerror("未设置物业");
            return;
        }
        $registerproperty=M('registerproperty');
        $registerpropertylist=$registerproperty->where('propertyid='.$propertyid." and roleid=1")->select();
        if(empty($registerpropertylist)){
            $this->output_commonerror("回收员不存在");
            return;
        }
        $devcode=$devdata['code'];
        $devname=$devdata['name'];

        $register=M('register');
        $sms = new SmsUtil();
        foreach ($registerpropertylist as $registerpropertylist_elem){
            $registerdata=$register->where('id='.$registerpropertylist_elem['userid'])->find();
            if(empty($registerdata)){
                continue;
            }
            $phonenumber=$registerdata['account'];
            \Think\Log::record("sendFullMsg account=".$phonenumber." devname=".$devname.", devcode=".$devcode,"INFO");
            //$status = $sms->sendFullMsg($phonenumber,$devname,$devcode);
            $status="";

            \Think\Log::record("sendFullMsg status=".$status,"INFO");
        }
        $this->output_data('');
        return;
    }

    public function  reg(){
        $account = $_REQUEST["account"];
        if( $account == ""){
            $this->output_commonerror("account is empty");
            return;
        }
        $code = $_REQUEST["code"];
        if( $code == ""){
            $this->output_commonerror("code is empty");
            return;
        }
        $vercode = $_REQUEST["vercode"];
        if( $vercode == ""){
            $this->output_commonerror("vercode is empty");
            return;
        }
        \Think\Log::record("reg account=".$account.", code=".$code.", vercode".$vercode,"INFO");

        $M_vercode=M('vercode');
        $result=$M_vercode->where("phoneNumber='".$account."'")->order("vercodetime desc")->find();
        if(empty($result)){
            $this->output_error("","1009","验证失败");
            return;
        }
        $vercodetime = $result["vercodetime"];
        if(time()-$vercodetime>60*5){
            $this->output_error("","1009","验证失败");
            return;
        }
        $dbcode = $result["vercode"];
        if($vercode!=$dbcode){
            $this->output_error("","1009","验证码不正确");
            return;
        }

        $map['account']=$account;
        $res = M("register")->where($map)->field("id,account,name,openid,roleid")->find();
        // 用户已存在
        if($res){
            $this->output_error("",'1002','手机号已注册');
            return;
        }

        $data = $this->wx_jscode2session($code);
        $openid = $data['openid'];
        $session_key = $data['session_key'];

        $map['openid']=$openid;
        $res = M("register")->where($map)->field("id,account,name,openid,roleid")->find();
        if($res){
            $this->output_error("",'1003','微信账号已注册');
            return;
        }else{
            // 插入用户
            $register=$this->doReg($account,$openid,$session_key,0);
            $M_register=M('register');
            $result=$M_register->where("account='".$account."'")->select();
            $register['id']=$result[0]["id"];


            $this->output_data($register);
            return;
        }
    }

    public function  bindphone(){
        $account = $_REQUEST["account"];
        if(empty($account)){
            $this->output_commonerror("手机号为空");
            return;
        }
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $vercode = $_REQUEST["vercode"];
        if( $vercode == ""){
            $this->output_commonerror("vercode is empty");
            return;
        }
        \Think\Log::record("bindphone account=".$account.", userid=".$userid.", vercode".$vercode,"INFO");

        $M_vercode=M('vercode');
        $result=$M_vercode->where("phoneNumber='".$account."'")->order("vercodetime desc")->find();
        if(empty($result)){
            $this->output_error("","1009","验证失败");
            return;
        }
        $vercodetime = $result["vercodetime"];
        if(time()-$vercodetime>60*5){
            $this->output_error("","1009","验证失败");
            return;
        }
        $dbcode = $result["vercode"];
        if($vercode!=$dbcode){
            $this->output_error("","1009","验证码不正确");
            return;
        }

        $map['account']=$account;
        $res = M("register")->where($map)->field("id,account,name,openid,roleid")->find();
        // 用户已存在
        if($res){
            $this->output_error("",'1002','手机号已注册');
            return;
        }
        $newdata['account']=$account;
        $newdata['phonenum']=$account;
        $newdata['bindphoneflag']=1;
        $register=M('register');
        $register->where("id=".$userid)->save($newdata);

        $this->output_data("");
        return;
    }

    public function quitCurrentProperty(){
        $userid=I('userid');
        if(empty($userid)){
            $this->output_error("",'1003','userid为空');
            return;
        }
        $register=M('register');
        $registerdata=$register->where('id='.$userid)->find();
        if(empty($registerdata)){
            $this->output_error("",'1003','用户不存在');
            return;
        }

        $data['propertyid']=null;
        $data['operatorid']=null;
        $data['carduserid']=null;
        $data['point']=0;
        $data['updatetimepoint']=time();
        $data['updatetime']=time();
        $register->where('id='.$userid)->save($data);
        $this->output_data("");
        return;
    }

    public function getGoods(){
        $operatorid = $_REQUEST["operatorid"];
//        if(empty($operatorid)){
//            $this->output_commonerror("operatorid is empty");
//            return;
//        }
        $goods=M('goods');
        $cond['status']=1;
        if(!empty($operatorid)&&$operatorid!='null') {
            $cond['operatorid'] = $operatorid;
        }
        $result=$goods->where($cond)->order("sales desc")->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem['content']="";
                $result_elem['price']=round($result_elem['price']);
            }
        }
//        $result["testAES"]=$this->makePasswd();
//        \Think\Log::record("testAES=".$result["testAES"],"INFO");
        $this->output_data($result);
    }

    public function getGoodsDetail(){
        $id = $_REQUEST["id"];
        if(empty($id)){
            $this->output_commonerror("id is empty");
            return;
        }
        $goods=M('goods');
        $result=$goods->where("id=".$id)->find();
        if(!empty($result)){
            $result['price']=round($result['price']);
        }
        $this->output_data($result);
    }

    public function getMyAddress(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $address=M('address');
        $result=$address->where("registerid=".$userid)->order("usetime desc")->select();
//        if(!empty($result)){
//            foreach ($result as &$result_elem){
//                $result_elem['content']="";
//                $result_elem['price']=round($result_elem['price']);
//            }
//        }
        $this->output_data($result);
    }

    public function getMyProperty(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $registerproperty=M('registerproperty');
        $result=$registerproperty->where("userid=".$userid)->order("id desc")->select();
        if(!empty($result)){
            $user=M('user');
            foreach ($result as &$result_elem){
                $userdata=$user->where('userid='.$result_elem['propertyid'])->find();
                if(empty($userdata)){
                    $result_elem['propertyname']=$result_elem['propertyid'];
                }else{
                    $result_elem['propertyname']=$userdata['name'];
                }
            }
        }
        $this->output_data($result);
    }

    public function getFirstAddress(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $address=M('address');
        $result=$address->where("registerid=".$userid)->order("usetime desc")->limit(1)->select();
//        if(!empty($result)){
//            foreach ($result as &$result_elem){
//                $result_elem['content']="";
//                $result_elem['price']=round($result_elem['price']);
//            }
//        }
        if(!empty($result)){
            $this->output_data($result[0]);
        }else{
            $this->output_data("");
        }

    }

    public function getAddressDetail(){
        $id = $_REQUEST["id"];
        if(empty($id)){
            $this->output_commonerror("id is empty");
            return;
        }
        $address=M('address');
        $result=$address->where("id=".$id)->find();
//        if(!empty($result)){
//            $result['price']=round($result['price']);
//        }
        $this->output_data($result);
    }

    public function addModAddress(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $registerid=$request["registerid"];
        $receiptname=$request["receiptname"];
        $phonenum=$request["phonenum"];
        $addressvalue=$request["address"];

        if(empty($registerid)){
            $this->output_commonerror("registerid is empty");
            return;
        }
        if(empty($receiptname)){
            $this->output_commonerror("receiptname is empty");
            return;
        }
        if(empty($phonenum)){
            $this->output_commonerror("phonenum is empty");
            return;
        }
        if(empty($addressvalue)){
            $this->output_commonerror("address is empty");
            return;
        }

        $address=M('address');
        if(empty($id)){
            $data['registerid']=$registerid;
            $data['phonenum']=$phonenum;
            $data['receiptname']=$receiptname;
            $data['address']=$addressvalue;
            $data['createtime']=time();
            $data['updatetime']=time();
            $data['usetime']=time();
            $address->add($data);
        }else{
            $data['phonenum']=$phonenum;
            $data['receiptname']=$receiptname;
            $data['address']=$addressvalue;
            $data['updatetime']=time();
            $address->where("id=".$id)->save($data);
        }
        $this->output_data("");
        return;
    }

    public function delAddress(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $registerid=$request["registerid"];

        if(empty($registerid)){
            $this->output_commonerror("registerid is empty");
            return;
        }
        if(empty($id)){
            $this->output_data("");
            return;
        }

        $address=M('address');
        $address->where("id=".$id)->delete();
        $this->output_data("");
        return;
    }

    public function addOrder(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $goodsid=$request["goodsid"];
        $addressid=$request["addressid"];

        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        if(empty($goodsid)){
            $this->output_commonerror("goodsid is empty");
            return;
        }
        if(empty($addressid)){
            $this->output_commonerror("addressid is empty");
            return;
        }

        $register=M('register');
        $registerdata=$register->where("id=".$userid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("用户不存在");
            return;
        }

        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('userid='.$userid.' and selected=1')->select();
        if(empty($registerpropertydata)||count($registerpropertydata)!=1){
            $this->output_commonerror("用户未选择物业");
            return;
        }

        $point=CommonDao::getUserPoint($userid);

        $goods=M('goods');
        $goodsdata=$goods->where("id=".$goodsid)->find();
        if(empty($goodsdata)){
            $this->output_commonerror("商品不存在");
            return;
        }

        $operatorid_goods=$goodsdata['operatorid'];
        $operatorid_register=$registerpropertydata[0]['operatorid'];
        if(empty($operatorid_register)||$operatorid_goods!=$operatorid_register){
            $this->output_commonerror("禁止购买其他运营商的商品");
            return;
        }

        $address=M('address');
        $addressdata=$address->where("id=".$addressid)->find();
        if(empty($addressdata)){
            $this->output_commonerror("收货地址不存在");
            return;
        }
        $fulladdress=$addressdata['receiptname']." ".$addressdata['phonenum']." ".$addressdata['address'];
        $addressdata1['usetime']=time();
        $address->where("id=".$addressid)->save($addressdata1);

        $price=$goodsdata['price'];
        if($point<$price){
            $this->output_commonerror("余额不足");
            return;
        }

        $operatorid=$goodsdata['operatorid'];
        if(empty($operatorid)){
            $this->output_commonerror("商品配置错误，未指定运营商");
            return;
        }

//        $register->execute("update think_register set point=point-".$price." where id=".$userid);
        CommonDao::minusPoint($userid,$price);

        $order=M('order');
        $orderdata['registerid']=$userid;
        $orderdata['goodsid']=$goodsid;
        $orderdata['status']=1;
        $orderdata['price']=$price;
        $orderdata['count']=1;
        $orderdata['total']=$price;
        $orderdata['address']=$fulladdress;
        $orderdata['operatorid']=$operatorid;
        $orderdata['createtime']=time();
        $orderdata['updatetime']=time();
        $orderdata['account']=$registerdata['account'];
        $orderdata['goodsname']=$goodsdata['name'];
        $orderid=$order->add($orderdata);
        $this->output_data($orderid);
        return;
    }

    public function getOrder(){
        $orderid = $_REQUEST["orderid"];
        if(empty($orderid)){
            $this->output_commonerror("orderid is empty");
            return;
        }
        $order=M('order');
        $result=$order->where("id=".$orderid)->find();
        if(!empty($result)){
            $goods=M('goods');
            $goodsdata=$goods->where("id=".$result['goodsid'])->find();
            $result['goodsname']=$goodsdata['name'];

            $result['statusname']=CommonDao::getOrderstatusName($result['status']);
        }
        $this->output_data($result);
    }

    public function getOrderlist(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $order=M('order');
        $result=$order->where("registerid=".$userid)->order("createtime desc")->limit(200)->select();
        if(!empty($result)){
            $goods=M('goods');
            foreach ($result as &$result_elem){
                $goodsdata=$goods->where("id=".$result_elem['goodsid'])->find();
                $result_elem['goodsname']=$goodsdata['name'];
                $result_elem['statusname']=CommonDao::getOrderstatusName($result_elem['status']);
                $result_elem['total']=round($result_elem['total']);
            }
        }
        $this->output_data($result);
    }

    public function getThrowlist(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $register=M('register');
        $registerdata=$register->where('id='.$userid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("用户不存在");
            return;
        }
        $carduserid=$registerdata['carduserid'];
        if(empty($carduserid)){
            $cond="userid=".$userid;
        }else{
            $cond="userid=".$userid." or carduserid=".$carduserid;
        }
        $throwlog=M('throwlog');
        $result=$throwlog->where($cond)->order("createtime desc")->limit(200)->select();
        if(!empty($result)){
            $dev=M('dev');
            foreach ($result as &$result_elem){
                $devdata=$dev->where("code='".$result_elem['devcode']."'")->find();
                $result_elem['devname']=$devdata['name'];
                $result_elem['throwtime']=date('Y-m-d H:i:s',$result_elem['throwtime']);
                $result_elem['throwweight']=round($result_elem['throwweight']/1000,3);
            }
        }
        $this->output_data($result);
    }

    public function getThrowlistByWeigh(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $devcode=$request["devcode"];
        $rublishtype=$request["rublishtype"];

        $devcodes=explode("-",$devcode,2);
        if(empty($devcodes)||count($devcodes)!=2){
            $this->output_error("","1024","二维码错误");
            return;
        }

        $devcode=$devcodes[0];

//        $register=M('register');
//        $registerdata=$register->where('id='.$userid)->find();
//        if(empty($registerdata)){
//            $this->output_commonerror("用户不存在");
//            return;
//        }
        $cond['userid']=$userid;
        $cond['devcode']=$devcode;
        $cond['rubbishtype']=$rublishtype;
        $cond['throwmode']=7;
        $time111=time()-120;
        $throwlog=M('throwlog');
        $throwlogweigh=M('throwlogweigh');
        $result=$throwlogweigh->where($cond)->where("throwweight > 0 and createtime>".$time111)->order("createtime desc")->find();
        if(empty($result)){
            $this->output_error("","1066","称重未完成，请称重完成后等待");
            return;
        }
        $throwweight=$result['throwweight'];
        $maxweight=round($throwweight*1.05,0);
        $minweight=round($throwweight*0.95,0);
        $cond1['devcode']=$devcode;
        $cond1['rubbishtype']=$rublishtype;
        $time1=time()-(3600*24*3);
        $loglist=$throwlog->where($cond1)->where("createtime>=".$time1." and throwweight >=".$minweight." and throwweight<=".$maxweight." and throwmode!=7")->order("createtime desc")->select();

        if(!empty($loglist)){
            $register=M('register');
            foreach ($loglist as &$loglist_elem){
                $userid=$loglist_elem['userid'];
                $throwtime=$loglist_elem['throwtime'];
                if(!empty($throwtime)){
                    $loglist_elem['throwtime']=date('Y-m-d H:i:s',$throwtime);
                }

                if(!empty($userid)){
                    $registerdata=$register->where('id='.$userid)->find();
                    if(!empty($registerdata)){
                        $loglist_elem['account']=$registerdata['account'];
                    }
                }
            }
        }

        $this->output_data($loglist);
        return;
    }

    public function getTasklist(){
        $userid = $_REQUEST["userid"];
        // 经度
        $longitude = $_REQUEST["lng"];
        if( $longitude == ""){
            $this->output_error("longitude is empty");
            return;
        }

        //纬度
        $latitude = $_REQUEST["lat"];
        if( $latitude == ""){
            $this->output_error("latitude is empty");
            return;
        }
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $registerproperty=M('registerproperty');
        $registerpropertylist=$registerproperty->where("userid=".$userid." and roleid=1")->select();
        if(empty($registerpropertylist)){
            $this->output_data("");
            return;
        }
        $propertyidlist=array();
        foreach ($registerpropertylist as $registerpropertylist_elem){
            $propertyidlist[]=$registerpropertylist_elem['propertyid'];
        }
        $cond['propertyid']=array('in',$propertyidlist);
        $cond['isfull']=1;
        $dev=M('dev');
        $result=$dev->where($cond)->order("createtime desc")->limit(200)->select();
        $info = array();
        foreach($result as $vo){
            $distance = $this->getDistance($latitude,$longitude,$vo['lat'],$vo['lng']);
            $vo['dist'] = round($distance/1000,2);
//            if($distance <= $dist){
            $info[] = $vo;
//            }
        }
//        if(!empty($result)){
//            $dev=M('dev');
//            foreach ($result as &$result_elem){
//                $devdata=$dev->where("code='".$result_elem['devcode']."'")->find();
//                $result_elem['devname']=$devdata['name'];
//                $result_elem['throwtime']=date('Y-m-d H:i:s',$result_elem['throwtime']);
//            }
//        }
        $this->output_data($info);
    }

    public function getTaskhistorylist(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $taskhistory=M('taskhistory');
        $result=$taskhistory->where("userid=".$userid)->order("createtime desc")->limit(200)->select();
        if(!empty($result)){
            $dev=M('dev');
            foreach ($result as &$result_elem){
                $devdata=$dev->where("id=".$result_elem['devid'])->find();
                $result_elem['devname']=$devdata['name'];
                $result_elem['createtime']=date('Y-m-d H:i:s',$result_elem['createtime']);
            }
        }
        $this->output_data($result);
    }

    function getDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6367000; //approximate radius of earth in meters
        $lat1 = ($lat1 * pi() ) / 180;
        $lng1 = ($lng1 * pi() ) / 180;
        $lat2 = ($lat2 * pi() ) / 180;
        $lng2 = ($lng2 * pi() ) / 180;

        $calcLongitude = $lng2 - $lng1;
        $calcLatitude = $lat2 - $lat1;
        $stepOne = pow(sin($calcLatitude / 2), 2) + cos($lat1) * cos($lat2) * pow(sin($calcLongitude / 2), 2);
        $stepTwo = 2 * asin(min(1, sqrt($stepOne)));
        $calculatedDistance = $earthRadius * $stepTwo;

        return round($calculatedDistance);
    }
    //
//    private function makePasswd(){
//        // 生成getToken接口passwd字段
//        return $this->encrypt(hash('sha256', "密码明文"),"密钥");
//    }
//
//    public static function encrypt($data, $key) {
//        $data =  openssl_encrypt($data, 'aes-128-ecb', $key, OPENSSL_RAW_DATA);
//        return base64_encode($data);
//    }
//    public static function decrypt($data, $key) {
//        $encrypted = base64_decode($data);
//        return openssl_decrypt($encrypted, 'aes-128-ecb', $key, OPENSSL_RAW_DATA);
//    }

    public function uploadFile(){
        $upload = new \Think\Upload();
        $upload->maxSize=0;//(不限制上传大小，但是同时要修改php.ini中的upload_max_filesize和post_max_size上传大小限制，否则过大会报错）
        $upload->rootPath='Public/Upload/';//上传图片目录
        $upload->exts=array('jpg','gif','png','jpeg','mp4','avi','apk');//允许上传的图片格式
        $upload->saveName=array('\Common\Common\CommonUtil::makeFileName',array('__FILE__'));
        $info=$upload->upload();//上传
//        \Think\Log::record("uploadFile=".json_encode($info),"INFO");
        if(!$info){//上传错误，输出错误信息
            $result=json_encode(
                $upload->getError()
            );
        }else{//上传成功，输出图片地址和上传的结果，因为前端ajax接收的是json的数据，所以要进行json_encode
            $url=__ROOT__."/Public/Upload/".$info['picture']['savepath'].$info['picture']['savename'];
            $result=json_encode(
                array(
                    'pictureUrl'=>$url,
                    'retCode'=>"0",
                    'md5'=>$info['picture']['md5'],
                )
            );
        }
        echo $result;//输出信息
    }

    private function wx_jscode2session($code){
        $appid='wxa5c68576571ccada';
        $secret='3c4d46675561884156f2b8ee9fa7973f';
        $url = 'https://api.weixin.qq.com/sns/jscode2session?appid='.$appid.'&secret='.$secret.'&js_code='.$code.'&grant_type=authorization_code';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);//这个是重点。
        //curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE); // https请求 不验证证书和hosts
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        $data = curl_exec($curl);
        curl_close($curl);
        \Think\Log::record("wx_jscode2session data=".$data,"INFO");
        return json_decode($data,true);
    }



    public function getViolationList(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $throwlog=M('throwlog');
        $result=$throwlog->where("userid=".$userid." and violationflag=1")->order("createtime desc")->limit(200)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem['createtime']=date('Y-m-d H:i:s',$result_elem['createtime']);
            }
        }
        $this->output_data($result);
    }

    public function setViolation(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $id=$request["id"];
        if(empty($userid)||empty($id)){
            $this->output_error("","1024","参数非法");
            return;
        }
        $throwlog=M('throwlog');
        $throwlogdata=$throwlog->where('id='.$id)->find();
        if(empty($throwlogdata)){
            $this->output_error("","1024","记录不存在");
            return;
        }

        $newdata['violationflag']=1;
        $newdata['violationreportuserid']=$userid;
        $newdata['violationreporttime']=time();
        $throwlog->where('id='.$id)->save($newdata);

        $point=$throwlogdata['point'];
        $userid=$throwlogdata['userid'];
        $propertyid=$throwlogdata['propertyid'];
        if(!empty($point)&&$point>0&&!empty($propertyid)){
            $registerproperty=M('registerproperty');
            $registerpropertydata=$registerproperty->where('userid='.$userid." and propertyid=".$propertyid)->find();
            if(!empty($registerpropertydata)){
                $pointdb=$registerpropertydata['point'];
                $newdata1['point']=$pointdb-$point;
                $registerproperty->where('id='.$registerpropertydata['id'])->save($newdata1);
            }
        }

        $this->output_data('');
        return;
    }

    public function getMyCardUserList(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $register=M('register');
        $registerdata=$register->where('id='.$userid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("用户不存在");
            return;
        }


        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('userid='.$userid)->order('selected desc')->find();
        if(!empty($registerpropertydata)){
            $carduserid=$registerpropertydata['carduserid'];
            $cardmasterflag=$registerpropertydata['cardmasterflag'];
            if(empty($carduserid)||empty($cardmasterflag)){
                $this->output_data('');
                return;
            }
            $carduserlist=$registerproperty->where('carduserid='.$carduserid." and userid!=".$userid)->select();
            if(empty($carduserlist)){
                $this->output_data('');
                return;
            }
            $reslist=array();
            foreach ($carduserlist as $carduser){
                $registerdata=$register->where('id='.$carduser['userid'])->find();
                if(empty($registerdata)){
                    continue;
                }
                $registerdata['name']=substr($registerdata['name'],0,3)."****".substr($registerdata['name'],7,4);
                $registerdata['registerpropertyid']=$carduser['id'];
                $reslist[]=$registerdata;
            }
            $this->output_data($reslist);
            return;
        }
        $this->output_data('');
        return;
    }

    public function delMyCardUser(){
        $userid = $_REQUEST["userid"];
        $id = $_REQUEST["id"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        if(empty($id)){
            $this->output_commonerror("id is empty");
            return;
        }
        $register=M('register');
        $registerdata=$register->where('id='.$userid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("用户不存在");
            return;
        }


        $registerproperty=M('registerproperty');
        $newdata['carduserid']=null;
        $newdata['cardmasterflag']=0;
        $registerproperty->where('id='.$id)->save($newdata);
        $this->output_data('');
        return;
    }

    public function selectMyProperty(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $registerpropertyid=$request["registerpropertyid"];

        $registerproperty=M('registerproperty');
        $data['selected']=0;
        $registerproperty->where('userid='.$userid)->save($data);
        $data['selected']=1;
        $registerproperty->where('id='.$registerpropertyid)->save($data);
        $this->output_data('');
    }

    public function getSearchhistory(){
        $userid = I("userid");
        $num = I("num");
        $searchhistory=M('searchhistory');
        $time=time()-3600*24*7;
        $result=$searchhistory->query("select item,count(item) as num from think_searchhistory where createtime>".$time." group by item order by num desc limit ".$num);
        $this->output_data($result);
        return;
    }

    public function getSearResult(){
        $item = I("item");
        $userid = I("userid");
        if(empty($item)){
            $this->output_data('');
            return;
        }
        $cond=array();
        $cond['item']=array('like','%'.$item.'%');
        $categoryitems=M('categoryitems');
        //$categoryitemslist=$categoryitems->where($cond)->order('id asc')->limit(100)->select();
        $categoryitemslist=$categoryitems->query("select a.item as item,b.name as categoryname,a.categoryid as categoryid  from think_categoryitems a left join think_category b on a.categoryid=b.id where a.item like '%".$item."%' order by a.id desc limit 100");

        $searchhistory=M('searchhistory');
        $newdata['item']=$item;
        $newdata['createtime']=time();
        $searchhistory->add($newdata);

        $this->output_data($categoryitemslist);
        return;
    }

    public function getSearResultByCategoryid(){
        $categoryid = I("categoryid");
        $userid = I("userid");
        $offset=I("offset");
        $limit=I("limit");
        if(empty($categoryid)){
            $this->output_data('');
            return;
        }
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=100;
        }

        $cond=array();
        $cond['categoryid']=$categoryid;
        $categoryitems=M('categoryitems');
        $categoryitemslist=$categoryitems->where($cond)->order('id desc')->field('item')->limit($offset,$limit)->select();

        $this->output_data($categoryitemslist);
        return;
    }

    public function getCategoryInfo(){
        $categoryid=I('categoryid');
        if(empty($categoryid)){
            $this->output_data('');
            return;
        }
        $category=M('category');
        $categoryinfo=$category->where("id=".$categoryid)->find();
        $this->output_data($categoryinfo);
        return;
    }

    public function getBanneradds(){
        $banneradds=M('banneradds');
        $banneradddatas=$banneradds->where('status=1')->order('ordernum asc')->select();
        $this->output_data($banneradddatas);
//        $goods=M('goods');
//        $goodslist=$goods->where("mainpic is not null")->order("id desc")->select();
//        $this->output_data($goodslist);
    }

    public function getCashInfo(){
        $userid = $_REQUEST["userid"];
        if(empty($userid)){
            $this->output_commonerror("userid is empty");
            return;
        }
        $register=M('register');
        $result=$register->where("id=".$userid)->find();

        $point=CommonDao::getUserPoint($userid);
        $res['point']=$point;
        $res['money']=$point*CommonDao::getRate();

        $config=M('config');
        $max_cash_times_perday=10;
        $configdata=$config->where("configkey='max_cash_times_perday'")->find();
        if(!empty($configdata)){
            $max_cash_times_perday=$configdata['configvalue'];
        }
        $res['max_cash_times_perday']=$max_cash_times_perday;

        $min_cash_money=1;
        $configdata=$config->where("configkey='min_cash_money'")->find();
        if(!empty($configdata)){
            $min_cash_money=$configdata['configvalue'];
        }
        $res['min_cash_money']=$min_cash_money;

        $max_cash_money=1;
        $configdata=$config->where("configkey='max_cash_money'")->find();
        if(!empty($configdata)){
            $max_cash_money=$configdata['configvalue'];
        }
        $res['max_cash_money']=$max_cash_money;

        $cash=M('cash');
        $today = strtotime(date("Y-m-d"),time());
        $cashtimes=$cash->where("createuserid=".$userid." and createtime>=".$today)->count();
        $res['current_times']=$cashtimes;
        $this->output_data($res);
    }

    public function addCash(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $createuserid=$request["createuserid"];
        $money=$request["money"];
        $type=$request["type"];
        $name=$request["name"];
        $bank=$request["bank"];
        $bankcardnum=$request["bankcardnum"];
        $zhifubao=$request["zhifubao"];
        if(empty($createuserid)){
            $this->output_commonerror("createuserid is empty");
            return;
        }
        if(empty($money)){
            $this->output_commonerror("金额为空");
            return;
        }
        if(empty($type)){
            $this->output_commonerror("提现类型为空");
            return;
        }

        $config=M('config');

        $min_cash_money=1;
        $configdata=$config->where("configkey='min_cash_money'")->find();
        if(!empty($configdata)){
            $min_cash_money=$configdata['configvalue'];
        }
        if($money<$min_cash_money){
            $this->output_commonerror("单笔不低于".$min_cash_money."元");
            return;
        }

        $max_cash_money=100;
        $configdata=$config->where("configkey='max_cash_money'")->find();
        if(!empty($configdata)){
            $max_cash_money=$configdata['configvalue'];
        }
        if($money>$max_cash_money){
            $this->output_commonerror("单笔不高于".$max_cash_money."元");
            return;
        }

        $cash=M('cash');
        $today = strtotime(date("Y-m-d"),time());
        $cashtimes=$cash->where("createuserid=".$createuserid." and createtime>=".$today)->count();
        $config=M('config');
        $max_cash_times_perday=10;
        $configdata=$config->where("configkey='max_cash_times_perday'")->find();
        if(!empty($configdata)){
            $max_cash_times_perday=$configdata['configvalue'];
        }
        if(($cashtimes+1)>$max_cash_times_perday){
            $this->output_commonerror("单日提现次数超过".$max_cash_times_perday."次");
            return;
        }

        $register=M('register');
        $registerdata=$register->where("id=".$createuserid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("createuserid not exist");
            return;
        }
        $openid=$registerdata['openid'];
        if(empty($openid)){
            $this->output_commonerror("openid为空");
            return;
        }

        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('userid='.$createuserid.' and selected=1')->select();
        if(empty($registerpropertydata)||count($registerpropertydata)!=1){
            $this->output_commonerror("用户未选择物业");
            return;
        }

        $dbpoint=CommonDao::getUserPoint($createuserid);
        $maxmoney=$dbpoint*CommonDao::getRate();
        if($money>$maxmoney){
            $this->output_commonerror("积分余额不足");
            return;
        }
        $costpoint=floatval($money)/CommonDao::getRate();
        if($dbpoint<$costpoint){
            $costpoint=$dbpoint;
        }
//        $Model = M();
//        $Model->execute("update think_registerproperty set point=point-".$costpoint." where userid=".$createuserid." and ");
        CommonDao::minusPoint($createuserid,$costpoint);

        $out_trade_no = date("YmdHis").strval(mt_rand(10000,99999)).$createuserid;

        $cash=M('cash');
        $data['createuserid']=$createuserid;
        $data['money']=$money;
        $data['point']=$costpoint;
        $data['status']=0;
        $data['name']=$name;
        $data['bank']=$bank;
        $data['bankcardnum']=$bankcardnum;
        $data['cashtype']=$type;
        $data['zhifubao']=$zhifubao;
        $data['propertyid']=$registerpropertydata[0]['propertyid'];
        $data['operatorid']=$registerpropertydata[0]['operatorid'];;
        $data['createtime']=time();
        $data['outtradeno']=$out_trade_no;
        $data['openid']=$openid;
        $data['updatetime']=time();
        $cash->add($data);

        //CommonDao::addMsg($createuserid,"提现申请已提交","您的".$money."元提现申请已提交，请等待审核");
        $this->output_data("提交成功");
        return;
    }

    public function getCashs()
    {
        $userid = $_REQUEST["userid"];
        if ($userid == "") {
            $this->output_commonerror("userid is empty");
            return;
        }

        $startPage = $_REQUEST["startPage"];
        if (empty($startPage)) {
            $startPage = 0;
        }

        $pageSize = $_REQUEST["pageSize"];
        if (empty($pageSize)) {
            $pageSize = 20;
        }
        error_log("getCashs userid=" . $userid . ", startPage=" . $startPage . ", pageSize=" . $pageSize);
        $cash = M('cash');
        $result = $cash->where("createuserid=" . $userid)->order("id desc")->limit($startPage, $pageSize)->select();
        $totalmoney = $cash->where("createuserid=" . $userid)->sum("money");
        if (!empty($result)) {
            for ($i = 0; $i < count($result); $i++) {
                $createtime = $result[$i]["createtime"];
                if (!empty($createtime)) {
                    $result[$i]["createtime"] = date('m-d H:i', $createtime);
                }
                $result[$i]["statusname"] = CommonDao::getCashStatusName($result[$i]["status"]);
            }
        }
        if(empty($totalmoney)){
            $totalmoney=0;
        }
        $res['list']=$result;
        $res['totalmoney']=$totalmoney;
        $this->output_data($res);
    }

    private function getRandChar($length){
        $str = null;
        $strPol = "0123456789";//大小写字母以及数字
        $max = strlen($strPol)-1;

        for($i=0;$i<$length;$i++){
            $str.=$strPol[mt_rand(0,$max)];
        }
        return "T".$str;
    }


    public function getItemtypes(){
        $itemtype=M('itemtype');
        $result=$itemtype->order("id asc")->select();
        $this->output_data($result);
    }
    public function addRecovery(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $createuserid=$request["createuserid"];
        $itemtype=$request["itemtype"];
        $itemdesc=$request["itemdesc"];
        $visittime=$request["visittime"];
        $contactname=$request["contactname"];
        $phonenumber=$request["phonenumber"];
        $address=$request["address"];
        if(empty($createuserid)){
            $this->output_commonerror("createuserid is empty");
            return;
        }
        if(empty($itemtype)){
            $this->output_commonerror("itemtype is empty");
            return;
        }
        if(empty($visittime)){
            $this->output_commonerror("visittime is empty");
            return;
        }
        if(empty($contactname)){
            $this->output_commonerror("contactname is empty");
            return;
        }
        if(empty($phonenumber)){
            $this->output_commonerror("phonenumber is empty");
            return;
        }
        if(empty($address)){
            $this->output_commonerror("address is empty");
            return;
        }

        $recovery=M('recovery');
        $data['createuserid']=$createuserid;
        $data['itemtype']=$itemtype;
        $data['itemdesc']=$itemdesc;
        $data['status']=0;
        $data['visittime']=strtotime($visittime);
        $data['contactname']=$contactname;
        $data['phonenumber']=$phonenumber;
        $data['address']=$address;
        $data['createtime']=time();
        $data['updatetime']=time();
        $recovery->add($data);

        //CommonDao::addMsg($createuserid,"大件回收申请已提交","您的大件回收申请已成功提交，请耐心等待工作人员上门回收");

        $this->output_data("添加成功");
        return;
    }

    public function getMobilePhoneUser(){
        $devcode = $_REQUEST["card_number"];
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $requestId=$request["requestId"];
        $mobilePhone=$request["mobilePhone"];
        $upTime=$request["upTime"];

        $data = array();
        $data['responseId'] = $requestId;
        $data['allowopencollect'] = 0;
        $data['status'] = "error";
        $data['msg'] = "error";

        if(!empty($mobilePhone)&&!empty($devcode)){
            $dev=M('dev');
            $devdata=$dev->where("code='".$devcode."'")->find();
            if(!empty($devdata)){
                $register=M('register');
                $registerinfo=$register->where("account='".$mobilePhone."'")->find();
                if(!empty($registerinfo)){
                    $registerproperty=M('registerproperty');
                    $registerpropertydata=$registerproperty->where("userid=".$registerinfo['id']." and propertyid=".$devdata['propertyid'])->find();
                    if(!empty($registerpropertydata)){
                        if($registerpropertydata['roleid']==1){
                            $data['allowopencollect'] = 1;
                        }else{
                            $data['allowopencollect'] = 0;
                        }

                        $data['userName'] = $registerinfo['name'];
                        $data['userId'] = $registerinfo['id'];
                        $data['mobilePhone'] = $registerinfo['account'];
                        $data['status'] = "success";
                        $data['msg'] = "success";
                    }else{
//                        $data['allowopencollect'] = 0;
//                        $data['msg'] = "不允许投递";
                        $newdata['userid']=$registerinfo['id'];
                        $newdata['propertyid']=$devdata['propertyid'];
                        $newdata['operatorid']=$devdata['operatorid'];
                        $newdata['point']=0;
                        $newdata['roleid']=0;
                        $newdata['accumulatedpoint']=0;
                        $registerproperty->add($newdata);

                        $data['allowopencollect'] = 0;
                        $data['userName'] = $registerinfo['name'];
                        $data['userId'] = $registerinfo['id'];
                        $data['mobilePhone'] = $registerinfo['account'];
                        $data['status'] = "success";
                        $data['msg'] = "success";
                    }
                }else{
                    $data['msg'] = "用户不存在";
                }
            }else{
                $data['msg'] = "设备不存在";
            }
        }else{
            $data['msg'] = "参数非法";
        }

        $res = json_encode($data);
        \Think\Log::record("getMobilePhoneUser res=".$res,"INFO");
        echo $res;die;
        return;
    }
}