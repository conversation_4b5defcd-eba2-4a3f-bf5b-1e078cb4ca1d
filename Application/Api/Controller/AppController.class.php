<?php
namespace Api\Controller;
use Api\Common\CommonController;
use Common\Common\CommonDao;
class AppController extends CommonController {
    public function openDoor(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devcode=$request["devcode"];
        $wxuserid=$request["wxuserid"];
        $oprType=$request["oprType"];
        $rubbishType=$request["rubbishType"];

        $register=M('register');
        $registerinfo=$register->where("id=".$wxuserid)->find();
        if(empty($registerinfo)){
            $this->output_error("","1012","用户不存在");
            return;
        }

        $devcode=$this->getDevcodeFromRandcode($devcode);

        if(empty($devcode)){
            $this->output_error("","1024","二维码错误");
            return;
        }

        $dev=M('dev');
        $devdata=$dev->where("code='".$devcode."'")->find();
        if(empty($devdata)){
            $this->output_error("","1025","设备不存在");
            return;
        }
        $propertyid=$devdata['propertyid'];
        if(empty($propertyid)){
            $this->output_error("","1026","设备数据配置错误，未指定物业");
            return;
        }
        $user=M('user');
        $userdata=$user->where("userid=".$propertyid)->find();
        if(empty($userdata)){
            $this->output_error("","1026","设备数据配置错误，物业不存在");
            return;
        }
        $operatoruserid=$userdata['operatoruserid'];

        $registerproperty=M('registerproperty');

        $registerpropertydata=$registerproperty->where('userid='.$registerinfo['id'].' and propertyid='.$propertyid)->find();
        if(empty($registerpropertydata)){
            $newdata111['selected']=0;
            $registerproperty->where('userid='.$registerinfo['id'])->save($newdata111);

            $registerpropertynew['userid']=$registerinfo['id'];
            $registerpropertynew['propertyid']=$propertyid;
            $registerpropertynew['operatorid']=$operatoruserid;
            $registerpropertynew['point']=0;
            $registerpropertynew['roleid']=0;
            $registerpropertynew['selected']=1;
            $registerproperty->add($registerpropertynew);
            $carduserid=null;
        }else{
            $carduserid=$registerpropertydata['carduserid'];
        }

        $retCode=$this->doopendoor($devcode,$wxuserid,$carduserid,$oprType,$rubbishType);

//        $this->output_data("开门成功");
//        return;

        if($retCode!="0"){
            $this->output_error("",$retCode,"开门失败");
        }else{
            $this->output_data("开门成功");
        }
        return;
    }

    private function doopendoor($devcode,$wxuserid,$userid,$oprType,$rubbishType){
        if(empty($userid)){
            $userid="";
        }
        if(empty($wxuserid)){
            $wxuserid="";
        }
        $req = array();
        $req['devcode'] = $devcode;
        $req['rubbishType'] = $rubbishType;
        $req['oprType'] = $oprType;
        $req['userid'] = $userid;
        $req['wxuserid'] = $wxuserid;

        $url = $this::server_url."openDoor.do";
        \Think\Log::record($url,"INFO");
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("openDoor res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);
//        if("0"!=$res["retCode"]){
//            return "1008";
//        }
        return $res["retCode"];
    }

    private function getDevcodeFromRandcode($devcode){
        if(empty($devcode)){
            return null;
        }
        $devcodes=explode("-",$devcode,2);
        if(empty($devcodes)||count($devcodes)!=2){
//            $this->output_error("","1024","二维码错误");
            return null;
        }

        $devcode=$devcodes[0];
        $randcode=$devcodes[1];
        if(empty($devcode)||empty($randcode)){
            return null;
        }

        $qrrandomcode=M('qrrandomcode');
        $qrrandomcodedata=$qrrandomcode->where("randcode='".$randcode."'")->order("createtime desc")->limit(1)->select();
        if(empty($qrrandomcodedata)){
            return null;
        }

        $createtime=$qrrandomcodedata[0]['createtime'];
        if(empty($createtime)||$createtime<(time()-600)){
            $qrrandomcode->where("id=".$qrrandomcodedata[0]['id'])->delete();
            return null;
        }
        return $devcode;
    }

    public function bindwx(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $carduserid=$request["carduserid"];
        $wxuserid=$request["wxuserid"];

        $card=M('card');
        $carddata=$card->where("id=".$carduserid)->find();
        if(empty($carddata)){
            $this->output_error("","1022","卡号不存在");
            return;
        }

        $register=M('register');
        $registerinfo=$register->where("id=".$wxuserid)->find();
        if(empty($registerinfo)){
            $this->output_error("","1012","用户不存在");
            return;
        }

        $propertyid=$carddata['propertyid'];
        $operatorid=$carddata['operatorid'];

        if(empty($propertyid)||empty($operatorid)){
            $this->output_error("","1012","卡信息配置错误");
            return;
        }

//        if(!empty($registerinfo['propertyid'])&&$registerinfo['propertyid']!=$carddata['propertyid']){
//            $this->output_error("","1032","无法绑定其他物业的小程序用户");
//            return;
//        }
//
//        if(empty($registerinfo['propertyid'])){
//            $newdata['propertyid']=$carddata['propertyid'];
//            $newdata['operatorid']=$carddata['operatorid'];
//        }
//
//        $newdata['carduserid']=$carduserid;
//        $register->where("id=".$wxuserid)->save($newdata);

        $registerproperty=M('registerproperty');
        $registerpropertydata1=$registerproperty->where("carduserid=".$carduserid." and cardmasterflag=1")->select();
        if(!empty($registerpropertydata1)){
            $cardmasterflag=0;
        }else{
            $cardmasterflag=1;
        }

        $registerpropertydata=$registerproperty->where("userid=".$wxuserid." and propertyid=".$propertyid)->select();
        if(empty($registerpropertydata)){
            $newdata['userid']=$wxuserid;
            $newdata['propertyid']=$carddata['propertyid'];
            $newdata['operatorid']=$carddata['operatorid'];
            $newdata['carduserid']=$carduserid;
            $newdata['point']=0;
            $newdata['roleid']=0;
            $newdata['selected']=0;
            $newdata['cardmasterflag']=$cardmasterflag;
            $registerproperty->add($newdata);
        }else{
            $dbcarduserid=$registerpropertydata['carduserid'];
            if(empty($dbcarduserid)){
                $newdata['operatorid']=$carddata['operatorid'];
                $newdata['carduserid']=$carduserid;
                $newdata['cardmasterflag']=$cardmasterflag;
                $registerproperty->where("id=".$registerpropertydata['id'])->save($newdata);
            }else if($dbcarduserid!=$carduserid){
                $newdata['operatorid']=$carddata['operatorid'];
                $newdata['carduserid']=$carduserid;
                $newdata['cardmasterflag']=$cardmasterflag;
                $registerproperty->where("id=".$registerpropertydata['id'])->save($newdata);
            }
        }

        $res['cardnum']=$carddata['cardnumber'];
        $this->output_data($res);
        return;
    }
    public function startCharge(){
        $userid=I('userid');
        $pileid=I('pileid');
        $portnum=I('portnum');
        $prepayment=I('prepayment');

        $this->output_data(null);
    }

    public function getAroundStationlist(){
        $offset=I('offset');
        $limit=I('limit');
        $lat=I('lat');
        $lng=I('lng');
        $dist=I('dist');


        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=50;
        }
        if(empty($lat)){
            $lat=34.595213;
        }
        if(empty($lng)){
            $lng=119.187327;
        }
        if(empty($dist)){
            $dist=20*1000;// 默认20公里矩形
        }

        $lng_max = $lng+$dist*0.00001141;
        $lng_min = $lng-$dist*0.00001141;
        $lat_max = $lat+$dist*0.00000899;
        $lat_min = $lat-$dist*0.00000899;
        $cond = 'lat>='.$lat_min.' and lat<='.$lat_max.' and lng>='.$lng_min.' and lng<='.$lng_max;


        $station=M('station');
        $result=$station->where($cond)->order('stationid desc')->limit($offset,$limit)->select();

        $info = array();
        foreach($result as $vo) {
            $distance = $this->getDistance($lat, $lng, $vo['lat'], $vo['lng']);
            $vo['distance'] = round($distance / 1000, 2);
            $info[] = $vo;
        }
        $info = $this->my_sort($info,'distance');
        $this->output_data($info);
    }

    public function getStationinfo(){
        $stationid=I('stationid');
        $code=I('code');
        $lat=I('lat');
        $lng=I('lng');
        if(empty($stationid)&&empty($code)){
            $this->output_data(null);
            return;
        }

        $station=M('station');
        if(!empty($stationid)){
            $cond['stationid']=$stationid;
        }else if(!empty($code)){
            $cond['code']=$code;
        }else{
            $this->output_data(null);
            return;
        }
        $result=$station->where($cond)->find();

        if(empty($result)){
            $this->output_data(null);
            return;
        }

        $pile=M('pile');
        $pileinfos=$pile->where("stationid=".$result['stationid'])->select();
        if(!empty($pileinfos)){
            $result['piles']=$pileinfos;
        }

        if(!empty($lat)&&!empty($lng)){
            $distance = $this->getDistance($lat, $lng, $result['lat'], $result['lng']);
            $result['distance'] = round($distance / 1000, 2);
        }

        $this->output_data($result);
    }

    public function getDevlist(){
        // 经度
        $longitude = $_REQUEST["lng"];
        if( $longitude == ""){
            $this->output_error("longitude is empty");
            return;
        }

        //纬度
        $latitude = $_REQUEST["lat"];
        if( $latitude == ""){
            $this->output_error("latitude is empty");
            return;
        }

        $dist = $_REQUEST["dist"];
        if(empty($dist)){
            $dist = 1000*1000;
        }

        $dist = 1000*1000*100;

        $startPage = $_REQUEST["startPage"];
        if(empty($startPage)){
            $startPage = 0;
        }

        $pageSize = $_REQUEST["pageSize"];
        if(empty($pageSize)){
            $pageSize = 20;
        }
        $startIndex=$startPage*$pageSize;
        $sortid = I("sortid");
        if(empty($sortid)){
            $sortid = 0;
        }

        $propertyid=$_REQUEST["propertyid"];

        $lng_max = $longitude+$dist*0.00001141;
        $lng_min = $longitude-$dist*0.00001141;
        $lat_max = $latitude+$dist*0.00000899;
        $lat_min = $latitude-$dist*0.00000899;
        $time=time()-80;
        $cond = 'lat>='.$lat_min.' and lat<='.$lat_max.' and lng>='.$lng_min.' and lng<='.$lng_max;

//        if(!empty($propertyid)&&$propertyid!="undefined"&&$propertyid!="null"){
//            $cond="propertyid=".$propertyid." and ".$cond;
//        }

//        $model = new Model();
        $dev=M('dev');
        $res=$dev->where($cond)->limit($startIndex,$pageSize)->select();
//        $sql="select id,code,name,status,address,lat,lng from think_dev where ".$cond." limit ".$startIndex.",".$pageSize;
//        $res = $model->query($sql);
//        error_log($model->_sql());

        $info = array();
        foreach($res as $vo){
            if(!empty($vo['heartbeattime']) && (time()-$vo['heartbeattime'])<C('onlineTimeDelta')){
                $vo['onlinestatusname']="在线";
                $vo['onlinestatus']=1;
            }else{
                $vo['onlinestatusname']="离线";
                $vo['onlinestatus']=0;
            }

            $distance = $this->getDistance($latitude,$longitude,$vo['lat'],$vo['lng']);
            $vo['distance'] = round($distance/1000,2);
            if($distance <= $dist){
                $info[] = $vo;
            }
        }
//        if($sortid==0){
//            $info = $this->my_sort($info,'distance');
//        }

        $this->output_data($info);
    }

    public function getDevInfo(){
        $id = $_REQUEST["id"];
        if( empty($id)){
            $this->output_error("id is empty");
            return;
        }
        // 经度
        $longitude = $_REQUEST["lng"];
        if( $longitude == ""){
            $this->output_error("longitude is empty");
            return;
        }

        //纬度
        $latitude = $_REQUEST["lat"];
        if( $latitude == ""){
            $this->output_error("latitude is empty");
            return;
        }

        $dev=M('dev');
        $vo=$dev->where("id=".$id)->find();
        if(!empty($vo['heartbeattime']) && (time()-$vo['heartbeattime'])<C('onlineTimeDelta')){
            $vo['onlinestatusname']="在线";
            $vo['onlinestatus']=1;
        }else{
            $vo['onlinestatusname']="离线";
            $vo['onlinestatus']=0;
        }

        $distance = $this->getDistance($latitude,$longitude,$vo['lat'],$vo['lng']);
        $vo['distance'] = round($distance/1000,2);

        $operatorid=$vo['operatorid'];
        $rublishtypes=explode(",",$vo['rublishtypes']);
        if(!empty($operatorid)&&!empty($rublishtypes)){
            $operatorpoint=M('operatorpoint');
            $rubbishtypepoint=M('rubbishtypepoint');
            $operatorpointlist=$operatorpoint->where("userid=".$operatorid)->select();
            $pointlist=array();
            if(!empty($operatorpointlist)){
                foreach ($operatorpointlist as &$operatorpointlist_elem){
                    $rubbishtype=$operatorpointlist_elem['rubbishtype'];

                    $contains=false;
                    foreach ($rublishtypes as $rublishtypes_elem){
                        if($rublishtypes_elem==$rubbishtype){
                            $contains=true;
                            break;
                        }
                    }
                    if(!$contains){
                        continue;
                    }

                    if(!empty($rubbishtype)){
                        $rubbishtypepointdata=$rubbishtypepoint->where("rubbishtype='".$rubbishtype."'")->find();
                        if(!empty($rubbishtypepointdata)){
                            $operatorpointlist_elem['rubbishtypename']=$rubbishtypepointdata['name'];
                            if($operatorpointlist_elem['pointrule']==1){
                                $operatorpointlist_elem['huanbaojin']=round(($operatorpointlist_elem['point'])/100,2);
                            }else{
                                $operatorpointlist_elem['huanbaojin']=round(($operatorpointlist_elem['point']*10)/100,2);
                            }
                            $pointlist[]=$operatorpointlist_elem;
                        }
                    }
                }
            }
            $vo['operatorpointlist']=$pointlist;
        }

        $this->output_data($vo);
    }

    public function getRublishtype(){
        $devcode=I('devcode');
        if(empty($devcode)){
            $this->output_commonerror("devcode is empty");
            return;
        }

        $devcode=$this->getDevcodeFromRandcode($devcode);
        if(empty($devcode)){
            $this->output_error("","1024","二维码错误");
            return;
        }

        $dev=M('dev');
        $devdata=$dev->where("code='".$devcode."'")->find();
        if(empty($devdata)){
            $this->output_commonerror("设备不存在");
            return;
        }
        $rublishtypes=$devdata['rublishtypes'];
        $rublishtypelist=array();
        if(!empty($rublishtypes)){
            $rublishtype1=explode(',',$rublishtypes);
            if(!empty($rublishtype1)){
                $rubbishtypepoint=M('rubbishtypepoint');
                $rubbishtypepointall=$rubbishtypepoint->order('rubbishtype asc')->select();
                if(!empty($rubbishtypepointall)){
                    foreach ($rublishtype1 as $rublishtype1_elem){
                        $rublishtype1_elem=trim($rublishtype1_elem);
                        if(!empty($rublishtype1_elem)){
                            $typename=null;
                            $typepoint=0;
                            foreach ($rubbishtypepointall as $rubbishtypepointall_elem){
                                if($rubbishtypepointall_elem['rubbishtype']==$rublishtype1_elem){
                                    $typename=$rubbishtypepointall_elem['name'];
                                    $typepoint=$rubbishtypepointall_elem['point'];
                                    break;
                                }
                            }
                            if(empty($typename)){
                                continue;
                            }
                            $elem = array();
                            $elem['type']=$rublishtype1_elem;
                            $elem['name']=$typename;
                            $elem['point']=$typepoint;
                            $rublishtypelist[]=$elem;
                        }
                    }
                }
            }
        }
        $devdata['rublishtypelist']=$rublishtypelist;
        $this->output_data($devdata);
        return;
    }

    public function getPileinfo(){
        $id=I('id');
        $code=I('code');
        if(empty($id)&&empty($code)){
            $this->output_data(null);
            return;
        }

        $pile=M('pile');
        if(!empty($id)){
            $cond['id']=$id;
        }else if(!empty($code)){
            $cond['code']=$code;
        }else{
            $this->output_data(null);
            return;
        }
        $result=$pile->where($cond)->find();

        if(empty($result)){
            $this->output_data(null);
            return;
        }
        $this->output_data($result);
    }

    public function getChargeloglist(){
        $offset=I('offset');
        $limit=I('limit');
        $userid=I('userid');

        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=50;
        }
        if(empty($userid)){
            $this->output_data(null);
            return;
        }

        $cond['userid']=$userid;
        $chargelog=M('chargelog');
        $result=$chargelog->where($cond)->order('id desc')->limit($offset,$limit)->select();

        $this->output_data($result);
    }

    public function getChargeloginfo(){
        $id=I('id');
        if(empty($id)){
            $this->output_data(null);
            return;
        }

        $chargelog=M('chargelog');
        $cond['id']=$id;
        $result=$chargelog->where($cond)->find();

        if(empty($result)){
            $this->output_data(null);
            return;
        }
        $this->output_data($result);
    }

    function getDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6367000; //approximate radius of earth in meters
        $lat1 = ($lat1 * pi() ) / 180;
        $lng1 = ($lng1 * pi() ) / 180;
        $lat2 = ($lat2 * pi() ) / 180;
        $lng2 = ($lng2 * pi() ) / 180;

        $calcLongitude = $lng2 - $lng1;
        $calcLatitude = $lat2 - $lat1;
        $stepOne = pow(sin($calcLatitude / 2), 2) + cos($lat1) * cos($lat2) * pow(sin($calcLongitude / 2), 2);
        $stepTwo = 2 * asin(min(1, sqrt($stepOne)));
        $calculatedDistance = $earthRadius * $stepTwo;

        return round($calculatedDistance);
    }

    function my_sort($arrays,$sort_key,$sort_order=SORT_ASC,$sort_type=SORT_NUMERIC ){
        if(is_array($arrays)){
            foreach ($arrays as $array){
                if(is_array($array)){
                    $key_arrays[] = $array[$sort_key];
                }else{
                    return false;
                }
            }
        }else{
            return false;
        }
        array_multisort($key_arrays,$sort_order,$sort_type,$arrays);
        return $arrays;
    }


    public function uploadfile(){
        $userid=$_POST['userid'];
        $woid=$_POST['woid'];
        $itemid=$_POST['itemid'];

        $files=$_FILES;
        \Think\Log::record("FILES: ".json_encode($files),"INFO");
        foreach ($files as $v){
            $file_ext=end(explode('.',$v['name']));
            $new_name="Public/Upload/wofile/".time().rand(999,10000).".".$file_ext;
            move_uploaded_file($v['tmp_name'],$new_name);
        }
        $this->output_data(__ROOT__."/".$new_name);
        return;
    }

    public function dudaotoudi(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $dudaoid=$request["dudaoid"];

        if(empty($code)){
            $this->output_error("","1","code为空");
            return;
        }
        if(empty($dudaoid)){
            $this->output_error("","1","用户未登录");
            return;
        }

        $codetemps=explode("|",$code);
        if(empty($codetemps)||count($codetemps)<2){
            $this->output_error("","1","二维码非法");
            return;
        }

        $cardnumber=$codetemps[0];
        $rubbishtype=$codetemps[1];

        if(empty($cardnumber)||strlen($rubbishtype)==0){
            $this->output_error("","1","二维码非法");
            return;
        }

        $card=M('card');
        $cond['cardnumber']=$cardnumber;
        $carddata=$card->where($cond)->find();
        if(empty($carddata)||empty($carddata['propertyid'])){
            $this->output_error("","1","卡号不存在或未归属物业");
            return;
        }

        $register=M('register');
        $dudaodata=$register->where("id=".$dudaoid)->find();
        if(empty($dudaodata)){
            $this->output_error("","1","督导员不存在");
            return;
        }

        $registerproperty=M('registerproperty');
        $cond1['userid']=$dudaoid;
        $cond1['propertyid']=$carddata['propertyid'];
        $registerpropertydata=$registerproperty->where($cond1)->find();
        if(empty($registerpropertydata)){
            $this->output_error("","1","当前督导员不属于卡号所在物业");
            return;
        }

        $currenttime=time();

        $point=1;
        $throwweight=100;
        $rubbishtypename=$this->getrubbishtypename($rubbishtype);

        $newdata1['carduserid']=$carddata['id'];
        $newdata1['point']=$point;
        $newdata1['throwtime']=$currenttime;
        $newdata1['throwweight']=$throwweight;
        $newdata1['throwmode']=0;
        $newdata1['createtime']=$currenttime;
        $newdata1['rubbishtype']=$this->getrubbishtype($rubbishtype);
        $newdata1['propertyid']=$carddata['propertyid'];
        $newdata1['operatorid']=$carddata['operatorid'];
        $newdata1['cardnum']=$cardnumber;
        $newdata1['throwmonth']=date('Ym', $currenttime);
        $newdata1['throwdate']=date('Ymd', $currenttime);
//        $newdata1['areaid1']=1;
//        $newdata1['areaid2']=1;
//        $newdata1['areaid3']=1;
//        $newdata1['areaid4']=1;
        $newdata1['throwtype']=1;
        $newdata1['dudaoid']=$dudaoid;
        $throwlog=M('throwlog');
        $throwlog->add($newdata1);

        $card->execute("update think_card set point=point+".$point." where id=".$carddata['id']);

        $card=M('card');
        $carddata1=$card->where("id=".$carddata['id'])->find();

        $res['point']=$point;
        $res['cardnumber']=$cardnumber;
        $res['rubbishtypename']=$rubbishtypename;
        $res['currentpoint']=$carddata1['point'];
        $this->output_data($res);
        return;
    }

    public function saomabangka(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $userid=$request["userid"];

        if(empty($code)){
            $this->output_error("","1","code为空");
            return;
        }
        if(empty($userid)){
            $this->output_error("","1","用户未登录");
            return;
        }

        $codetemps=explode("|",$code);
        if(empty($codetemps)||count($codetemps)<2){
            $this->output_error("","1","二维码非法");
            return;
        }

        $cardnumber=$codetemps[0];
        $rubbishtype=$codetemps[1];

        if(empty($cardnumber)||strlen($rubbishtype)==0){
            $this->output_error("","1","二维码非法");
            return;
        }

        $card=M('card');
        $cond['cardnumber']=$cardnumber;
        $carddata=$card->where($cond)->find();
        if(empty($carddata)||empty($carddata['propertyid'])){
            $this->output_error("","1","卡号不存在或未归属物业");
            return;
        }

        $register=M('register');
        $registerdata=$register->where("id=".$userid)->find();
        if(empty($registerdata)){
            $this->output_error("","1","用户不存在");
            return;
        }

        $cond22['userid']=$userid;
        $cond22['propertyid']=$carddata['propertyid'];
        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where($cond22)->find();

        if(!empty($registerpropertydata)){
            if($carddata['id']==$registerpropertydata['carduserid']){
                $this->output_data("绑定成功，重复绑定");
                return;
            }else{
                $this->output_error("","1","绑定失败，已绑定同一物业其他卡号");
                return;
            }
        }else{
            $registerpropertydb1=$registerproperty->where("carduserid=".$carddata['id'])->find();
            $registerpropertydb2=$registerproperty->where("userid=".$userid)->find();
            $newdata['userid']=$userid;
            $newdata['propertyid']=$carddata['propertyid'];
            $newdata['operatorid']=$carddata['operatorid'];
            $newdata['carduserid']=$carddata['id'];
            $newdata['point']=0;
            $newdata['roleid']=0;
            if(empty($registerpropertydb1)){
                $newdata['cardmasterflag']=1;
            }else{
                $newdata['cardmasterflag']=0;
            }

            if(empty($registerpropertydb2)){
                $newdata['selected']=1;
            }else{
                $newdata['selected']=0;
            }
            $registerproperty->add($newdata);
        }

        $this->output_data("");
        return;
    }

    private function getrubbishtype($rubbishtype){
        if($rubbishtype==1){
            return 1;
        }else if($rubbishtype==2){
            return 2;
        }else if($rubbishtype==3){
            return 4;
        }else if($rubbishtype==4){
            return 5;
        }else{
            return 2;
        }
    }

    private function getrubbishtypename($rubbishtype){
        $category=M('category');
        $categorydata=$category->where("id=".$rubbishtype)->find();
        if(empty($categorydata)){
            return "未知";
        }
        return $categorydata['name'];
    }
    //填充统计数据
    public function test(){
        //admin
        $this->dealdata(1,1);
        $map['roleid'] = 2;
        $user = M("user")->where($map)->select();
        foreach($user as $k=>$v){
            $this->dealdata($v['userid'],$v['roleid']);
        }
        dump("成功");
    }
    public function dealdata($userid,$roleid){
        $map['uid'] = $userid;
        $map['date'] = date("Y-m-d",time());
        $datastatistics = M("datastatistics")->where($map)->find();
        if(!empty($datastatistics)){
            M("datastatistics")->where("id=".$datastatistics['id'])->delete();
        }
        //if(empty($datastatistics)){
            $data['date'] = date("Y-m-d",time());
            $data['createtime'] = time();
            $data['uid'] = $userid;
            $dataid = M("datastatistics")->add($data);
            $this->getDashboardData($userid,$roleid,$dataid);
            $this->getZongliang7tian($userid,$roleid,$dataid);
            $this->getToufangcishu7tian($userid,$roleid,$dataid);
            $this->getZongliangbianhua($userid,$roleid,$dataid);
        //}
    }
    function getDashboardData($userid,$roleid,$dataid){
        
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $monthstart=strtotime(date("Y-m-01"),time());

        $delta=$this->getDelta($userid);
        $time=time()-C('onlineTimeDelta');
        $throwlog=M('dev');
        if($roleid==1){
            $totaldev=$throwlog->query('select count(1) as num from think_dev');
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where isfull=1');
        }else if($roleid==2){
            $totaldev=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid);
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid.' and heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid.' and isfull=1');
        }else{
            $totaldev=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid);
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid.' and heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid.' and isfull=1');
        }
//        \Think\Log::record("isfull=".$isfull,"INFO");
        $total=0;
        $online=0;
        if(!empty($totaldev)){
            $total=$totaldev[0]['num'];
            if(empty($total)){
                $total=0;
            }
            $online=$onlinedev[0]['num'];
            if(empty($online)){
                $online=0;
            }
        }

        $res['totaldev']=$total*$delta;
        $res['onlinedev']=$online*$delta;
        $res['offlinedev']=($total-$online)*$delta;
        $res['alarmdev']=0;

        $isfullnum=0;
        if(!empty($isfull)){
            $isfullnum=$isfull[0]["num"];
        }
        $res['fulldev']=$isfullnum*$delta;
        $res['unfulldev']=$res['totaldev']-$res['fulldev'];

        $register=M('register');
        $card=M('card');
        $totalwpuser=$register->count();
        $totalcarduser=$card->count();
        $totalwpuser_monthadd=$register->where("createtime>=".$monthstart)->count();
        $totalcarduser_monthadd=$card->where("createtime>=".$monthstart)->count();
        $res['totalwpuser']=$totalwpuser*$delta;
        $res['totalcarduser']=$totalcarduser*$delta;

        $user=M('user');
        $ttotalresidential=$user->where("roleid=3")->count();
        $ttotalresidential_monthadd=$user->where("roleid=3 and createtime>=".$monthstart)->count();

        $res['totalfamily']=$totalcarduser*$delta;
        $res['totalresidential']=$ttotalresidential*$delta;
        $res['totaluser']=($totalcarduser+$totalwpuser)*$delta;

        $res['totalfamily_monthadd']=$totalcarduser_monthadd*$delta;
        $res['totalresidential_monthadd']=$ttotalresidential_monthadd*$delta;
        $res['totaluser_monthadd']=($totalcarduser_monthadd+$totalwpuser_monthadd)*$delta;

        // 今天时间戳
        $today = strtotime(date("Y-m-d"),time());
        $today_str=date("Y-m-d H:i:s",$today);

        // 昨天时间戳
        $yesterday = $today-(3600*24);
        $yesterday_str=date("Y-m-d H:i:s",$yesterday);

        $throwlog=M('throwlog');
        $zhilei_zongliang=$throwlog->where("rubbishtype='6'")->sum("throwweight");
        $zhilei_zuori=$throwlog->where("rubbishtype='6' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $zhilei_jinri=$throwlog->where("rubbishtype='6' and createtime>".$today)->sum("throwweight");

        $suliao_zongliang=$throwlog->where("rubbishtype='A'")->sum("throwweight");
        $suliao_zuori=$throwlog->where("rubbishtype='A' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $suliao_jinri=$throwlog->where("rubbishtype='A' and createtime>".$today)->sum("throwweight");

        $jinshu_zongliang=$throwlog->where("rubbishtype='8'")->sum("throwweight");
        $jinshu_zuori=$throwlog->where("rubbishtype='8' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $jinshu_jinri=$throwlog->where("rubbishtype='8' and createtime>".$today)->sum("throwweight");

        $fangzhi_zongliang=$throwlog->where("rubbishtype='7'")->sum("throwweight");
        $fangzhi_zuori=$throwlog->where("rubbishtype='7' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $fangzhi_jinri=$throwlog->where("rubbishtype='7' and createtime>".$today)->sum("throwweight");

        $youdu_zongliang=$throwlog->where("rubbishtype='5'")->sum("throwweight");
        $youdu_zuori=$throwlog->where("rubbishtype='5' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $youdu_jinri=$throwlog->where("rubbishtype='5' and createtime>".$today)->sum("throwweight");

        $qita_zongliang=$throwlog->where("rubbishtype='1'")->sum("throwweight");
        $qita_zuori=$throwlog->where("rubbishtype='1' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $qita_jinri=$throwlog->where("rubbishtype='1' and createtime>".$today)->sum("throwweight");

        $pingguan_zongliang=$throwlog->where("rubbishtype='C'")->count();
        $pingguan_zuori=$throwlog->where("rubbishtype='C' and createtime>".$yesterday." and createtime<=".$today)->count();
        $pingguan_jinri=$throwlog->where("rubbishtype='C' and createtime>".$today)->count();

        $res['zhilei_zongliang']=round($zhilei_zongliang*$delta/1000,2);
        $res['zhilei_zuori']=round($zhilei_zuori*$delta/1000,2);
        $res['zhilei_jinri']=round($zhilei_jinri*$delta/1000,2);

        $res['suliao_zongliang']=round($suliao_zongliang*$delta/1000,2);
        $res['suliao_zuori']=round($suliao_zuori*$delta/1000,2);
        $res['suliao_jinri']=round($suliao_jinri*$delta/1000,2);

        $res['jinshu_zongliang']=round($jinshu_zongliang*$delta/1000,2);
        $res['jinshu_zuori']=round($jinshu_zuori*$delta/1000,2);
        $res['jinshu_jinri']=round($jinshu_jinri*$delta/1000,2);

        $res['fangzhi_zongliang']=round($fangzhi_zongliang*$delta/1000,2);
        $res['fangzhi_zuori']=round($fangzhi_zuori*$delta/1000,2);
        $res['fangzhi_jinri']=round($fangzhi_jinri*$delta/1000,2);

        $res['youdu_zongliang']=round($youdu_zongliang*$delta/1000,2);
        $res['youdu_zuori']=round($youdu_zuori*$delta/1000,2);
        $res['youdu_jinri']=round($youdu_jinri*$delta/1000,2);

        $res['qita_zongliang']=round($qita_zongliang*$delta/1000,2);
        $res['qita_zuori']=round($qita_zuori*$delta/1000,2);
        $res['qita_jinri']=round($qita_jinri*$delta/1000,2);

        $res['pingguan_zongliang']=$pingguan_zongliang;
        $res['pingguan_zuori']=$pingguan_zuori;
        $res['pingguan_jinri']=$pingguan_jinri;

        M("datastatistics")->where("id=".$dataid)->save($res);
        return;
    }
    private function getDelta($userid){
        if(empty($userid)){
            return 1;
        }
        if($userid==201){
            return 7;
        }else {
            return 1;
        }
    }
    public function getZongliang7tian($userid,$roleid,$dataid){
        
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-7*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=round($monthstatic_ele['weight']*$delta/1000,0);
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $resdata['zongliang7tian'] = json_encode($res);
            M("datastatistics")->where("id=".$dataid)->save($resdata);
            return;
        }
        return;
    }
    public function getToufangcishu7tian($userid,$roleid,$dataid){
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-7*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=$monthstatic_ele['times']*$delta;
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $resdata['toufangcishu7tian'] = json_encode($res);
            M("datastatistics")->where("id=".$dataid)->save($resdata);
            return;
        }
        return;
    }
    public function getZongliangbianhua($userid,$roleid,$dataid){
        
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-30*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=round($monthstatic_ele['weight']*$delta/1000,0);
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $resdata['ymsjfb'] = json_encode($res);
            M("datastatistics")->where("id=".$dataid)->save($resdata);
            return;
        }
        return;
    }

}