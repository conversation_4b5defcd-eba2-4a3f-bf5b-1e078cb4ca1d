<?php
namespace Api\Controller;
use Api\Common\CommonController;
use Common\Common\CommonDao;
class DevController extends CommonController {
    public function getDevInfo(){
        $devcode = $_REQUEST["devcode"];
        if( empty($devcode)){
            $this->output_error("devcode is empty");
            return;
        }

        $dev=M('dev');
        $devinfo=$dev->where("code='".$devcode."'")->find();
        if(!empty($devinfo['heartbeattime']) && (time()-$devinfo['heartbeattime'])<C('onlineTimeDelta')){
            $devinfo['onlinestatusname']="在线";
            $devinfo['onlinestatus']=1;
        }else{
            $devinfo['onlinestatusname']="离线";
            $devinfo['onlinestatus']=0;
        }

        $operatorid=$devinfo['operatorid'];
        $rublishtypes=explode(",",$devinfo['rublishtypes']);
        if(!empty($operatorid)&&!empty($rublishtypes)){
            $operatorpoint=M('operatorpoint');
            $rubbishtypepoint=M('rubbishtypepoint');
            $operatorpointlist=$operatorpoint->where("userid=".$operatorid)->select();
            $pointlist=array();
            if(!empty($operatorpointlist)){
                foreach ($operatorpointlist as &$operatorpointlist_elem){
                    $rubbishtype=$operatorpointlist_elem['rubbishtype'];

                    $contains=false;
                    foreach ($rublishtypes as $rublishtypes_elem){
                        if($rublishtypes_elem==$rubbishtype){
                            $contains=true;
                            break;
                        }
                    }
                    if(!$contains){
                        continue;
                    }

                    if(!empty($rubbishtype)){
                        $rubbishtypepointdata=$rubbishtypepoint->where("rubbishtype='".$rubbishtype."'")->find();
                        if(!empty($rubbishtypepointdata)){
                            $operatorpointlist_elem['rubbishtypename']=$rubbishtypepointdata['name'];

                            if($operatorpointlist_elem['pointrule']==1){
                                $operatorpointlist_elem['huanbaojin']=round(($operatorpointlist_elem['point'])/100,2);
                            }else{
                                $operatorpointlist_elem['huanbaojin']=round(($operatorpointlist_elem['point']*10)/100,2);
                            }
                            $pointlist[]=$operatorpointlist_elem;
                        }
                    }
                }
            }
            $devinfo['operatorpointlist']=$pointlist;
        }

        $this->output_data($devinfo);
    }
}