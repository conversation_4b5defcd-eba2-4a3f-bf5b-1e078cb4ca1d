<?php
namespace Api\Controller;
use Api\Common\CommonController;
use Common\Common\CommonDao;
use Common\Common\SmsUtil;
class JobController extends CommonController {
    public function doJob(){
        \Think\Log::record("doJob start","INFO");
        $this->adsExpireNotify();
        $this->sendStatisticMsg();
        \Think\Log::record("doJob end","INFO");
        $this->output_data('执行完成');
        return;
    }

    private function sendStatisticMsg(){
        \Think\Log::record("统计信息短信推送开始","INFO");

        $sms = new SmsUtil();
        $datatime=date('Y-m-d',time()-3600*24);
        $today = strtotime(date("Y-m-d"),time());
        $yesterday = $today-(3600*24);
        $user=M('user');
        $throwlog=M('throwlog');

        $adminuserlist=$user->where('roleid=1 and status=1 and phonenumber is not null')->select();
        if(!empty($adminuserlist)){
            $usernumdata=$throwlog->query("select count(distinct(userid)) as renshu from think_throwlog where createtime>=".$yesterday." and createtime<".$today);
            $usernum=$usernumdata[0]['renshu'];
            if(empty($usernum)){
                $usernum=0;
            }

            $thrownumdata=$throwlog->query("select count(1) as cishu from think_throwlog where createtime>=".$yesterday." and createtime<".$today);
            $thrownum=$thrownumdata[0]['cishu'];
            if(empty($thrownum)){
                $thrownum=0;
            }

            $throwweightdata=$throwlog->query("select sum(throwweight) as weight from think_throwlog where createtime>=".$yesterday." and createtime<".$today);
            $throwweight=$throwweightdata[0]['weight'];
            if(empty($throwweight)){
                $throwweight=0;
            }
            $throwweight=round($throwweight/1000,2);

            $violationnumdata=$throwlog->query("select count(1) as num from think_throwlog where violationflag=1 and createtime>=".$yesterday." and createtime<".$today);
            $violationnum=$violationnumdata[0]['num'];
            if(empty($violationnum)){
                $violationnum=0;
            }
            foreach ($adminuserlist as $adminuser){
                $phonenumber=$adminuser['phonenumber'];
                if(empty($phonenumber)||strlen($phonenumber)<11){
                    continue;
                }
                $name=$adminuser['name'];
                \Think\Log::record("sendStatisticMsg name=".$name.", phonenumber=".$phonenumber." datatime=".$datatime.", usernum=".$usernum.", thrownum=".$thrownum.", throwweight=".$throwweight.", violationnum=".$violationnum,"INFO");
                $status = $sms->sendStatisticMsg($phonenumber,$datatime,$usernum,$thrownum,$throwweight."千克",$violationnum);
                \Think\Log::record("sendStatisticMsg status=".$status,"INFO");
            }
        }

        $operatoruserlist=$user->where('roleid=2 and status=1 and phonenumber is not null')->select();
        if(!empty($operatoruserlist)){
            foreach ($operatoruserlist as $operatoruser){
                $phonenumber=$operatoruser['phonenumber'];
                if(empty($phonenumber)||strlen($phonenumber)<11){
                    continue;
                }
                $operatorid=$operatoruser['userid'];
                $name=$operatoruser['name'];
                $usernumdata=$throwlog->query("select count(distinct(userid)) as renshu from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and operatorid=".$operatorid);
                $usernum=$usernumdata[0]['renshu'];
                if(empty($usernum)){
                    $usernum=0;
                }

                $thrownumdata=$throwlog->query("select count(1) as cishu from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and operatorid=".$operatorid);
                $thrownum=$thrownumdata[0]['cishu'];
                if(empty($thrownum)){
                    $thrownum=0;
                }

                $throwweightdata=$throwlog->query("select sum(throwweight) as weight from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and operatorid=".$operatorid);
                $throwweight=$throwweightdata[0]['weight'];
                if(empty($throwweight)){
                    $throwweight=0;
                }
                $throwweight=round($throwweight/1000,2);

                $violationnumdata=$throwlog->query("select count(1) as num from think_throwlog where violationflag=1 and createtime>=".$yesterday." and createtime<".$today." and operatorid=".$operatorid);
                $violationnum=$violationnumdata[0]['num'];
                if(empty($violationnum)){
                    $violationnum=0;
                }

                \Think\Log::record("sendStatisticMsg name=".$name.", phonenumber=".$phonenumber." datatime=".$datatime.", usernum=".$usernum.", thrownum=".$thrownum.", throwweight=".$throwweight.", violationnum=".$violationnum,"INFO");
                $status = $sms->sendStatisticMsg($phonenumber,$datatime,$usernum,$thrownum,$throwweight."千克",$violationnum);
                \Think\Log::record("sendStatisticMsg status=".$status,"INFO");
            }
        }

        $propertyuserlist=$user->where('roleid=3 and status=1 and phonenumber is not null')->select();
        if(!empty($propertyuserlist)){
            foreach ($propertyuserlist as $propertyuser){
                $phonenumber=$propertyuser['phonenumber'];
                if(empty($phonenumber)||strlen($phonenumber)<11){
                    continue;
                }
                $propertyid=$propertyuser['userid'];
                $name=$propertyuser['name'];
                $usernumdata=$throwlog->query("select count(distinct(userid)) as renshu from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and propertyid=".$propertyid);
                $usernum=$usernumdata[0]['renshu'];
                if(empty($usernum)){
                    $usernum=0;
                }

                $thrownumdata=$throwlog->query("select count(1) as cishu from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and propertyid=".$propertyid);
                $thrownum=$thrownumdata[0]['cishu'];
                if(empty($thrownum)){
                    $thrownum=0;
                }

                $throwweightdata=$throwlog->query("select sum(throwweight) as weight from think_throwlog where createtime>=".$yesterday." and createtime<".$today." and propertyid=".$propertyid);
                $throwweight=$throwweightdata[0]['weight'];
                if(empty($throwweight)){
                    $throwweight=0;
                }
                $throwweight=round($throwweight/1000,2);

                $violationnumdata=$throwlog->query("select count(1) as num from think_throwlog where violationflag=1 and createtime>=".$yesterday." and createtime<".$today." and propertyid=".$propertyid);
                $violationnum=$violationnumdata[0]['num'];
                if(empty($violationnum)){
                    $violationnum=0;
                }

                \Think\Log::record("sendStatisticMsg name=".$name.", phonenumber=".$phonenumber." datatime=".$datatime.", usernum=".$usernum.", thrownum=".$thrownum.", throwweight=".$throwweight.", violationnum=".$violationnum,"INFO");
                $status = $sms->sendStatisticMsg($phonenumber,$datatime,$usernum,$thrownum,$throwweight."千克",$violationnum);
                \Think\Log::record("sendStatisticMsg status=".$status,"INFO");
            }
        }

        \Think\Log::record("统计信息短信推送结束","INFO");
    }

    private function adsExpireNotify(){
        $time1=time();
        $time2=$time1+3600*24*7;
        $ads=M('ads');
        $adslist=$ads->where('endtime>'.$time1.' and endtime<'.$time2.' and createuserid is not null and status=2')->select();
        if(empty($adslist)){
            \Think\Log::record("不存在即将过期广告","INFO");
            return;
        }
        $user=M('user');
        $sms = new SmsUtil();
        foreach ($adslist as $adslist_elem){
            $createuserid=$adslist_elem['createuserid'];
            if(empty($createuserid)){
                continue;
            }
            $createuser=$user->where('userid='.$createuserid)->find();
            if(empty($createuser)){
                continue;
            }
            $phonenumber=$createuser['phonenumber'];
            if(empty($phonenumber)||strlen($phonenumber)<11){
                continue;
            }
            $adsexpirenotifytime=$adslist_elem['adsexpirenotifytime'];
            if(!empty($adsexpirenotifytime)&&((time()-$adsexpirenotifytime)<(3600*24*2))){
                continue;
            }
            $filename='编号['.$adslist_elem['id'].']';
            $endtime=date('Y-m-d',$adslist_elem['endtime']);
            \Think\Log::record("sendAdsExpireMsg phonenumber=".$phonenumber." filename=".$filename.", endtime=".$endtime,"INFO");
            $status = $sms->sendAdsExpireMsg($phonenumber,$filename,$endtime);
            \Think\Log::record("sendAdsExpireMsg status=".$status,"INFO");
            $newdata['adsexpirenotifytime']=time();
            $ads->where('id='.$adslist_elem['id'])->save($newdata);
        }
    }
}