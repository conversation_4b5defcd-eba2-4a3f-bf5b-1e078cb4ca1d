<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
use Admin\Common\CommonUtil;
class UserMgrController extends CommonController {
    public function index(){
        $roleid=I('roleid');
        if(empty($roleid)){
            return;
        }

        if(session('roleid')==1){
            $this->assign('lookpasswd',1);
        }else{
            $this->assign('lookpasswd',0);
        }

        if($roleid==1){
            $this->assign('privilege',$this->getPrivilege(35));
            $this->assign('roleid',$roleid);
            $this->assign('lookpasswd',0);
            $this->display();
        }else if($roleid==2){
            $this->assign('privilege',$this->getPrivilege(32));
            $this->assign('roleid',$roleid);
            $this->display();
        }else if($roleid==3){
            $this->assign('privilege',$this->getPrivilege(30));
            $this->assign('roleid',$roleid);
            $this->display("indexproperty");
        }else if($roleid==4){
            $this->assign('privilege',$this->getPrivilege(56));
            $this->assign('roleid',$roleid);
            $this->display("index");
        }else if($roleid==5){
            $this->assign('privilege',$this->getPrivilege(57));
            $this->assign('roleid',$roleid);
            $this->display("index");
        }else if($roleid==6){
            $this->assign('privilege',$this->getPrivilege(30));
            $this->assign('roleid',$roleid);
            $this->display("index");
        }else if($roleid==7){
            $this->assign('privilege',$this->getPrivilege(30));
            $this->assign('roleid',$roleid);
            $area=M('area');
            $level1arealist=$area->where('parentid=-1')->select();
            $this->assign("level1arealist",$level1arealist);
            $this->display("index_regulator");
        }else{
        }
    }

    public function index_committee(){
        $userid=I('userid');
        if(empty($userid)){
            return;
        }
        $this->assign('propertyid',$userid);
        $this->assign('roleid',6);
        $user=M('user');
        $propertydata=$user->where('userid='.$userid)->find();
        if(empty($propertydata)){
            return;
        }
        $this->assign('pagetitle',"居委会账号 - 所属物业[".$propertydata['name'].']');

        if(session('roleid')==1){
            $this->assign('lookpasswd',1);
        }else{
            $this->assign('lookpasswd',0);
        }
        $this->assign('privilege',$this->getPrivilege(30));
        $this->display();
//        $user=M('user');
//        $committeelist=$user->where('propertyid='.$userid)->select();
    }

    public function add(){
        $roleid=I('roleid');
        if(empty($roleid)){
            return;
        }

        $this->assign('roleid',$roleid);
        if($roleid==3){
            $user=M('user');
            $operatorlist=$user->where("roleid=2")->select();
            $this->assign('operatorlist',$operatorlist);

            $roleid=session("roleid");
            if($roleid==1){
                $this->assign("canmodoperator",1);
            }else{
                $this->assign("canmodoperator",0);
            }

            $this->display("addproperty");
        }else{
            if($roleid==4){
                $this->assign("pagetile","添加财务");
            }else if($roleid==5){
                $this->assign("pagetile","添加广告商");
            }else if($roleid==6){
                $propertyid=I('propertyid');
                if(empty($propertyid)){
                    return;
                }
                $user=M('user');
                $propertydata=$user->where('userid='.$propertyid)->find();
                if(empty($propertydata)){
                    return;
                }
                $this->assign("pagetile","添加居委会管理员-所属物业[".$propertydata['name']."]");
                $this->assign("propertyid",$propertyid);
                $this->display("addcommittee");
                return;
            }else if($roleid==2){
                $this->assign("pagetile","添加运营商");
            }else if($roleid==1){
                $this->assign("pagetile","添加超级管理员");
            }else if($roleid==7){
                $this->assign("pagetile","添加政府监管员");
            }
            $this->display();
        }
    }

    public function update(){
        $userid=I('userid');
        if(empty($userid)){
            return;
        }
        $user=M('user');
        $result=$user->where("userid='".$userid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("user",$result);

        if($result['roleid']==3){
            $roleid=session("roleid");
            if($roleid==1){
                $this->assign("canmodoperator",1);
            }else{
                $this->assign("canmodoperator",0);
            }
            $user=M('user');
            $operatorlist=$user->where("roleid=2")->select();
            $this->assign('operatorlist',$operatorlist);
            $this->display("updateproperty");
        }else{
            $this->display();
        }
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $roleid=$request["roleid"];

        if(empty($roleid)){
            return;
        }
//        $username=$request["username"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $account=session('account');
        $user=M('user');
        $cond["roleid"]=$roleid;
        $cond["account"]=array("neq",$account);
        if(session("roleid")==2&&$roleid==3){
            $cond["operatoruserid"]=session("userid");
        }
        $res['total']=$user->where($cond)->count("userid");
        $result=$user->where($cond)->order('userid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return null;
        }
        $elem['rolename']=CommonDao::getRoleName($elem['roleid']);
        $elem['flagname']=CommonDao::getUserFlagName($elem['flag']);
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        $operatoruserid=$elem['operatoruserid'];
        if(!empty($operatoruserid)){
            $user=M('user');
            $operatordata=$user->where("userid=".$operatoruserid)->find();
            if(!empty($operatordata)){
                $elem['operatorname']=$operatordata['name'];
            }
        }
        if(empty($elem['userpwd1'])){
            $elem['userpwd1']='';
        }

        $area=M('area');

        $areaid1=$elem['areaid1'];
        if(!empty($areaid1)){
            $areadata=$area->where("areaid=".$areaid1)->find();
            if(!empty($areadata)){
                $elem['areaid1name']=$areadata['areaname'];
            }
        }
        $areaid2=$elem['areaid2'];
        if(!empty($areaid2)){
            $areadata=$area->where("areaid=".$areaid2)->find();
            if(!empty($areadata)){
                $elem['areaid2name']=$areadata['areaname'];
            }
        }
        $areaid3=$elem['areaid3'];
        if(!empty($areaid3)){
            $areadata=$area->where("areaid=".$areaid3)->find();
            if(!empty($areadata)){
                $elem['areaid3name']=$areadata['areaname'];
            }
        }
        $areaid4=$elem['areaid4'];
        if(!empty($areaid4)){
            $areadata=$area->where("areaid=".$areaid4)->find();
            if(!empty($areadata)){
                $elem['areaid4name']=$areadata['areaname'];
            }
        }
        return $elem;
    }

    public function api_getlist_committee(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $propertyid=$request["propertyid"];

        if(empty($propertyid)){
            return;
        }
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $user=M('user');
        $cond["propertyid"]=$propertyid;
        $res['total']=$user->where($cond)->count("userid");
        $result=$user->where($cond)->order('userid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $name=$request["name"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];
        $roleid=$request["roleid"];
        $operatoruserid=$request["operatoruserid"];
        $homephone=$request["homephone"];
        if(empty($account)){
            $this->output_commonerror('账号不能为空');
            return;
        }
        if(empty($userpwd)){
            $this->output_commonerror('密码不能为空');
            return;
        }
        if(empty($roleid)){
            $this->output_commonerror('角色不能为空');
            return;
        }
        if(empty($homephone)){
            $this->output_commonerror('上门回收电话不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $name=empty($name)?$account:$name;

        if(session("roleid")==2){
            $operatoruserid=session("userid");
        }

        $user=M('user');
        $data['account']=$account;
        $data['name']=$name;
        $data['userpwd']=$userpwd;
        $data['userpwd1']=$userpwd1;
        $data['status']=1;
        $data['roleid']=$roleid;
        $data['operatoruserid']=$operatoruserid;
        $data['createtime']=time();
        $data['homephone']=$homephone;

        if($roleid==7){
            $areaid1=$request["areaid1"];
            $areaid2=$request["areaid2"];
            $areaid3=$request["areaid3"];
            $areaid4=$request["areaid4"];

            $data['areaid1']=$areaid1;
            $data['areaid2']=$areaid2;
            $data['areaid3']=$areaid3;
            $data['areaid4']=$areaid4;
        }

        $user->add($data);
        $this->output_data();
        return;
    }

    public function api_add_committee(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $name=$request["name"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];
        $propertyid=$request["propertyid"];
        if(empty($account)){
            $this->output_commonerror('账号不能为空');
            return;
        }
        if(empty($userpwd)){
            $this->output_commonerror('密码不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('所属物业不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $name=empty($name)?$account:$name;

        $user=M('user');
        $data['account']=$account;
        $data['name']=$name;
        $data['userpwd']=$userpwd;
        $data['userpwd1']=$userpwd1;
        $data['status']=1;
        $data['propertyid']=$propertyid;
        $data['roleid']=6;
        $data['createtime']=time();
        $user->add($data);
        $this->output_data();
        return;
    }

    public function api_add_property(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $name=$request["name"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];
        $roleid=$request["roleid"];
        $operatoruserid=$request["operatoruserid"];
        $autopoweronoff=$request["autopoweronoff"];
        $autodooropenclose=$request["autodooropenclose"];
        $autoadjustlight=$request["autoadjustlight"];
        $autoadjustvolume=$request["autoadjustvolume"];
        if(empty($account)){
            $this->output_commonerror('账号不能为空');
            return;
        }
        if(empty($userpwd)){
            $this->output_commonerror('密码不能为空');
            return;
        }
        if(empty($roleid)){
            $this->output_commonerror('角色不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $name=empty($name)?$account:$name;

        if(session("roleid")==2){
            $operatoruserid=session("userid");
        }

        $user=M('user');
        $data['account']=$account;
        $data['name']=$name;
        $data['userpwd']=$userpwd;
        $data['userpwd1']=$userpwd1;
        $data['status']=1;
        $data['roleid']=$roleid;
        $data['operatoruserid']=$operatoruserid;
        $data['createtime']=time();
//        $data['autopoweronoff']=$autopoweronoff;
        $data['autodooropenclose']=$autodooropenclose;
//        $data['autoadjustlight']=$autoadjustlight;
//        $data['autoadjustvolume']=$autoadjustvolume;
        $user->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $account=$request["account"];
        $name=$request["name"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];
        $operatoruserid=$request["operatoruserid"];
        $homephone=$request["homephone"];
        if(empty($userid)){
            $this->output_commonerror('用户ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('用户账号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($homephone)){
            $this->output_commonerror('上门回收电话不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("userid=".$userid)->select();
        if(empty($result)){
            $this->output_commonerror('用户ID不存在');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."' and userid!=".$userid)->select();
        if(!empty($result)){
            $this->output_commonerror('用户账号已存在');
            return;
        }

        if(session("roleid")==2){
            $operatoruserid=session("userid");
        }

        $user=M('user');
        $data['account']=$account;
        $data['name']=$name;
        if(!empty($userpwd)){
            $data['userpwd']=$userpwd;
            $data['userpwd1']=$userpwd1;
        }
        $data['operatoruserid']=$operatoruserid;
        $data['homephone']=$homephone;
        $user->where('userid='.$userid)->save($data);
        $this->output_data("");
        return;
    }

    public function api_update_property(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $account=$request["account"];
        $name=$request["name"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];
        $operatoruserid=$request["operatoruserid"];
        $autopoweronoff=$request["autopoweronoff"];
        $autodooropenclose=$request["autodooropenclose"];
        $autoadjustlight=$request["autoadjustlight"];
        $autoadjustvolume=$request["autoadjustvolume"];
        if(empty($userid)){
            $this->output_commonerror('用户ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('用户账号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("userid=".$userid)->select();
        if(empty($result)){
            $this->output_commonerror('用户ID不存在');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."' and userid!=".$userid)->select();
        if(!empty($result)){
            $this->output_commonerror('用户账号已存在');
            return;
        }

        if(session("roleid")==2){
            $operatoruserid=session("userid");
        }

        $user=M('user');
        $data['account']=$account;
        $data['name']=$name;
        if(!empty($userpwd)){
            $data['userpwd']=$userpwd;
            $data['userpwd1']=$userpwd1;
        }
        $data['operatoruserid']=$operatoruserid;
//        $data['autopoweronoff']=$autopoweronoff;
        $data['autodooropenclose']=$autodooropenclose;
//        $data['autoadjustlight']=$autoadjustlight;
//        $data['autoadjustvolume']=$autoadjustvolume;
        $user->where('userid='.$userid)->save($data);

        CommonUtil::setParamNotify($userid);

        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $useridlist=$request["idlist"];
        if(empty($useridlist)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $dev=M('dev');
        $card=M('card');
        $register=M('register');
        foreach ($useridlist as $userid){
            $userdb=$user->where('userid='.$userid)->find();
            if(empty($userdb)){
                continue;
            }
            $roleid=$userdb['roleid'];
            if($roleid==2){
                $propertyuser=$user->where('operatoruserid='.$userid)->select();
                if(!empty($propertyuser)){
                    $this->output_commonerror('请先删除运营商'.$userdb['name'].'名下的物业用户');
                    return;
                }
            }else if($roleid==3){
                $devdata=$dev->where("propertyid=".$userid)->select();
                if(!empty($devdata)){
                    $this->output_commonerror('请先删除物业'.$userdb['name'].'名下的设备');
                    return;
                }
                $carduser=$card->where("propertyid=".$userid." and status=0")->select();
                if(!empty($carduser)){
                    $this->output_commonerror('请先删除物业'.$userdb['name'].'名下的卡号');
                    return;
                }
                $registeruser=$register->where("propertyid=".$userid)->select();
                if(!empty($registeruser)){
                    $this->output_commonerror('请先删除物业'.$userdb['name'].'名下的小程序用户');
                    return;
                }
            }
            $user->where('userid='.$userid)->delete();
        }
        $this->output_data("");
        return;
    }

    public function addarea4(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $area3id=$request["area3id"];
        $areaname=$request["areaname"];
        if(empty($area3id)){
            $this->output_error('','1','请选择区县');
            return;
        }
        if(empty($areaname)){
            $this->output_error('','1','名称不能为空');
            return;
        }

        $area=M('area');
        $areadata['areacode']=time();
        $areadata['areaname']=$areaname;
        $areadata['level']=4;
        $areadata['parentid']=$area3id;
        $area->add($areadata);

        $this->output_data("");
        return;
    }
}