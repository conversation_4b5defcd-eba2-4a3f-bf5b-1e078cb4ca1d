<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;

class WoXSTaskMgrController extends CommonController {
    public function index(){
        if(I('wkotype')==1){
            $this->assign('privilege',$this->getPrivilege(25));
        }else{
            $this->assign('privilege',$this->getPrivilege(27));
        }

        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->display();
    }

    public function add(){
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->assign("targetlevels",CommonDao::getAllTargetlevel());
        $this->assign("cityList",CommonDao::getAllCitys());
        $this->assign("maintenanceList",CommonDao::getAllMaintenances());
//        $this->assign("targetstations",CommonDao::getAlltargetstations());
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $wotask=M('wotask');
        $result=$wotask->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $result['taskinterval']=$result['taskinterval']/(24*60);
        $result['woistartdelay']=$result['woistartdelay']/(24*60);
        $result['woitimelength']=$result['woitimelength']/(24*60);
        \Think\Log::record(json_encode($result),"INFO");
        $this->assign("wotask",$result);

        $this->assign("targetlevels",CommonDao::getAllTargetlevel());
        $this->assign("cityList",CommonDao::getAllCitys());
        $this->assign("maintenanceList",CommonDao::getAllMaintenances());
        $this->assign("wkotypename",CommonDao::getWotypeName($result['type']));
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $wkotype=$request["wkotype"];
//        $wotaskname=$request["wotaskname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if(empty($wkotype)){
            $this->output_commonerror('wkotype不能为空');
            return;
        }
        $wotask=M('wotask');
        $res['total']=$wotask->where("wotype=".$wkotype)->count("id");
//        $cond['roleid']=2;
        $result=$wotask->where("wotype=".$wkotype)->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return null;
        }
        if(!empty($elem['create_userid'])){
            $user=M('user');
            $userdata=$user->where("userid=".$elem['create_userid'])->find();
            $elem['create_username']=$userdata['name'];
        }
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $starttime=$elem['starttime'];
        if(!empty($starttime)){
            $elem['starttime']=date('Y-m-d H:i:s',$starttime);
        }
        $elem['targetlevelname']=CommonDao::getTargetlevelName($elem['targetlevel']);
        return $elem;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $name=$request["name"];
        $starttime=$request["starttime"];
        $taskinterval=$request["taskinterval"];
        $woistartdelay=$request["woistartdelay"];
        $woitimelength=$request["woitimelength"];
        $targetlevel=$request["targetlevel"];
        $targetcity=$request["targetcity"];
        $targetmaintenance=$request["targetmaintenance"];
        $wkotype=$request["wkotype"];

        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($starttime)){
            $this->output_commonerror('开始时间不能为空');
            return;
        }
        if(empty($taskinterval)){
            $this->output_commonerror('间隔时间不能为空');
            return;
        }
        if(empty($woistartdelay)){
//            $this->output_commonerror('生成工单开始时间不能为空');
//            return;
            $woistartdelay=0;
        }
        if(empty($woitimelength)){
            $this->output_commonerror('生成工单时长不能为空');
            return;
        }
        if($targetlevel==2&&empty($targetcity)){
            $this->output_commonerror('目标城市不能为空');
            return;
        }
        if($targetlevel==3&&empty($targetmaintenance)){
            $this->output_commonerror('目标运维单位不能为空');
            return;
        }
        if(empty($wkotype)){
            $this->output_commonerror('wkotype不能为空');
            return;
        }

        $wotask=M('wotask');
        $data['name']=$name;
        $data['create_userid']=session("userid");
        $data['starttime']=strtotime($starttime);
        $data['taskinterval']=$taskinterval*24*60;
        $data['wotype']=$wkotype;
        $data['woistartdelay']=$woistartdelay*24*60;
        $data['woitimelength']=$woitimelength*24*60;
        $data['targetlevel']=$targetlevel;
        $data['targetcity']=$targetcity;
        $data['targetmaintenance']=$targetmaintenance;
        $time=time();
        $data['createtime']=$time;
        $wotask->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $name=$request["name"];
        $starttime=$request["starttime"];
        $taskinterval=$request["taskinterval"];
        $woistartdelay=$request["woistartdelay"];
        $woitimelength=$request["woitimelength"];
        $targetlevel=$request["targetlevel"];
        $targetcity=$request["targetcity"];
        $targetmaintenance=$request["targetmaintenance"];

        if(empty($id)){
            $this->output_commonerror('id不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($starttime)){
            $this->output_commonerror('开始时间不能为空');
            return;
        }
        if(empty($taskinterval)){
            $this->output_commonerror('间隔时间不能为空');
            return;
        }
        if(empty($woistartdelay)){
//            $this->output_commonerror('生成工单开始时间不能为空');
//            return;
            $woistartdelay=0;
        }
        if(empty($woitimelength)){
            $this->output_commonerror('生成工单时长不能为空');
            return;
        }
        if($targetlevel==2&&empty($targetcity)){
            $this->output_commonerror('目标城市不能为空');
            return;
        }
        if($targetlevel==3&&empty($targetmaintenance)){
            $this->output_commonerror('目标运维单位不能为空');
            return;
        }

        $wotask=M('wotask');
        $data['name']=$name;
        $data['starttime']=strtotime($starttime);
        $data['taskinterval']=$taskinterval*24*60;
        $data['woistartdelay']=$woistartdelay*24*60;
        $data['woitimelength']=$woitimelength*24*60;
        $data['targetlevel']=$targetlevel;
        $data['targetcity']=$targetcity;
        $data['targetmaintenance']=$targetmaintenance;
        $wotask->where('id='.$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $wotask=M('wotask');
        foreach ($idlist as $id){
            $wotask->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}