<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class GoodsMgrController extends CommonController {
    public function index(){
        if(session('roleid')==2){
            $this->assign("add_able",1);
        }else{
            $this->assign("add_able",0);
        }

        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else{
            $this->assign("search_operatorid_visible",0);
        }
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $goods=M('goods');
        $result=$goods->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        $name=$request["name"];
        $operatorid  =$request["operatorid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $cond['operatorid']=session('userid');
        }else if(session('roleid')==1){
            if(!empty($operatorid)){
                $cond['operatorid']=$operatorid;
            }
        }
        if(!empty($name)){
            $cond['name']=array('like','%'.$name.'%');
        }

        $goods=M('goods');
        $res['total']=$goods->where($cond)->count("id");
        $result=$goods->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $operatorid=$result['operatorid'];
        if(!empty($operatorid)){
            $user=M('user');
            $operatordata=$user->where("userid=".$operatorid)->find();
            if(!empty($operatordata)){
                $result['operatorname']=$operatordata['name'];
            }
        }
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $name=$request["name"];
        $price=$request["price"];
        $content=$request["content"];
        $mainpic=$request["mainpic"];
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($price)){
            $this->output_commonerror('单价不能为空');
            return;
        }
        if(empty($mainpic)){
            $this->output_commonerror('主图不能为空');
            return;
        }
        $goods=M('goods');
//        $result=$goods->where("name='".$name."'")->select();
//        if(!empty($result)){
//            $this->output_commonerror('名称已存在');
//            return;
//        }

        if(session('roleid')!=2){
            $this->output_commonerror('仅允许运营商用户添加');
            return;
        }
        $operatorid=session('userid');

        $goods=M('goods');
        $data['price']=$price;
        $data['name']=$name;
        $data['content']=$content;
        $data['mainpic']=$mainpic;
        $data['operatorid']=$operatorid;
        $data['createtime']=time();
        $data['updatetime']=time();
        $goods->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $name=$request["name"];
        $price=$request["price"];
        $content=$request["content"];
        $mainpic=$request["mainpic"];
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($price)){
            $this->output_commonerror('单价不能为空');
            return;
        }
        if(empty($mainpic)){
            $this->output_commonerror('主图不能为空');
            return;
        }
        $goods=M('goods');
        $result=$goods->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }
        $goods=M('goods');
//        $result=$goods->where("name='".$name."' and id!=".$id)->select();
//        if(!empty($result)){
//            $this->output_commonerror('名称已存在');
//            return;
//        }

        $goods=M('goods');
        $data['price']=$price;
        $data['name']=$name;
        $data['content']=$content;
        $data['mainpic']=$mainpic;
        $data['updatetime']=time();
        $goods->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $goods=M('goods');
        foreach ($idlist as $id){
            $goods->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}