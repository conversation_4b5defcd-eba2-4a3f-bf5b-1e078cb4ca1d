<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class BannerMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $banneradds=M('banneradds');
        $result=$banneradds->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $banneradds=M('banneradds');
        $res['total']=$banneradds->count("id");
        $result=$banneradds->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $type=$request["type"];
        $url=$request["url"];
        $md5=$request["md5"];
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
        $filename = end(explode('/',$url));

        $config=M('config');
        $localurl=$config->where("configkey='localurl'")->find();

        $banneradds=M('banneradds');
        $data['pic_url']=$url;
        $data['filename']=$filename;
        $data['status']=1;
        $data['ordernum']=1;
        $data['createtime']=time();
        $banneradds->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $type=$request["type"];
        $url=$request["url"];
        if(empty($type)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
        $filename = end(explode('/',$url));

        $banneradds=M('banneradds');
        $data['type']=$type;
        $data['url']=$url;
        $data['filename']=$filename;
        $data['updatetime']=time();
        $banneradds->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $banneradds=M('banneradds');
        foreach ($idlist as $id){
            $banneradds->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}