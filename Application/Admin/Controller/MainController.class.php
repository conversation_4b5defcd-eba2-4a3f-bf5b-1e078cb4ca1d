<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Admin\Common\CommonUtil;
use Admin\Common\MenuTree;
class MainController extends CommonController {
    public function index(){
        $this->checkLogin();
        \Think\Log::record("SESSION检测成功 ","INFO");

        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');

        $menuTrees=$this->getMenuTree($roleid);
        \Think\Log::record("menuTrees ".json_encode($menuTrees),"INFO");
        $this->assign("menuTrees1",json_decode(json_encode($menuTrees),true));

        if($roleid==1){
            $this->assign("displaydashboard",1);
        }else{
//            $this->assign("displaydashboard",0);

            $this->assign("displaydashboard",1);
        }
        $this->display();
    }

    private function getMenuTree($roleid){
        $menuids=CommonUtil::getMenuids($roleid);
        $menuTrees=array();
        if(!empty($menuids)){
            $menu=M('menu');
            $result=$menu->where("parentid=%d",array(-1))->order('menuorder asc')->select();
            if(!empty($result)){
                for($j=0;$j<count($result);$j++){
                    if(in_array($result[$j]['menuid'],$menuids)){
                        $submenuTree=new MenuTree();
                        $submenuTree->menuid=$result[$j]['menuid'];
                        $submenuTree->menutitle=$result[$j]['menutitle'];
                        $submenuTree->url=$result[$j]['url'];
                        $submenuTree->iconhtml=$result[$j]['iconhtml'];
                        $this->makeMenuTree($submenuTree,$menuids);
//                        array_push($menuTrees,$submenuTree);
                        $menuTrees[]=$submenuTree;
                    }
                }
            }
        }
        return $menuTrees;
    }

    private function makeMenuTree($menuTree,$menuids){
        $menu=M('menu');
        $result=$menu->where("parentid=%d",array($menuTree->menuid))->order('menuorder asc')->select();
        if(empty($result)){
            return;
        }
        $menuTree->subnodes=array();
        for($j=0;$j<count($result);$j++){
            if(in_array($result[$j]['menuid'],$menuids)){
                $submenuTree=new MenuTree();
                $submenuTree->menuid=$result[$j]['menuid'];
                $submenuTree->menutitle=$result[$j]['menutitle'];
                $submenuTree->url=$result[$j]['url'];
                $submenuTree->iconhtml=$result[$j]['iconhtml'];
                $this->makeMenuTree($submenuTree,$menuids);
//                array_push($menuTree->subnodes,$submenuTree);
                $menuTree->subnodes[]=$submenuTree;
            }
        }
    }

    public function modpasswd(){
        $userid=session('userid');
        if(empty($userid)){
            return;
        }
        $account=session('account');
        $this->assign("userid",$userid);
        $this->assign("account",$account);
        $this->display();
    }

    public function api_modpasswd(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
//        $userid=$request["userid"];
        $olduserpwd=$request["olduserpwd"];
        $userpwd=$request["userpwd"];
        $userpwd1=$request["userpwd1"];

        $session_roleid=session('roleid');
        if(empty($session_roleid)){
            return;
        }
        $userid=session('userid');
        if(empty($userid)){
            return;
        }

//        if(empty($userid)){
//            $this->output_commonerror('userid不能为空');
//            return;
//        }
        if(empty($olduserpwd)){
            $this->output_commonerror('olduserpwd不能为空');
            return;
        }
        if(empty($userpwd)){
            $this->output_commonerror('userpwd不能为空');
            return;
        }
        if(strlen($userpwd)<6){
            $this->output_commonerror('密码长度过短');
            return;
        }

        $user=M('user');
        $result=$user->where("userid=".$userid)->find();
        if(empty($result)){
            $this->output_commonerror('用户不存在');
            return;
        }
        $dbuserpwd=$result['userpwd'];
        if($dbuserpwd!=$olduserpwd){
            $this->output_commonerror('老密码不正确');
            return;
        }
        $data['userpwd']=$userpwd;
        $data['userpwd1']=$userpwd1;
        $user->where("userid=".$userid)->save($data);

        $this->output_data();
        return;
    }

    public function moduserinfo(){
        $userid=session('userid');
        if(empty($userid)){
            return;
        }
        $user=M('user');
        $userdata=$user->where('userid='.$userid)->find();
        $this->assign("user",$userdata);

        if(session('roleid')==6){
            $propertyid=$userdata['propertyid'];
            $propertydata=$user->where('userid='.$propertyid)->find();
            $this->assign("property",$propertydata);
        }

        $this->display();
    }

    public function api_moduserinfo(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
//        $userid=$request["userid"];
        $name=$request["name"];
        $phonenumber=$request["phonenumber"];

        $session_roleid=session('roleid');
        if(empty($session_roleid)){
            return;
        }
        $userid=session('userid');
        if(empty($userid)){
            return;
        }

        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }

        $user=M('user');
        $result=$user->where("userid=".$userid)->find();
        if(empty($result)){
            $this->output_commonerror('用户不存在');
            return;
        }
        $data['name']=$name;
        $data['phonenumber']=$phonenumber;
        $user->where("userid=".$userid)->save($data);

        $this->output_data();
        return;
    }
}