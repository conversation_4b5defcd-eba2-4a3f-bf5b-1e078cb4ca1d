<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class AdsMgrController extends CommonController {
    public function index(){
        if(session('roleid')==1||session('roleid')==5){
            $this->assign("add_able",1);
        }else{
            $this->assign("add_able",0);
        }
        $this->display();
    }

    public function add(){
        $this->assign("typelist",CommonDao::getAllType());
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $ads=M('ads');
        $result=$ads->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("modelvo",$result);
        $this->assign("typelist",CommonDao::getAllType());
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $type=$request["type"];
        $status=$request["status"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $cond['operatorid']=session('userid');
        }else if(session('roleid')==5){
            $cond['createuserid']=session('userid');
        }

        if(!empty($type)){
            $cond['type']=$type;
        }

        if(isset($status)){
            $cond['status']=$status;
        }

        $ads=M('ads');
        $res['total']=$ads->where($cond)->count("id");
        $result=$ads->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $starttime=$result['starttime'];
        if(!empty($starttime)){
            $result['starttime']=date('Y-m-d',$starttime);
        }
        $endtime=$result['endtime'];
        if(!empty($endtime)){
            $result['endtime']=date('Y-m-d',$endtime);
        }
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $updatetime=$result['updatetime'];
        if(!empty($updatetime)){
            $result['updatetime']=date('Y-m-d H:i:s',$updatetime);
        }
        $result['typename']=CommonDao::getTypeName($result['type']);
        $result['statusname']=CommonDao::getAdsStatusName($result['status']);
        $operatorid=$result['operatorid'];
        if(!empty($operatorid)){
            $user=M('user');
            $operatordata=$user->where("userid=".$operatorid)->find();
            if(!empty($operatordata)){
                $result['operatorname']=$operatordata['name'];
            }
        }
        $createuserid=$result['createuserid'];
        if(!empty($createuserid)){
            $user=M('user');
            $createuserdata=$user->where("userid=".$createuserid)->find();
            if(!empty($createuserdata)){
                $result['createusername']=$createuserdata['name'];
            }
        }
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $type=$request["type"];
        $url=$request["url"];
        $md5=$request["md5"];
        $starttime=$request["starttime"];
        $endtime=$request["endtime"];
        if(empty($type)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
        if(empty($md5)){
            $this->output_commonerror('md5不能为空');
            return;
        }
        if(empty($starttime)){
            $this->output_commonerror('起始时间不能为空');
            return;
        }
        if(empty($endtime)){
            $this->output_commonerror('结束时间不能为空');
            return;
        }
        $filename = end(explode('/',$url));

        $config=M('config');
        $localurl=$config->where("configkey='localurl'")->find();

        $status=0;
        if(session('roleid')==1){
            $status=2;
//            $this->output_commonerror('仅允许管理员用户添加');
//            return;
        }else{
            $status=0;
        }
//        $operatorid=session('userid');

        $ads=M('ads');
        $data['type']=$type;
        $data['url']=$url;
        $data['filename']=$filename;
        $data['starttime']=strtotime($starttime);
        $data['endtime']=strtotime($endtime);
        $data['createtime']=time();
        $data['updatetime']=time();
        $content=$localurl['configvalue'].$url."#".$md5."#0";
        $data['content']=$content;
        $data['status']=$status;
        $data['createuserid']=session('userid');
//        $data['operatorid']=$operatorid;
        $adsid=$ads->add($data);
//        $this->addFiletask($adsid,2,$content);
        $this->output_data("");
        return;
    }

    private function addFiletask($adsid,$filetype,$content){
        $dev=M('dev');
        $devdata=$dev->order("id asc")->select();
        if(!empty($devdata)){
            $filetask=M('filetask');
            foreach ($devdata as $devdata_elem){
                $filetaskdata['devid']=$devdata_elem['id'];
                $filetaskdata['status']=0;
                $filetaskdata['filetype']=$filetype;
                $filetaskdata['failtimes']=0;
                $filetaskdata['content']=$content;
                $filetaskdata['createtime']=time();
                $filetaskdata['adsid']=$adsid;
                $filetask->add($filetaskdata);
            }
        }
    }

    private function delFileTask($adsid){
        $filetask=M('filetask');
        $filetask->where("adsid=".$adsid)->delete();
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $type=$request["type"];
        $url=$request["url"];
//        $starttime=$request["starttime"];
//        $endtime=$request["endtime"];
        if(empty($type)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
//        if(empty($starttime)){
//            $this->output_commonerror('起始时间不能为空');
//            return;
//        }
//        if(empty($endtime)){
//            $this->output_commonerror('结束时间不能为空');
//            return;
//        }
        $filename = end(explode('/',$url));

        $ads=M('ads');
        $data['type']=$type;
        $data['url']=$url;
        $data['filename']=$filename;
//        $data['starttime']=strtotime($starttime);
//        $data['endtime']=strtotime($endtime);
        $data['updatetime']=time();
        $ads->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $ads=M('ads');
        $play=M('play');
        $playadsfile=M('playadsfile');
        foreach ($idlist as $id){
            $adsdata=$ads->where('id='.$id)->find();
            if(empty($adsdata)){
                continue;
            }
            $playadsfiledata=$playadsfile->where('adsfileid='.$id)->select();
            if(!empty($playadsfiledata)){
                $playid=$playadsfiledata[0]['playid'];
//                $playdata=$play->where('id='.$playid)->find();
//                if(!empty($playdata)){
//
//                }
                $this->output_commonerror("广告文件[".$adsdata['filename']."]正在使用，请先删除对应播放实例ID：".$playid);
                return;
            }
        }

        foreach ($idlist as $id){
            $ads->where('id='.$id)->delete();
            $this->delFileTask($id);
        }
        $this->output_data("");
        return;
    }

    function modstatus(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $status=$request["status"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }

        $ads=M('ads');
        $data['status']=$status;
        $data['updatetime']=time();
        $ads->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }
}