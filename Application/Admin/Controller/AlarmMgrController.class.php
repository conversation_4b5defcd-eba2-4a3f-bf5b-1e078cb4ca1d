<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class AlarmMgrController extends CommonController {
    public function index(){
        $devcode=I('devcode');
        if(empty($devcode)){
            return;
        }
        $this->assign("devcode",$devcode);
        $this->display();
    }

    public function detail(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $alarm=M('alarm');
        $result=$alarm->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("alarm",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $devcode=$request["devcode"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if(empty($devcode)){
            $this->output_data("");
            return;
        }
        $alarm=M('alarm');
        $cond["devcode"]=$devcode;
        $res['total']=$alarm->where($cond)->count("id");
//        $cond['status']=1;
        $result=$alarm->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $dev=M('dev');
        $result2=$dev->where("code='".$result['devcode']."'")->find();
        if(!empty($result2)){
            $result['devcode']=$result2['code'];
            $result['devname']=$result2['name'];
        }
        $faulttime=$result['faulttime'];
        if(!empty($faulttime)){
            $result['faulttime']=date('Y-m-d H:i:s',$faulttime);
        }
        $cleartime=$result['cleartime'];
        if(!empty($cleartime)){
            $result['cleartime']=date('Y-m-d H:i:s',$cleartime);
        }
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $result['typename']=CommonDao::getAlarmTypeName($result['type']);
        $result['statusname']=CommonDao::getAlarmStatusName($result['status']);
        $result['wastetypename']=CommonDao::getRubbishtypeName($result['wastetype']);
        return $result;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $alarm=M('alarm');
        foreach ($idlist as $id){
            $alarm->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }

    public function api_confirmalarm(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_data();
            return;
        }
        $alarm=M('alarm');
        $data['confirmFlag']=1;
        $data['confirmtime']=date('Y-m-d h:i:s',time());
        $alarm->where('id='.$id)->save($data);
        $this->output_data();
        return;
    }
}