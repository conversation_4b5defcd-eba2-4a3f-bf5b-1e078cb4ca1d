<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class AsstMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(19));
        $this->display();
    }

    public function add(){
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->display();
    }

    public function update(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $solve=M('solve');
        $result=$solve->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("wkotypename",CommonDao::getWotypeName($result['wotype']));
        $this->assign("statusList",CommonDao::getAllSolveStatus());
        $this->assign("levelList",CommonDao::getAllSolveLevel());
        $this->assign("userList",CommonDao::getAllUser());
        $this->assign("solve",$result);
        $this->assign("roleid",session('roleid'));
        $this->display();
    }

    public function addservlog(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $solve=M('solve');
        $result=$solve->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("wkotypename",CommonDao::getWotypeName($result['wotype']));
        $this->assign("userList",CommonDao::getAllUser());
        $this->assign("solve",$result);

        $parts=M('parts');
        $partsdata=$parts->order("id desc")->select();
        $this->assign("partsList",$partsdata);
        $this->display();
    }

    public function modservlog(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $solve=M('solve');
        $result=$solve->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("wkotypename",CommonDao::getWotypeName($result['wotype']));
        $this->assign("userList",CommonDao::getAllUser());
        $this->assign("replaceflags",CommonDao::getAllReplaceflag());
        $this->assign("solve",$result);

        $parts=M('parts');
        $partsdata=$parts->order("id desc")->select();
        $this->assign("partsList",$partsdata);

        $servlog=M('servlog');
        $servlogdata=$servlog->where('solveid='.$woid)->find();

        $starttime=$servlogdata['starttime'];
        if(!empty($starttime)){
            $servlogdata['starttime']=date('Y-m-d H:i:s',$starttime);
        }
        $endtime=$servlogdata['endtime'];
        if(!empty($endtime)){
            $servlogdata['endtime']=date('Y-m-d H:i:s',$endtime);
        }
        $this->assign("servlog",$servlogdata);

        $servlogparts=M('servlogparts');
        $servlogpartsdata=$servlogparts->where('servlogid='.$servlogdata['id'])->select();
        $this->assign("servlogparts",$servlogpartsdata);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $solvename=$request["solvename"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $userid=session("userid");
        if(empty($userid)){
            return;
        }
        $solve=M('solve');

        if(session('roleid')==5&&session('orgid')==1){
            // 省公司
            $res['total']=$solve->where("status=2 and servlogstatus=0")->count("woid");
            $result=$solve->where("status=2 and servlogstatus=0")->order('woid desc')->limit($offset,$limit)->select();
        }if(session('roleid')==5&&session('orgid')!=1){
            // 运维单位
            $station=M('station');
            $stationdata=$station->where("orgid=".session('orgid'))->select();
            if(!empty($stationdata)){
                $stationids=array();
                foreach ($stationdata as $stationdata_elem){
                    $stationids[]=$stationdata_elem['stationid'];
                }
                if(!empty($stationids)){
                    $cond['status']=2;
                    $cond['servlogstatus']=2;
                    $cond['stationid']=array('IN',$stationids);
                    $res['total']=$solve->where($cond)->count("woid");
                    $result=$solve->where($cond)->order('woid desc')->limit($offset,$limit)->select();
                }
            }
        }else if(session('roleid')==3){
            // 厂商
            $res['total']=$solve->where("status=2 and solvemanufacturerid=".session('userid'))->count("woid");
            $result=$solve->where("status=2 and solvemanufacturerid=".session('userid'))->order('woid desc')->limit($offset,$limit)->select();
        }else if(session('roleid')==1){
            // 厂商
            $res['total']=$solve->where("status=2")->count("woid");
            $result=$solve->where("status=2")->order('woid desc')->limit($offset,$limit)->select();
        }

        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        $station=M('station');
        $user=M('user');
        $stationid=$elem['stationid'];
        if(!empty($stationid)){
            $stationdata=$station->where("stationid=".$stationid)->find();
            $elem['stationname']=$stationdata['name'];
        }
        $solvemanufacturerid=$elem['solvemanufacturerid'];
        if(!empty($solvemanufacturerid)){
            $userdata=$user->where("userid=".$solvemanufacturerid)->find();
            $elem['solveuseridname']=$userdata['name'];
        }
        $elem['statusname']=CommonDao::getSolveStatusName($elem['status']);
        $elem['servlogstatusname']=CommonDao::getServlogStatusName($elem['servlogstatus']);
        $elem['solvetypename']=CommonDao::getSolveTypeName($elem['solvetype']);
        $elem['levelname']=CommonDao::getSolveLevelName($elem['level']);

        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $elem;
    }

//    public function api_add(){
//        $request = file_get_contents('php://input');
//        $request = json_decode($request,true);
//        stationid=$request["stationid"];
//        $nickname=$request["nickname"];
//        $solvepwd=$request["solvepwd"];
//        $roleid=$request["roleid"];
//        $wkotype=$request["wkotype"];
//        if(empty($account)){
//            $this->output_commonerror('账号不能为空');
//            return;
//        }
//        if(empty($solvepwd)){
//            $this->output_commonerror('密码不能为空');
//            return;
//        }
//        if(empty($roleid)){
//            $this->output_commonerror('角色不能为空');
//            return;
//        }
//        if(empty($wkotype)){
//            $this->output_commonerror('wkotype不能为空');
//            return;
//        }
//        $solve=M('solve');
//        $result=$solve->where("account='".$account."'")->select();
//        if(!empty($result)){
//            $this->output_commonerror('账号已存在');
//            return;
//        }
//        $nickname=empty($nickname)?$account:$nickname;
//        $sex=1;
//
//        $solve=M('solve');
//        $data['account']=$account;
//        $data['nickname']=$nickname;
//        $data['solvepwd']=$solvepwd;
//        $data['roleid']=$roleid;
//        $data['status']=1;
//        $data['sex']=$sex;
//        $time=date('Y-m-d h:i:s',time());
//        $data['createtime']=$time;
//        $data['updatetime']=$time;
//        $solve->add($data);
//        $this->output_data();
//        return;
//    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woid=$request["woid"];
        $status=$request["status"];
        $level=$request["level"];
        $reason=$request["reason"];
        $solveuserid=$request["solveuserid"];
        if(empty($woid)){
            $this->output_commonerror('ID不能为空');
            return;
        }

        $solve=M('solve');
        $result=$solve->where("woid=".$woid)->select();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }
        $user=M('user');
        $userresult=$user->where("userid=".$solveuserid)->select();
        if(empty($userresult)){
            $this->output_commonerror('用户不存在');
            return;
        }

        $solve=M('solve');
        $data['status']=$status;
        $data['level']=$level;
        $data['reason']=$reason;
        $data['solveuserid']=$solveuserid;
        $solve->where('woid='.$woid)->save($data);
        $this->output_data();
        return;
    }

    public function api_addservlog(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woid=$request["woid"];
        $starttime=$request["starttime"];
        $endtime=$request["endtime"];
        $manday=$request["manday"];
        $remark=$request["remark"];
        $replaceflag=$request["replaceflag"];
        $parts_req=$request["parts"];
        $userid=$request["userid"];
        if(empty($woid)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(!empty($starttime)){
            $starttime=strtotime($starttime);
        }
        if(!empty($endtime)){
            $endtime=strtotime($endtime);
        }

        if(empty($userid)){
            $userid=session('userid');
        }

        if($replaceflag==1){
            if(empty($parts_req)){
                $this->output_commonerror('配件不能为空');
                return;
            }
            foreach ($parts_req as $parts_elem){
                if(empty($parts_elem['partsid'])||empty($parts_elem['partscount'])){
                    $this->output_commonerror('配件信息错误,存在部分配件记录为空');
                    return;
                }
            }
        }
        $solve=M('solve');
        $solvedata=$solve->where("woid=".$woid)->find();
        if(empty($solvedata)){
            $this->output_commonerror('消缺工单不存在');
            return;
        }
        $config=M('config');
        $labordayprice=$config->where("configkey='labordayprice'")->find();

        $servlog=M('servlog');
        $servlog->where("solveid=".$woid)->delete();
        $servlogdata['solveid']=$woid;
        $servlogdata['woid']=$solvedata['rel_woid'];
        $servlogdata['solvemanufacturerid']=$userid;
        $servlogdata['status']=0;
        $servlogdata['manday']=$manday;
        $servlogdata['mancost']=$manday*$labordayprice['configvalue'];
        $servlogdata['starttime']=$starttime;
        $servlogdata['endtime']=$endtime;
        $servlogdata['remark']=$remark;
        $servlogdata['replaceflag']=$replaceflag;
        $servlogdata['createtime']=time();
        $servlog->add($servlogdata);
        $servlogdata=$servlog->where("solveid=".$woid)->find();
        $servlogid=$servlogdata['id'];

        if($replaceflag==1){
            $servlogparts=M('servlogparts');
            $parts=M('parts');
            $servlogparts->where("servlogid=".$servlogid)->delete();
            foreach ($parts_req as $parts_elem){
                $partsdata=$parts->where("id=".$parts_elem['partsid'])->find();
                if(empty($partsdata)){
                    continue;
                }
                $servlogpartsdata['servlogid']=$servlogid;
                $servlogpartsdata['partsid']=$parts_elem['partsid'];
                $servlogpartsdata['name']=$partsdata['name'];
                $servlogpartsdata['model']=$partsdata['model'];
                $servlogpartsdata['unit']=$partsdata['unit'];
                $servlogpartsdata['unitprice']=$partsdata['unitprice_discount'];
                $servlogpartsdata['partscount']=$parts_elem['partscount'];
                $servlogpartsdata['totalprice']=$partsdata['unitprice_discount']*$parts_elem['partscount'];
                $servlogparts->add($servlogpartsdata);
            }
        }

        $solvedata1['servlogstatus']=0;
        $solve->where("woid=".$woid)->save($solvedata1);
        $this->output_data("");
        return;
    }

    public function api_modservlog(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $starttime=$request["starttime"];
        $endtime=$request["endtime"];
        $manday=$request["manday"];
        $remark=$request["remark"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(!empty($starttime)){
            $starttime=strtotime($starttime);
        }
        if(!empty($endtime)){
            $endtime=strtotime($endtime);
        }

        $config=M('config');
        $labordayprice=$config->where("configkey='labordayprice'")->find();

        $servlog=M('servlog');
        $servlogdata['manday']=$manday;
        $servlogdata['mancost']=$manday*$labordayprice['configvalue'];
        $servlogdata['starttime']=$starttime;
        $servlogdata['endtime']=$endtime;
        $servlogdata['remark']=$remark;
        $servlog->where("id=".$id)->save($servlogdata);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woidlist=$request["woidlist"];
        if(empty($woidlist)){
            $this->output_data();
            return;
        }
        $solve=M('solve');
        foreach ($woidlist as $woid){
            $solve->where('woid='.$woid)->delete();
        }
        $this->output_data();
        return;
    }

    public function api_getParts(){
        $id=I('id');
        if($id==-1){
            $this->output_data();
            return;
        }
        $parts=M('parts');
        $res=$parts->where("id=".$id)->find();
        $this->output_data($res);
        return;
    }

    public function sgs_servlog(){
        $woid=I('woid');
        $type=I('type');
        $solve=M('solve');
        $servlog=M('servlog');
        if($type=='N'){
            $solvedata['status']=3;
            $solvedata['servlogstatus']=3;
            $solve->where("woid=".$woid)->save($solvedata);
            $servlogdata['status']=3;
            $servlog->where("solveid=".$woid)->save($servlogdata);
        }else if($type=='Y'){
            $solvedata['servlogstatus']=1;
            $solve->where("woid=".$woid)->save($solvedata);
            $servlogdata['status']=1;
            $servlog->where("solveid=".$woid)->save($servlogdata);
        }

        $this->output_data('');
        return;
    }

    public function weixiuwancheng(){
        $woid=I('woid');
        $type=I('type');
        $solve=M('solve');
        $servlog=M('servlog');
        if($type=='N'){

        }else if($type=='Y'){
            $solvedata['servlogstatus']=2;
            $solve->where("woid=".$woid)->save($solvedata);
            $servlogdata['status']=2;
            $servlog->where("solveid=".$woid)->save($servlogdata);
        }

        $this->output_data('');
        return;
    }

    public function yunweidanweiqueren(){
        $woid=I('woid');
        $type=I('type');
        $solve=M('solve');
        $servlog=M('servlog');
        if($type=='N'){

        }else if($type=='Y'){
            $solvedata['status']=3;
            $solvedata['servlogstatus']=3;
            $solve->where("woid=".$woid)->save($solvedata);
            $servlogdata['status']=3;
            $servlog->where("solveid=".$woid)->save($servlogdata);
        }

        $this->output_data('');
        return;
    }
}