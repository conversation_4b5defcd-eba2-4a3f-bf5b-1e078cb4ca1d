<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Admin\Common\CommonUtil;

class SettingMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function setting(){
        $userid=session('userid');
        if(empty($userid)){
            return;
        }
        $user=M('user');
        $userdata=$user->where('userid='.$userid)->find();
        if(empty($userdata)){
            return;
        }
        $this->assign("user",$userdata);
        $this->display();
    }

    public function api_setting(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $autodooropenclose=$request["autodooropenclose"];

        $userid=session('userid');
        if(empty($userid)){
            return;
        }

        $user=M('user');
        $data['autodooropenclose']=$autodooropenclose;
        $user->where("userid=".$userid)->save($data);

        CommonUtil::setParamNotify($userid);

        $this->output_data('');
        return;
    }
}