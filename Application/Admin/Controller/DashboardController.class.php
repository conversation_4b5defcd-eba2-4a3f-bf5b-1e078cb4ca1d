<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Admin\Common\CommonUtil;
use Admin\Common\MenuTree;
use Common\Common\CommonDao;

class DashboardController extends CommonController {
    public function dashboard(){
        $this->checkLogin();
        \Think\Log::record("SESSION检测成功 ","INFO");

        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');

        //展示统计数据
        $map['uid'] = $userid;
        $map['date'] = date("Y-m-d");
        $info = M("datastatistics")->where($map)->find();
        if(empty($info)){
            $url='https://recycle.jifu.club/jifurecycle/index.php/Api/App/test';
            $api = file_get_contents($url);

            $info = M("datastatistics")->where($map)->find();
        }
        $this->assign("info",$info);
        //七天投放量
        if(empty($info['zongliang7tian'])){
            $zongliang7tian['monthlist'] = json_encode(array());
            $zongliang7tian['valuelist'] = json_encode(array());
        }else{
            $zongliang7tianarr = json_decode($info['zongliang7tian'],true);
            $zongliang7tian['monthlist'] = json_encode($zongliang7tianarr['monthlist']);
            $zongliang7tian['valuelist'] = json_encode($zongliang7tianarr['valuelist']);
        }

        $this->assign("zongliang7tian",$zongliang7tian);
        
        if(empty($info['toufangcishu7tian'])){
            $toufangcishu7tian['monthlist'] = json_encode(array());
            $toufangcishu7tian['valuelist'] = json_encode(array());
        }else{
            $toufangcishu7tianarr = json_decode($info['toufangcishu7tian'],true);
            $toufangcishu7tian['monthlist'] = json_encode($toufangcishu7tianarr['monthlist']);
            $toufangcishu7tian['valuelist'] = json_encode($toufangcishu7tianarr['valuelist']);
        }
        $this->assign("toufangcishu7tian",$toufangcishu7tian);
        //已满时间分布
        if(empty($info['ymsjfb'])){
            $ymsjfb['monthlist'] = json_encode(array());
            $ymsjfb['valuelist'] = json_encode(array());
        }else{
            $ymsjfbtianarr = json_decode($info['ymsjfb'],true);
            $ymsjfb['monthlist'] = json_encode($ymsjfbtianarr['monthlist']);
            $ymsjfb['valuelist'] = json_encode($ymsjfbtianarr['valuelist']);
        }
        $this->assign("ymsjfb",$ymsjfb);

        $this->display("newpage1");
    }
    //新页面
    public function newpage(){
        $this->checkLogin();
        \Think\Log::record("SESSION检测成功 ","INFO");

        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');
        $this->display();
    }

    public function dashboardlogin(){
        $account=$_REQUEST['account'];
        $pwd=$_REQUEST['pwd'];
        $backurl=$_REQUEST['backurl'];
        if(empty($account)||empty($pwd)){
            $this->output_data("用户名或密码为空");
            return;
        }

        $user=M('user');
        $userinfo=$user->where("account='".$account."'")->find();
        if(empty($userinfo)){
            $this->output_data("账号不存在");
            return;
        }

        if($pwd!=$userinfo['userpwd']){
            $this->output_data("用户名或密码错误");
            return;
        }

        $role=M('role');
        $roledata=$role->where("roleid=".$userinfo['roleid'])->find();
        if(empty($roledata)){
            $this->output_data("角色不存在");
            return;
        }
        if($roledata['loginentrance']==1){
            $this->output_data("用户不存在");
            return;
        }

        session('userid',$userinfo['userid']);
        session('account',$account);
        session('roleid',$userinfo['roleid']);
        session('orgid',$userinfo['orgid']);
        session('operatoruserid',$userinfo['operatoruserid']);

        $role=M('role');
        $result=$role->where("roleid='%d'",array($userinfo['roleid']))->find();
        if(empty($result)){
            $this->output_data("用户角色不存在");
            return;
        }
        session('rolename',$result['rolename']);

        \Think\Log::record("后台登录成功1 ".$account,"INFO");

        $this->checkLogin();
        \Think\Log::record("SESSION检测成功 ","INFO");

        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');
        if(!empty($backurl)){
            $this->assign("backurl",$backurl);
            $this->assign("backurlflag",1);
        }else{
            $this->assign("backurlflag",0);
        }
        $this->display("dashboard");
    }

    public function getDashboardTime(){
        $this->output_data(date('Y-m-d H:i:s',time()));
        return;
    }

    public function api_getDevlist(){
        $cond=array();
        $roleid=session('roleid');
        if(empty($roleid)){
            $this->output_data('');
            return;
        }

        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }
        $dev=M('dev');
        $result=$dev->where($cond)->where("lat is not null and lng is not null and lat!=0 and lng!=0")->field("id,name,lat,lng,heartbeattime,devtype")->select();
        $time=time()-C('onlineTimeDelta');
        if(!empty($result)){
            foreach ($result as &$result_elem){
                if(!empty($result_elem['heartbeattime'])&&$result_elem['heartbeattime']>$time){
                    $result_elem['online']=1;
                }else{
                    $result_elem['online']=0;
                }
            }
        }
        $this->output_data($result);
        return;
    }

    public function getDevInfo(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devid    =$request["id"];
        $cond=array();
        $cond['id']=$devid;
        $dev=M('dev');
        $result=$dev->where($cond)->find();
        if(!empty($result)){
            $time=time()-C('onlineTimeDelta');
            if(!empty($result['heartbeattime'])&&$result['heartbeattime']>$time){
                $result['onlinename']="在线";
            }else{
                $result['onlinename']="离线";
            }
        }
        $this->output_data($result);
        return;
    }

    public function getCanyurenshu(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-180*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>'.$starttime.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['renshu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['renshu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }else{
            $monthstatic=$throwlog->query('select count(distinct(userid)) as renshu,throwmonth from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['renshu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }
        $this->output_data('');
        return;
    }

    public function getToufangcishu(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-180*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(1) as cishu,throwmonth from think_throwlog where createtime>'.$starttime.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['cishu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(1) as cishu,throwmonth from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['cishu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }else{
            $monthstatic=$throwlog->query('select count(1) as cishu,throwmonth from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwmonth');
            if(!empty($monthstatic)){
                $monthlist=array();
                $valuelist=array();
                foreach ($monthstatic as $monthstatic_ele){
                    $monthlist[]=$monthstatic_ele['throwmonth'];
                    $valuelist[]=$monthstatic_ele['cishu']*$delta;
                }
                $res['monthlist']=$monthlist;
                $res['valuelist']=$valuelist;
                $this->output_data($res);
                return;
            }
        }
        $this->output_data('');
        return;
    }

    public function getFenleitongji(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-180*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>'.$starttime.' group by rubbishtype order by weight desc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by rubbishtype order by weight desc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,rubbishtype from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by rubbishtype order by weight desc');
        }
        if(!empty($monthstatic)){
            $valuelist=array();
            $rubbishtypelist=array();
            $num=0;
            foreach ($monthstatic as $monthstatic_ele){
                $num++;
                if($num>14){
                    break;
                }
                $element['value']=$monthstatic_ele['weight']*$delta;
                $element['name']=CommonDao::getRubbishtypeName($monthstatic_ele['rubbishtype']);
                $valuelist[]=$element;
                $rubbishtypelist[]=$monthstatic_ele['rubbishtype'];
            }
            $res['rubbishtypenamelist']=$this->getRubbishtypeNameList($rubbishtypelist);
            $res['valuelist']=$valuelist;
            $this->output_data($res);
            return;
        }
        $this->output_data('');
        return;
    }

    private function getRubbishtypeNameList($rubbishtypelist){
        $rubbishtypeNameList=array();
        if(empty($rubbishtypelist)){
            return $rubbishtypeNameList;
        }
        $delta=$this->getDelta();
        $rubbishtypepoint=M('rubbishtypepoint');
        $rubbishtypepointlist=$rubbishtypepoint->select();
        if(empty($rubbishtypepointlist)){
            return $rubbishtypeNameList;
        }
        foreach ($rubbishtypelist as $rubbishtypelist_elem){
            $name=null;
            foreach ($rubbishtypepointlist as $rubbishtypepointlist_elem){
                if($rubbishtypepointlist_elem['rubbishtype']==$rubbishtypelist_elem){
                    $name=$rubbishtypepointlist_elem['name'];
                    break;
                }
            }
            if(!empty($name)){
                $rubbishtypeNameList[]=$name;
            }
        }
        return $rubbishtypeNameList;
    }

    public function getZongliangbianhua(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-30*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=round($monthstatic_ele['weight']*$delta/1000,0);
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $this->output_data($res);
            return;
        }
        $this->output_data('');
        return;
    }

    public function getToudishijian(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        // 今天时间戳
        $today = strtotime(date("Y-m-d"),time());

        // 昨天时间戳
        $yesterday = $today-(3600*24);
        $starttime = $yesterday;
        $throwlog=M('throwlog');
        $throwloglist=$throwlog->where("throwhour is null")->select();
        if(!empty($throwloglist)){
            foreach ($throwloglist as $throwloglist_elem){
                $newdata['throwhour']=date("H",$throwloglist_elem['createtime']);
                $throwlog->where("id=".$throwloglist_elem['id'])->save($newdata);
            }
        }
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(1) as times,throwhour from think_throwlog where createtime>'.$starttime.' group by throwhour order by throwhour asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(1) as times,throwhour from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwhour order by throwhour asc');
        }else{
            $monthstatic=$throwlog->query('select count(1) as times,throwhour from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwhour order by throwhour asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwhour'];
                $valuelist[]=round($monthstatic_ele['times']*$delta,0);
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $this->output_data($res);
            return;
        }
        $this->output_data('');
        return;
    }

    public function getZongliang7tian(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-7*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=round($monthstatic_ele['weight']*$delta/1000,0);
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $this->output_data($res);
            return;
        }
        $this->output_data('');
        return;
    }

    public function getToufangcishu7tian(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-7*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' group by throwdate order by throwdate asc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by throwdate order by throwdate asc');
        }else{
            $monthstatic=$throwlog->query('select count(1) as times,throwdate from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by throwdate order by throwdate asc');
        }
        if(!empty($monthstatic)){
            $monthlist=array();
            $valuelist=array();
            foreach ($monthstatic as $monthstatic_ele){
                $monthlist[]=$monthstatic_ele['throwdate'];
                $valuelist[]=$monthstatic_ele['times']*$delta;
            }
            $res['monthlist']=$monthlist;
            $res['valuelist']=$valuelist;
            $this->output_data($res);
            return;
        }
        $this->output_data('');
        return;
    }

    public function getShequlajipaiming(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-180*24*3600;
        $throwlog=M('throwlog');
        if($roleid==1){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,propertyid from think_throwlog where createtime>'.$starttime.' group by propertyid order by weight desc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,propertyid from think_throwlog where createtime>'.$starttime.' and operatorid='.$userid.' group by propertyid order by weight desc');
        }else{
            $monthstatic=$throwlog->query('select sum(throwweight) as weight,propertyid from think_throwlog where createtime>'.$starttime.' and propertyid='.$userid.' group by propertyid order by weight desc');
        }
        $user=M('user');
        if(!empty($monthstatic)){
            $paiming=array();
            $num=0;
            foreach ($monthstatic as $monthstatic_ele){
                if(empty($monthstatic_ele['propertyid'])){
                    continue;
                }
                $userdata=$user->where('userid='.$monthstatic_ele['propertyid'])->find();
                if(!empty($userdata)){
                    $temp['propertyname']=$userdata['name'];
                    $temp['weight']=round($monthstatic_ele['weight']*$delta/1000,0);
                    $temp['index']=$num+1;
                    if($num>500){
                        break;
                    }
                    $paiming[]=$temp;
                    $num++;
                }
            }
            $this->output_data($paiming);
            return;
        }
        $this->output_data('');
        return;
    }

    public function getShequweifatongji(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $delta=$this->getDelta();
        $starttime=time()-180*24*3600;
        $throwlog=M('violation');
        if($roleid==1){
            $monthstatic=$throwlog->query('select count(1) as num,propertyid from think_violation where createtime>'.$starttime.' group by propertyid order by num desc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select count(1) as num,propertyid from think_violation where createtime>'.$starttime.' and operatorid='.$userid.' group by propertyid order by num desc');
        }else{
            $monthstatic=$throwlog->query('select count(1) as num,propertyid from think_violation where createtime>'.$starttime.' and propertyid='.$userid.' group by propertyid order by num desc');
        }
        $user=M('user');
        if(!empty($monthstatic)){
            $paiming=array();
            $num=0;
            foreach ($monthstatic as $monthstatic_ele){
                if(empty($monthstatic_ele['propertyid'])){
                    continue;
                }
                $userdata=$user->where('userid='.$monthstatic_ele['propertyid'])->find();
                if(!empty($userdata)){
                    $temp['propertyname']=$userdata['name'];
                    $temp['num']=$monthstatic_ele['num']*$delta;
                    $temp['index']=$num+1;
                    if($num>500){
                        break;
                    }
                    $paiming[]=$temp;
                    $num++;
                }
            }
            $this->output_data($paiming);
            return;
        }
        $this->output_data('');
        return;
    }

    function getDashboardData(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $monthstart=strtotime(date("Y-m-01"),time());

        $delta=$this->getDelta();
        $time=time()-C('onlineTimeDelta');
        $throwlog=M('dev');
        if($roleid==1){
            $totaldev=$throwlog->query('select count(1) as num from think_dev');
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where isfull=1');
        }else if($roleid==2){
            $totaldev=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid);
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid.' and heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where operatorid='.$userid.' and isfull=1');
        }else{
            $totaldev=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid);
            $onlinedev=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid.' and heartbeattime>'.$time);

            $isfull=$throwlog->query('select count(1) as num from think_dev where propertyid='.$userid.' and isfull=1');
        }
//        \Think\Log::record("isfull=".$isfull,"INFO");
        $total=0;
        $online=0;
        if(!empty($totaldev)){
            $total=$totaldev[0]['num'];
            if(empty($total)){
                $total=0;
            }
            $online=$onlinedev[0]['num'];
            if(empty($online)){
                $online=0;
            }
        }

        $res['totaldev']=$total*$delta;
        $res['onlinedev']=$online*$delta;
        $res['offlinedev']=($total-$online)*$delta;
        $res['alarmdev']=0;

        $isfullnum=0;
        if(!empty($isfull)){
            $isfullnum=$isfull[0]["num"];
        }
        $res['fulldev']=$isfullnum*$delta;
        $res['unfulldev']=$res['totaldev']-$res['fulldev'];

        $register=M('register');
        $card=M('card');
        $totalwpuser=$register->count();
        $totalcarduser=$card->count();
        $totalwpuser_monthadd=$register->where("createtime>=".$monthstart)->count();
        $totalcarduser_monthadd=$card->where("createtime>=".$monthstart)->count();
        $res['totalwpuser']=$totalwpuser*$delta;
        $res['totalcarduser']=$totalcarduser*$delta;

        $user=M('user');
        $ttotalresidential=$user->where("roleid=3")->count();
        $ttotalresidential_monthadd=$user->where("roleid=3 and createtime>=".$monthstart)->count();

        $res['totalfamily']=$totalcarduser*$delta;
        $res['totalresidential']=$ttotalresidential*$delta;
        $res['totaluser']=($totalcarduser+$totalwpuser)*$delta;

        $res['totalfamily_monthadd']=$totalcarduser_monthadd*$delta;
        $res['totalresidential_monthadd']=$ttotalresidential_monthadd*$delta;
        $res['totaluser_monthadd']=($totalcarduser_monthadd+$totalwpuser_monthadd)*$delta;

        // 今天时间戳
        $today = strtotime(date("Y-m-d"),time());
        $today_str=date("Y-m-d H:i:s",$today);

        // 昨天时间戳
        $yesterday = $today-(3600*24);
        $yesterday_str=date("Y-m-d H:i:s",$yesterday);

        $throwlog=M('throwlog');
        $zhilei_zongliang=$throwlog->where("rubbishtype='6'")->sum("throwweight");
        $zhilei_zuori=$throwlog->where("rubbishtype='6' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $zhilei_jinri=$throwlog->where("rubbishtype='6' and createtime>".$today)->sum("throwweight");

        $suliao_zongliang=$throwlog->where("rubbishtype='A'")->sum("throwweight");
        $suliao_zuori=$throwlog->where("rubbishtype='A' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $suliao_jinri=$throwlog->where("rubbishtype='A' and createtime>".$today)->sum("throwweight");

        $jinshu_zongliang=$throwlog->where("rubbishtype='8'")->sum("throwweight");
        $jinshu_zuori=$throwlog->where("rubbishtype='8' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $jinshu_jinri=$throwlog->where("rubbishtype='8' and createtime>".$today)->sum("throwweight");

        $fangzhi_zongliang=$throwlog->where("rubbishtype='7'")->sum("throwweight");
        $fangzhi_zuori=$throwlog->where("rubbishtype='7' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $fangzhi_jinri=$throwlog->where("rubbishtype='7' and createtime>".$today)->sum("throwweight");

        $youdu_zongliang=$throwlog->where("rubbishtype='5'")->sum("throwweight");
        $youdu_zuori=$throwlog->where("rubbishtype='5' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $youdu_jinri=$throwlog->where("rubbishtype='5' and createtime>".$today)->sum("throwweight");

        $qita_zongliang=$throwlog->where("rubbishtype='1'")->sum("throwweight");
        $qita_zuori=$throwlog->where("rubbishtype='1' and createtime>".$yesterday." and createtime<=".$today)->sum("throwweight");
        $qita_jinri=$throwlog->where("rubbishtype='1' and createtime>".$today)->sum("throwweight");

        $pingguan_zongliang=$throwlog->where("rubbishtype='C'")->count();
        $pingguan_zuori=$throwlog->where("rubbishtype='C' and createtime>".$yesterday." and createtime<=".$today)->count();
        $pingguan_jinri=$throwlog->where("rubbishtype='C' and createtime>".$today)->count();

        $res['zhilei_zongliang']=round($zhilei_zongliang*$delta/1000,2);
        $res['zhilei_zuori']=round($zhilei_zuori*$delta/1000,2);
        $res['zhilei_jinri']=round($zhilei_jinri*$delta/1000,2);

        $res['suliao_zongliang']=round($suliao_zongliang*$delta/1000,2);
        $res['suliao_zuori']=round($suliao_zuori*$delta/1000,2);
        $res['suliao_jinri']=round($suliao_jinri*$delta/1000,2);

        $res['jinshu_zongliang']=round($jinshu_zongliang*$delta/1000,2);
        $res['jinshu_zuori']=round($jinshu_zuori*$delta/1000,2);
        $res['jinshu_jinri']=round($jinshu_jinri*$delta/1000,2);

        $res['fangzhi_zongliang']=round($fangzhi_zongliang*$delta/1000,2);
        $res['fangzhi_zuori']=round($fangzhi_zuori*$delta/1000,2);
        $res['fangzhi_jinri']=round($fangzhi_jinri*$delta/1000,2);

        $res['youdu_zongliang']=round($youdu_zongliang*$delta/1000,2);
        $res['youdu_zuori']=round($youdu_zuori*$delta/1000,2);
        $res['youdu_jinri']=round($youdu_jinri*$delta/1000,2);

        $res['qita_zongliang']=round($qita_zongliang*$delta/1000,2);
        $res['qita_zuori']=round($qita_zuori*$delta/1000,2);
        $res['qita_jinri']=round($qita_jinri*$delta/1000,2);

        $res['pingguan_zongliang']=$pingguan_zongliang;
        $res['pingguan_zuori']=$pingguan_zuori;
        $res['pingguan_jinri']=$pingguan_jinri;

        $this->output_data($res);
        return;
    }

    public function getThrowlog(){
        $throwlog=M('throwlog');
        $throwloglist=$throwlog->where("throwweight>0")->order("id desc")->field("id,devname,userid,carduserid,account,throwweight,rubbishtype,throwtime")->limit(1000)->select();
        $reslist=array();
        $card=M('card');
        $rubbishtypepoint=M('rubbishtypepoint');
        foreach ($throwloglist as &$throwloglist_elem){
            $elem=array();
            if(!empty($throwloglist_elem['account'])){
                $elem['name']=$throwloglist_elem['account'];
            }else if(!empty($throwloglist_elem['carduserid'])){
                $carddata=$card->where("id=".$throwloglist_elem['carduserid'])->find();
                if(!empty($carddata)){
                    $elem['name']=$carddata['cardnumber'];
                }else{
                    $elem['name']="";
                }
            }else{
                $elem['name']="";
            }
            $elem['id']=$throwloglist_elem['id'];
            $elem['throwweight']=$throwloglist_elem['throwweight'];
            $elem['devname']=$throwloglist_elem['devname'];
            if(!empty($throwloglist_elem['rubbishtype'])){
                $rubbishtypepointdata=$rubbishtypepoint->where("rubbishtype='".$throwloglist_elem['rubbishtype']."'")->find();
                if(!empty($rubbishtypepointdata)){
                    $elem['rubbishtypename']=$rubbishtypepointdata['name'];
                }
            }
            $elem['time']=date('m-d H:i',$throwloglist_elem['throwtime']);
            $reslist[]=$elem;
        }
        $this->output_data($reslist);
        return;
    }

    public function getZongliangbudabiao(){
        $userid=session("userid");
        $roleid=session('roleid');
        if(empty($userid)||empty($roleid)){
            $this->output_data('');
            return;
        }
        $throwlog=M('user');
        if($roleid==1){
            $monthstatic=$throwlog->query('select userid,name from think_user where flag=1 and roleid=3 order by userid desc');
        }else if($roleid==2){
            $monthstatic=$throwlog->query('select userid,name from think_user where flag=1 and roleid=3 and operatoruserid='.$userid.' order by userid desc');
        }else{
            $monthstatic=$throwlog->query('select userid,name from think_user where flag=1 and roleid=3 and userid='.$userid.' order by userid desc');
        }
        $user=M('user');
        $throwlog=M('throwlog');
        $starttime= strtotime(date("Y-m-01",time()));
        if(!empty($monthstatic)){
            foreach ($monthstatic as &$monthstatic_elem){
                $userid=$monthstatic_elem['userid'];
                $weight=$throwlog->where('propertyid='.$userid.' and createtime>='.$starttime)->sum('throwweight');
                if(empty($weight)){
                    $weight=0;
                }
                $monthstatic_elem['weight']=round($weight/1000,0);
            }
            $this->output_data($monthstatic);
            return;
        }
        $this->output_data('');
        return;
    }

    private function getDelta(){
        $userid=session("userid");
        if(empty($userid)){
            return 1;
        }
        if($userid==201){
            return 7;
        }else {
            return 1;
        }
    }
}