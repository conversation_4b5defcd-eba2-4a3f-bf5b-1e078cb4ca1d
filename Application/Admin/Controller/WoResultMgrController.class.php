<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class WoResultMgrController extends CommonController {
    public function index(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $wo=M('wo');
        $result=$wo->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("wo",$result);

//        $this->assign("itemTree",CommonDao::getItemTree(1));
        $this->display();
    }

    public function getItemTree(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woid=$request["woid"];

        $items=CommonDao::getItemTree(1);
        if(!empty($items)){
            $woitem=M('woitem');
            $woitemdata=$woitem->where("woid=".$woid)->select();
            if(!empty($woitemdata)){
                foreach ($items as &$item){
                    if($item['isleaf']!=1){
                        $item['result']='';
                        $item['issuedesc']='';
                        $item['issuepic']='';
                        continue;
                    }
                    $flag=false;
                    foreach($woitemdata as $woitemelem){
                        if($woitemelem['itemid']==$item['id']){
                            $item['result']=1;
                            $item['issuedesc']=$woitemelem['issuedesc'];
                            $item['issuepic']=$woitemelem['issuepic'];
                            $flag=true;
                            break;
                        }
                    }
                    if(!$flag){
                        $item['result']=0;
                    }
                }
            }else{
                foreach ($items as &$item){
                    if($item['isleaf']!=1){
                        $item['result']='';
                        $item['issuedesc']='';
                        $item['issuepic']='';
                        continue;
                    }
                    $item['result']=0;
                }
            }
        }
        \Think\Log::record(json_encode($items),"INFO");
        $this->output_data($items);
        return;
    }

    public function getPicList(){
        $woid=I('woid');
        $itemid=I('itemid');
        if(empty($woid)){
            $this->output_data(null);
            return;
        }
        if(empty($itemid)){
            $this->output_data(null);
            return;
        }
        $woitem=M('woitem');
        $woitemdata=$woitem->where("woid=".$woid." and itemid=".$itemid)->find();
        if(empty($woitemdata)){
            $this->output_data(null);
            return;
        }
        if(empty($woitemdata['issuepic'])){
            $this->output_data(null);
            return;
        }
        $issuepics=explode("|",$woitemdata['issuepic']);
        $this->output_data($issuepics);
        return;
    }
}