<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Think\Think;
use Admin\Common\CommonUtil;

class LoginController extends CommonController {
    public function index(){

        // 判断是否自动登录
        $userid=session('userid');
        $account=session('account');
        $roleid=session('roleid');
        if(!empty($userid)&&!empty($account)&&!empty($roleid)){
            \Think\Log::record("后台自动登录成功 ".$account,"INFO");
            $this->redirect('Main/index');
        }
        $this->display();
    }

    public function login(){
        $account=I('account');
        $password=I('password');

        $user=M('user');
        $result=$user->where("account='%s'",array($account))->find();
        if(empty($result)){
            $this->assign('errorMsg','用户不存在');
            $this->display('index');
            return;
        }
//        if($result['userpwd']!=$password){
//            $this->assign('errorMsg','密码错误');
//            $this->display('index');
//            return;
//        }

        $role=M('role');
        $roledata=$role->where("roleid=".$result['roleid'])->find();
        if(empty($roledata)){
            $this->assign('errorMsg','角色不存在');
            $this->display('index');
            return;
        }
        if($roledata['loginentrance']==1){
            $this->assign('errorMsg','用户不存在');
            $this->display('index');
            return;
        }

        session('userid',$result['userid']);
        session('account',$account);
        session('roleid',$result['roleid']);
        session('orgid',$result['orgid']);
        session('operatoruserid',$result['operatoruserid']);

        if(session('roleid')==6){
            session('propertyid',$result['propertyid']);
        }else if(session('roleid')==7){
            session('arealevel',CommonUtil::getUserAreaLevel($result));
        }

        $role=M('role');
        $result=$role->where("roleid='%d'",array($result['roleid']))->find();
        if(empty($result)){
            $this->assign('errorMsg','用户角色不存在');
            $this->display('index');
            return;
        }
        session('rolename',$result['rolename']);

        \Think\Log::record("后台登录成功 ".$account,"INFO");

        if(session('roleid')==7){
            $this->redirect('Dashboard/dashboard');
            return;
        }

        $this->redirect('Main/index');
    }
}
