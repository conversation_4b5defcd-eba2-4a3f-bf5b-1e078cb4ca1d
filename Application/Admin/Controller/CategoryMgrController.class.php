<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class CategoryMgrController extends CommonController {
    public function index(){
		$this->assign("pagetitle","垃圾类别列表");
        $this->display();
    }

    public function add(){
        $category=M('category');
        $categorylist=$category->order("id asc")->select();
        $this->assign("categorylist",$categorylist);

		$this->assign("pagetitle","添加垃圾类别");
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $categoryitems=M('categoryitems');
        $result=$categoryitems->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);

        $category=M('category');
        $categorylist=$category->order("id asc")->select();
        $this->assign("categorylist",$categorylist);

		$this->assign("pagetitle","修改垃圾类别");
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $item=$request["item"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $cond=array();
        if(!empty($item)){
            $cond['item']=array('like','%'.$item.'%');
        }
        $categoryitems=M('categoryitems');
        $res['total']=$categoryitems->where($cond)->count("id");
        $result=$categoryitems->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $categoryid=$result['categoryid'];
        if(!empty($categoryid)){
            $category=M('category');
            $categorydata=$category->where('id='.$categoryid)->find();
            if(!empty($categorydata)){
                $result['categoryname']=$categorydata['name'];
            }
        }

        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $categoryid=$request["categoryid"];
        $item=$request["item"];
        if(empty($categoryid)){
            $this->output_commonerror('垃圾类别不能为空');
            return;
        }
        if(empty($item)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $categoryitems=M('categoryitems');
        $result=$categoryitems->where("item='".$item."'")->select();
        if(!empty($result)){
            $this->output_commonerror('物品名称已存在');
            return;
        }

        $categoryitems=M('categoryitems');
        $data['categoryid']=$categoryid;
        $data['item']=$item;
        $data['createtime']=time();
        $categoryitems->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $categoryid=$request["categoryid"];
        $item=$request["item"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($categoryid)){
            $this->output_commonerror('垃圾类别不能为空');
            return;
        }
        if(empty($item)){
            $this->output_commonerror('垃圾类别名称不能为空');
            return;
        }
        $categoryitems=M('categoryitems');
        $result=$categoryitems->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('垃圾类别ID不存在');
            return;
        }
        $categoryitems=M('categoryitems');
        $result=$categoryitems->where("item='".$item."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('物品名称已存在');
            return;
        }

        $categoryitems=M('categoryitems');
        $data['item']=$item;
        $data['categoryid']=$categoryid;
        $categoryitems->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $categoryitems=M('categoryitems');
        foreach ($idlist as $id){
            $categoryitems->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}