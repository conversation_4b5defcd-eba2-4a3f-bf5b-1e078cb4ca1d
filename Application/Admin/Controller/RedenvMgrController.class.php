<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class RedenvMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function detail(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $redenv=M('redenv');
        $result=$redenv->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("redenv",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $redenv=M('redenv');
        $res['total']=$redenv->count("id");
        $result=$redenv->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            $register=M('register');
            for($i=0;$i<count($result);$i++){
                $result1=$register->where("userid=".$result[$i]['userid'])->find();
                if(!empty($result1)){
                    $result[$i]['account']=$result1['account'];
                    $result[$i]['nickname']=$result1['nickname'];
                }
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $redenv=M('redenv');
        foreach ($idlist as $id){
            $redenv->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}