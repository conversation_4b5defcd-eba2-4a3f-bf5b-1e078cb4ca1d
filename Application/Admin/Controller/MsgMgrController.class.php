<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class MsgMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(24));
        $this->display();
    }

    public function add(){
        $this->assign("typelist",CommonDao::getAllMsgType());

        $this->assign("userlist",CommonDao::getAllUser());
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $msg=M('msg');
        $result=$msg->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("msg",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $msgname=$request["msgname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $msg=M('msg');
        $res['total']=$msg->count("id");
//        $cond['roleid']=2;
        $result=$msg->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$e){
                $e=$this->makeInfo($e);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($data){
        if(empty($data)){
            return $data;
        }
        $createtime=$data['createtime'];
        if(!empty($createtime)){
            $data['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $user=M('user');
        $createuserid=$data['createuserid'];
        if(!empty($createuserid)){
            $userdata=$user->where("userid=".$createuserid)->find();
            if(!empty($userdata)){
                $data['createusername']=$userdata['name'];
            }
        }
        $userid=$data['userid'];
        if(!empty($userid)){
            $userdata=$user->where("userid=".$userid)->find();
            if(!empty($userdata)){
                $data['username']=$userdata['name'];
            }
        }
        $data['typename']=CommonDao::getMsgTypeName($data['type']);
        return $data;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $title=$request["title"];
        $content=$request["content"];
        $type=$request["type"];
        $userid=$request["userid"];
        if(empty($title)){
            $this->output_commonerror('标题不能为空');
            return;
        }
        if(empty($content)){
            $this->output_commonerror('内容不能为空');
            return;
        }
        if($type==2&&empty($userid)){
            $this->output_commonerror('接收用户不能为空');
            return;
        }

        $msg=M('msg');
        $data['title']=$title;
        $data['content']=$content;
        $data['type']=$type;
        if($type==2){
            $data['userid']=$userid;
        }else{
            $data['userid']='';
        }

        $data['createuserid']=session("userid");
        $data['createtime']=time();
        $msg->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $account=$request["account"];
        $nickname=$request["nickname"];
        $msgpwd=$request["msgpwd"];
        $roleid=$request["roleid"];
        $status=$request["status"];
        if(empty($id)){
            $this->output_commonerror('用户ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('用户账号不能为空');
            return;
        }
        if(empty($nickname)){
            $this->output_commonerror('昵称不能为空');
            return;
        }
        if(empty($roleid)){
            $this->output_commonerror('角色不能为空');
            return;
        }
        if(empty($status)){
            $this->output_commonerror('状态不能为空');
            return;
        }
        $msg=M('msg');
        $result=$msg->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('用户ID不存在');
            return;
        }
        $msg=M('msg');
        $result=$msg->where("account='".$account."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('用户账号已存在');
            return;
        }

        $msg=M('msg');
        $data['account']=$account;
        $data['nickname']=$nickname;
        if(!empty($msgpwd)){
            $data['msgpwd']=$msgpwd;
        }
        $data['roleid']=$roleid;
        $data['status']=$status;
        $time=date('Y-m-d h:i:s',time());
        $data['updatetime']=$time;
        $msg->where('id='.$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $msg=M('msg');
        foreach ($idlist as $id){
            $msg->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}