<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;

class DevMgrController extends CommonController {
    public function index(){
        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $this->assign("search_propertyid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else if(session('roleid')==2){
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",1);
            $cond['operatoruserid']=session('userid');
            $propertylist=$user->where($cond)->select();
            $this->assign("propertylist",$propertylist);
        }else{
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",0);
        }

        $this->assign("alarmlist",CommonDao::getAllAlarm());

        $play=M('play');
        $playlist=$play->order('id desc')->select();
        $this->assign("playlist",$playlist);
        $this->display();
    }

    public function add(){
        $register=M('register');
        $registerproperty=M('registerproperty');
        $user=M('user');
        $operatorlist=$user->where("roleid=2")->select();
        $this->assign("operatorlist",$operatorlist);

        if(session('roleid')==1){
            $this->assign("display_operator",1);
            $this->assign("display_property",1);

            $registerpropertydata=$registerproperty->where("roleid=1")->distinct(true)->field('userid')->select();
        }else if(session('roleid')==2){
            $propertylist=$user->where("roleid=3 and operatoruserid=".session('userid'))->select();
            $this->assign("propertylist",$propertylist);
            $this->assign("display_operator",0);
            $this->assign("display_property",1);

            $registerpropertydata=$registerproperty->where("roleid=1 and operatorid=".session('userid'))->distinct(true)->field('userid')->select();
        }else{
            $this->assign("display_operator",0);
            $this->assign("display_property",0);

            $registerpropertydata=$registerproperty->where("roleid=1 and propertyid=".session('userid'))->distinct(true)->field('userid')->select();
        }

        if(!empty($registerpropertydata)){
            $collectuserlist=array();
            foreach ($registerpropertydata as $registerpropertydata_elem) {
                $registerdata=$register->where('id='.$registerpropertydata_elem['userid'])->find();
                if(!empty($registerdata)){
                    $collectuserlist[]=$registerdata;
                }
            }
            $this->assign("collectuserlist",$collectuserlist);
        }

        $area=M('area');
        $level1arealist=$area->where('parentid=-1')->select();
        $this->assign("level1arealist",$level1arealist);
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $dev=M('dev');
        $result=$dev->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }

        if(!empty($result['lat'])&&!empty($result['lng'])){
            $position=$this->Convert_GCJ02_To_BD09($result['lat'],$result['lng']);
            $lat=$position['lat'];
            $lng=$position['lng'];
        }else{
            $lat=$result['lat'];
            $lng=$result['lng'];
        }

        $this->assign("lat",$lat);
        $this->assign("lng",$lng);
        $this->assign("dev",$result);

        $register=M('register');
        $registerproperty=M('registerproperty');

        $user=M('user');
        $operatorlist=$user->where("roleid=2")->select();
        $this->assign("operatorlist",$operatorlist);

        if(session('roleid')==1){
            $this->assign("display_operator",1);
            $this->assign("display_property",1);

            $registerpropertydata=$registerproperty->where("roleid=1")->distinct(true)->field('userid')->select();
        }else if(session('roleid')==2){
            $propertylist=$user->where("roleid=3 and operatoruserid=".session('userid'))->select();
            $this->assign("propertylist",$propertylist);
            $this->assign("display_operator",0);
            $this->assign("display_property",1);

            $registerpropertydata=$registerproperty->where("roleid=1 and operatorid=".session('userid'))->distinct(true)->field('userid')->select();
        }else{
            $this->assign("display_operator",0);
            $this->assign("display_property",0);

            $registerpropertydata=$registerproperty->where("roleid=1 and propertyid=".session('userid'))->distinct(true)->field('userid')->select();
        }

        if(!empty($registerpropertydata)){
            $collectuserlist=array();
            foreach ($registerpropertydata as $registerpropertydata_elem) {
                $registerdata=$register->where('id='.$registerpropertydata_elem['userid'])->find();
                if(!empty($registerdata)){
                    $collectuserlist[]=$registerdata;
                }
            }
            $this->assign("collectuserlist",$collectuserlist);
        }

        $area=M('area');
        $level1arealist=$area->where('parentid=-1')->select();
        $this->assign("level1arealist",$level1arealist);

        if(!empty($result['areaid1'])){
            $level2arealist=$area->where('parentid='.$result['areaid1'])->select();
            $this->assign("level2arealist",$level2arealist);
        }

        if(!empty($result['areaid2'])){
            $level3arealist=$area->where('parentid='.$result['areaid2'])->select();
            $this->assign("level3arealist",$level3arealist);
        }

        if(!empty($result['areaid3'])){
            $level4arealist=$area->where('parentid='.$result['areaid3'])->select();
            $this->assign("level4arealist",$level4arealist);
        }

        $this->display();
    }

    public function detail(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $dev=M('dev');
        $result=$dev->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("lat",$result['lat']);
        $this->assign("lng",$result['lng']);
        $this->assign("dev",$result);

        $collectuserid=$result['collectuserid'];
        if(!empty($collectuserid)){
            $register=M('register');
            $registerdata=$register->where("id=".$collectuserid)->find();
            if(!empty($registerdata)){
                $this->assign("collectusername",$registerdata['name']);
            }
        }

        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $devcode     =$request["devcode"];
        $devname     =$request["devname"];
        $operatorid  =$request["operatorid"];
        $propertyid  =$request["propertyid"];
        $alarmflag  =$request["alarm"];
        $communityname  =$request["communityname"];

        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        if($alarmflag==1){
            $offset=0;
            $limit=10000;
        }

        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }
        if(!empty($devcode)){
            $cond['code']=array('like','%'.$devcode.'%');
        }
        if(!empty($devname)){
            $cond['name']=array('like','%'.$devname.'%');
        }
        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }
        if (!empty($communityname)){
            $cond['communityname']=array('like','%'.$communityname.'%');
        }

        $dev=M('dev');
        $alarm=M('alarm');
        $res['total']=$dev->where($cond)->count("id");
        $result=$dev->where($cond)->order('id desc')->limit($offset,$limit)->select();

        $newresult=array();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $alarmdata=$alarm->where("devcode='".$result[$i]['code']."' and status=0")->select();
                if(empty($alarmdata)){
                    if($alarmflag==1){
                        continue;
                    }
                    $result[$i]['isalarm']=0;
                }else{
                    $result[$i]['isalarm']=1;
                }

                $newresult[]=$this->makeInfo($result[$i]);
            }
        }

        if($alarmflag==1){
            $res['total']=count($newresult);
        }
        $res['rows']=$newresult;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        $time=time()-C('onlineTimeDelta');
        if($result['heartbeattime']>$time){
            $result['onlinestatusname']="在线";
            $result['onlinestatus']=1;
        }else{
            $result['onlinestatusname']="离线";
            $result['onlinestatus']=0;
        }

        $collectuserid=$result['collectuserid'];
        if(!empty($collectuserid)){
            $register=M('register');
            $registerdata=$register->where("id=".$collectuserid)->find();
            $result['collectuseraccount']=$registerdata['account'];
            $result['collectusername']=$registerdata['name'];
        }

        $user=M('user');
        if(!empty($result['propertyid'])){
            $userdata=$user->where("userid=".$result['propertyid'])->find();
            if(!empty($userdata)){
                $result['propertyname']=$userdata['name'];
            }
        }
        if(!empty($result['operatorid'])){
            $userdata=$user->where("userid=".$result['operatorid'])->find();
            if(!empty($userdata)){
                $result['operatorname']=$userdata['name'];
            }
        }
        if(!empty($result['playid'])){
            $play=M('play');
            $playdata=$play->where("id=".$result['playid'])->find();
            if(!empty($playdata)){
                $result['playname']=$playdata['name'];
            }
        }

        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $communityname=$request["communityname"];
        $address=$request["address"];
        $collectuserid=$request["collectuserid"];
        $operatorid=$request["operatorid"];
        $propertyid=$request["propertyid"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        $areaid1=$request["areaid1"];
        $areaid2=$request["areaid2"];
        $areaid3=$request["areaid3"];
        $areaid4=$request["areaid4"];
        $households=$request["households"];
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
        if(empty($lat)||empty($lng)){
            $this->output_commonerror('地址不能为空');
            return;
        }

        $position=$this->Convert_BD09_To_GCJ02($lat,$lng);
        $lat=$position['lat'];
        $lng=$position['lng'];

        if(session('roleid')==2){
            $operatorid=session('userid');
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
        }

        if(empty($operatorid)){
            $this->output_commonerror('运营商不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('物业不能为空');
            return;
        }

        $dev=M('dev');
        $result=$dev->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $dev=M('dev');
        $data['code']=$code;
        $data['name']=$name;
        $data['communityname']=$communityname;
        $data['address']=$address;
        if(!empty($collectuserid)){
            $data['collectuserid']=$collectuserid;
        }
        $data['operatorid']=$operatorid;
        $data['propertyid']=$propertyid;
        $data['createtime']=time();
        $data['lat']=$lat;
        $data['lng']=$lng;
        $data['areaid1']=$areaid1;
        $data['areaid2']=$areaid2;
        $data['areaid3']=$areaid3;
        $data['areaid4']=$areaid4;
        $data['households']=$households;
        $data['areaname']=$this->getAreaname($areaid1,$areaid2,$areaid3,$areaid4);
        $dev->add($data);
        $this->output_data("");
        return;
    }

    private function getAreaname($areaid1,$areaid2,$areaid3,$areaid4){
        $area=M('area');
        if(!empty($areaid4)){
            $areaid=$areaid4;
        }else if(!empty($areaid3)){
            $areaid=$areaid3;
        }else if(!empty($areaid2)){
            $areaid=$areaid2;
        }else if(!empty($areaid1)){
            $areaid=$areaid1;
        }else{
            return "";
        }
        $areadaata=$area->where('areaid='.$areaid)->find();
        if(empty($areadaata)){
            return "";
        }
        return $areadaata['areaname'];
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $code=$request["code"];
        $name=$request["name"];
        $communityname=$request["communityname"];
        $address=$request["address"];
        $customerphone=$request["customerphone"];
        $collectuserid=$request["collectuserid"];
        $operatorid=$request["operatorid"];
        $propertyid=$request["propertyid"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        $areaid1=$request["areaid1"];
        $areaid2=$request["areaid2"];
        $areaid3=$request["areaid3"];
        $areaid4=$request["areaid4"];
//        $welcomemsg=$request["welcomemsg"];
        $households=$request["households"];
        if(empty($id)){
            $this->output_commonerror('设备ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }

        if(session('roleid')==2){
            $operatorid=session('userid');
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
        }

        if(empty($operatorid)){
            $this->output_commonerror('运营商不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('物业不能为空');
            return;
        }
        if(empty($lat)){
            $this->output_commonerror('位置不能为空');
            return;
        }
        if(empty($lng)){
            $this->output_commonerror('位置不能为空');
            return;
        }

        $position=$this->Convert_BD09_To_GCJ02($lat,$lng);
        $lat=$position['lat'];
        $lng=$position['lng'];

        $dev=M('dev');
        $result=$dev->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }
//        $oldwelcomemsg=$result['welcomemsg'];
        $dev=M('dev');
        $result=$dev->where("code='".$code."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $dev=M('dev');
        $data['code']=$code;
        $data['name']=$name;
        $data['communityname']=$communityname;
        $data['address']=$address;
        $data['customerphone']=$customerphone;
        $data['collectuserid']=$collectuserid;
        $data['operatorid']=$operatorid;
        $data['propertyid']=$propertyid;
        $data['lat']=$lat;
        $data['lng']=$lng;
        $data['areaid1']=$areaid1;
        $data['areaid2']=$areaid2;
        $data['areaid3']=$areaid3;
        $data['areaid4']=$areaid4;
        $data['households']=$households;
//        $data['welcomemsg']=$welcomemsg;
        $data['areaname']=$this->getAreaname($areaid1,$areaid2,$areaid3,$areaid4);
        $dev->where('id='.$id)->save($data);

//        if(strcmp($oldwelcomemsg,$welcomemsg)!=0){
//
//        }

        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $dev=M('dev');
        foreach ($idlist as $id){
            $dev->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }

    public function findposition()
    {
        $this->assign("lat",$_REQUEST['lat']);
        $this->assign("lng",$_REQUEST['lng']);
        $this->display();
    }

    public function api_getdevparam(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_data("");
            return;
        }
        $dev=M('dev');
        $devdata=$dev->where("id=".$id)->find();
        if(empty($devdata)){
            $this->output_commonerror('设备不存在');
            return;
        }
        $req = array();
        $req['devcode'] = $devdata['code'];

        $url = $this::server_url."getdevparam.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("getdevparam res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        if(empty($res)||$res['retCode']!=0){
            $this->output_commonerror('返回失败');
            return;
        }
        $this->output_data($res['data']);
        return;
    }

    public function getQRRandCode(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devcode=$request["devcode"];
        if(empty($devcode)){
            $this->output_data("");
            return;
        }
        $dev=M('dev');
        $devdata=$dev->where("code='".$devcode."'")->find();
        if(empty($devdata)){
            $this->output_commonerror('设备不存在');
            return;
        }

        $url = $this::server_url_devapi."getrandcode.do?card_number=".$devdata['code']."&client_type=10&client_version=1.0.0&encrypted=0&requestId=123";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
//        curl_setopt($curl,CURLOPT_POST,true);
//        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("getrandcode res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        if(empty($res)||$res['status']!="success"){
            $this->output_commonerror('返回失败');
            return;
        }
        $this->output_data($res['randcode']);
        return;
    }

    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }

    public function getSubArea(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $areaid=$request["areaid"];
        if(empty($areaid)){
            $this->output_data("");
            return;
        }
        $area=M('area');
        $data=$area->where("parentid=".$areaid)->select();
        $this->output_data($data);
        return;
    }

    public function setTextMsg(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $msgcontent=$request["msgcontent"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $req=array();
        $req['devids']=$idlist;
        $req['msgcontent']=$msgcontent;
        $url = $this::server_url."setTextMsgNotify.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("setTextMsg res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        if(empty($res)||$res['retCode']!=0){
            $this->output_commonerror('返回失败');
            return;
        }
        $this->output_data('');
        return;
    }

    public function setPlayID(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $playid=$request["playid"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        if(empty($playid)){
            $this->output_commonerror('播放列表不能为空');
            return;
        }

        $play=M('play');
        $playdata=$play->where('id='.$playid)->find();
        if(empty($playdata)){
            $this->output_commonerror('播放列表不存在');
            return;
        }

        $playurl=$playdata['url'];
        if(empty($playurl)){
            $this->output_commonerror('播放文件URL不存在');
            return;
        }
        $ROOT = $_SERVER['DOCUMENT_ROOT'];
        $md5=md5_file($ROOT.$playurl);
        if(empty($md5)){
            $this->output_commonerror('计算播放文件MD5失败');
            return;
        }

        $config=M('config');
        $localurl=$config->where("configkey='localurl'")->find();
        $playlistcontent=$localurl['configvalue'].$playurl."#".$md5."#0";

        $filetask=M('filetask');
        foreach ($idlist as $id){
            $tempcond2['devid']=$id;
//            $tempcond2['playid']=$playid;
            $tempcond2['filetype']=3;
            $filetask->where($tempcond2)->delete();

            $data['devid']=$id;
            $data['status']=0;
            $data['filetype']=3;
            $data['failtimes']=0;
            $data['content']=$playlistcontent;
            $data['createtime']=time();
            $data['playid']=$playid;
            $filetask->add($data);
        }

        $playadsfile=M('playadsfile');
        $ads=M('ads');
        $playadsfilelist=$playadsfile->where('playid='.$playid)->select();
        if(!empty($playadsfilelist)){
            foreach ($playadsfilelist as $playadsfile){
                $adsid=$playadsfile['adsfileid'];
                $adsdata=$ads->where('id='.$adsid)->find();
                if(empty($adsdata)){
                    continue;
                }
                $content=$adsdata['content'];
                foreach ($idlist as $id){

                    $tempcond['devid']=$id;
                    $tempcond['status']=3;
                    $tempcond['adsid']=$adsid;
                    $filetaskdb=$filetask->where($tempcond)->select();
                    if(!empty($filetaskdb)){
                        \Think\Log::record("文件之前已下发成功，忽略此次下发 filetaskdb=".json_encode($filetaskdb),"INFO");
                        continue;
                    }

                    $tempcond1['devid']=$id;
                    $tempcond1['adsid']=$adsid;
                    $filetask->where($tempcond1)->delete();

                    $data['devid']=$id;
                    $data['status']=0;
                    $data['filetype']=2;
                    $data['failtimes']=0;
                    $data['content']=$content;
                    $data['createtime']=time();
                    $data['adsid']=$adsid;
                    $filetask->add($data);
                }
            }
        }

        $this->output_data('');
        return;
    }

    public function reboot(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $dev=M('dev');
        $devcodes=array();
        foreach ($idlist as $id){
            $devdata=$dev->where('id='.$id)->find();
            if(empty($devdata)){
                continue;
            }
            $devcodes[]=$devdata['code'];
        }
        $req=array();
        $req['devcodes']=$devcodes;
        $req['flag']=0;
        $url = $this::server_url."sendControlMsg.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("sendControlMsg res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        if(empty($res)||$res['retCode']!=0){
            $this->output_commonerror('返回失败');
            return;
        }
        $this->output_data('');
        return;
    }

    /*
    * 中国正常GCJ02坐标---->百度地图BD09坐标
    * 腾讯地图用的也是GCJ02坐标
    * @param double $lat 纬度
    * @param double $lng 经度
    */
    function Convert_GCJ02_To_BD09($lat,$lng){
        $x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        $x = $lng;
        $y = $lat;
        $z =sqrt($x * $x + $y * $y) + 0.00002 * sin($y * $x_pi);
        $theta = atan2($y, $x) + 0.000003 * cos($x * $x_pi);
        $lng = $z * cos($theta) + 0.0065;
        $lat = $z * sin($theta) + 0.006;
        return array('lng'=>$lng,'lat'=>$lat);
    }


    /*
    * 百度地图BD09坐标---->中国正常GCJ02坐标
    * 腾讯地图用的也是GCJ02坐标
    * @param double $lat 纬度
    * @param double $lng 经度
    * @return array();
    */
    function Convert_BD09_To_GCJ02($lat,$lng){
        $x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        $x = $lng - 0.0065;
        $y = $lat - 0.006;
        $z = sqrt($x * $x + $y * $y) - 0.00002 * sin($y * $x_pi);
        $theta = atan2($y, $x) - 0.000003 * cos($x * $x_pi);
        $lng = $z * cos($theta);
        $lat = $z * sin($theta);
        return array('lng'=>$lng,'lat'=>$lat);
    }
}
