<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class OrgzMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(10));
        $this->display();
    }

    public function api_getOrgs(){
        $org=M('org');
        $subdata=$org->where("parentid=-1")->select();
        $nodes=array();
        if(!empty($subdata)){
            foreach ($subdata as $elem){
                $result=$this->makeOrgTree($elem['id']);
                if(!empty($result)){
                    $nodes[]=$result;
                }
            }
        }
        $this->output_data($nodes);
    }

    public function api_addOrg(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        \Think\Log::record(json_encode($request),"INFO");
        $parentId=$request["parentId"];
        $name=$request["name"];
        $ordernum=$request["ordernum"];

        if(empty($parentId)){
            $this->output_commonerror("parentId为空");
            return;
        }
        if(empty($name)){
            $this->output_commonerror("name为空");
            return;
        }
        if(empty($ordernum)){
            $ordernum=0;
        }
        $data['name']=$name;
        $data['parentid']=$parentId;
        $data['ordernum']=$ordernum;
        $org=M('org');
        $org->add($data);
        $this->output_data(null);
    }

    public function api_modOrg(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        \Think\Log::record(json_encode($request),"INFO");
        $id=$request["id"];
        $name=$request["name"];
        $ordernum=$request["ordernum"];

        if(empty($id)){
            $this->output_commonerror("id为空");
            return;
        }
        if(empty($name)){
            $this->output_commonerror("name为空");
            return;
        }
        if(empty($ordernum)){
            $ordernum=0;
        }
        $data['name']=$name;
        $data['ordernum']=$ordernum;
        $org=M('org');
        $org->where("id=".$id)->save($data);
        $this->output_data(null);
    }

    public function api_delOrg(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_commonerror("节点为空");
            return;
        }
        $org=M('org');
        $data=$org->where('id='.$id)->find();
        if(empty($data)){
            $this->output_commonerror("节点不存在");
            return;
        }
        if($data['parentid']==-1){
            $this->output_error(null,"1024","不允许删除根节点");
            return;
        }

        $subs=$org->where("parentid=".$id)->select();
        if(!empty($subs)){
            $this->output_error(null,"1025","请先删除子节点");
            return;
        }
        $org->where('id='.$id)->delete();
        $this->output_data();
        return;
    }

    private function makeOrgTree($id){
        $org=M('org');
        $data=$org->where("id=".$id)->find();
        if(empty($data)){
            return;
        }
        $node['dataid']=$data['id'];
        $node['text']=$data['name'];
        $node['ordernum']=$data['ordernum'];

//        $button="<button type='button' id='btnEdit_".$data['id']."' class='btn btn-primary btn-xs' onclick='exxecutefunction()'>"."<i class='glyphicon glyphicon-edit'></i> Edit"."</button>";
//        $node['text']="<div style='display: inline-block'><span>".$data['name']."</span>".$button."</div>";

        $subdata=$org->where("parentid=".$id)->select();
        if(empty($subdata)){
            return $node;
        }
        $nodes=array();
        foreach ($subdata as $elem){
            $result=$this->makeOrgTree($elem['id']);
            if(!empty($result)){
                $nodes[]=$result;
            }
        }
        if(!empty($nodes)){
            array_multisort(array_column($nodes,'ordernum'),SORT_ASC,$nodes);
            $node['nodes']=$nodes;
        }
        return $node;
    }

    public function api_getOrgPath(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_commonerror("id为空");
            return;
        }

        $res=$this->makeOrgPath($id);
        $this->output_data($res);
        return;
    }
    private function makeOrgPath($id){
        if(empty($id)){
            return;
        }
        $org=M('org');
        $data=$org->where("id=".$id)->find();
        if(empty($data)){
            return;
        }

        $parentdatas=$org->where("id=".$data['parentid'])->find();

        if(empty($parentdatas)){
            return $data['name'];
        }else{
            $parentPath=$this->makeOrgPath($parentdatas[id]);
            if(empty($parentPath)){
                return $parentdatas['name']." > ".$data['name'];
            }else{
                return $parentPath." > ".$data['name'];
            }
        }
    }

    public function api_getCitys(){
        $city=M('city');
        $citydata=$city->order('cityid asc')->select();
        if(!empty($citydata)){
            $maintenance=M('maintenance');
            for($i=0;$i<count($citydata);$i++){
                $maintenancedata=$maintenance->where("cityid=".$citydata[$i]['cityid'])->select();
                if(!empty($maintenancedata)){
                    $citydata[$i]['subnode']=array();
                    for($j=0;$j<count($maintenancedata);$j++){
                        $citydata[$i]['subnode'][$j]=$maintenancedata[$j];
                    }
                }
            }
        }
        $this->output_data($citydata);
    }

    public function api_getUserlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        \Think\Log::record(json_encode($request),"INFO");
        $offset=$request["offset"];
        $limit=$request["limit"];
        $level=$request["level"];
        $levelid=$request["levelid"];
//        $username=$request["username"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if($level==0){
            $cond['roleid']=2;
        }else if($level==1){
            $cond['roleid']=4;
            $cond['cityid']=$levelid;
        }else if($level==2){
//            $cond['roleid']=5;
//            $cond['maintenanceid']=$levelid;
            $cond="(roleid=5 or roleid=6 ) and maintenanceid=".$levelid;
        }else{
            return;
        }
        $user=M('user');
        $res['total']=$user->where($cond)->count("userid");
//        $cond['roleid']=2;
        $result=$user->where($cond)->order('userid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return null;
        }
        $elem['rolename']=CommonDao::getRoleName($elem['roleid']);
        $elem['cityname']=CommonDao::getCityInfo($elem['cityid']);
        $elem['maintenancename']=CommonDao::getMaintenanceInfo($elem['maintenanceid']);
        $elem['manufacturername']=CommonDao::getManufacturerInfo($elem['manufacturerid']);

        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $elem;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $useridlist=$request["useridlist"];
        if(empty($useridlist)){
            $this->output_data();
            return;
        }
        $user=M('user');
        foreach ($useridlist as $userid){
            $user->where('userid='.$userid)->delete();
        }
        $this->output_data();
        return;
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $devid=I('devid');
        if(empty($devid)){
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devid='".$devid."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("dev",$result);
        $this->display();
    }

    public function api_getDevlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $devname=$request["devname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $dev=M('dev');
        $res['total']=$dev->count("devid");
        $cond['status']=1;
        $result=$dev->where($cond)->order('devid desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $devStatus=$this->getDevStatus($result[$i]['devid']);
                if(empty($devStatus)){
                    $result[$i]['online']='离线';
                    continue;
                }else{
                    $result[$i]['online']='在线';
                    $result[$i]['elect']=$devStatus['electricity'];
                    $result[$i]['count']=$devStatus['currentcount'];
                }
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function getDevStatus($devid){
        $req = array();
        $req['devid'] = $devid;

        $devStatus=array();

        $url = $this::server_url."getDevStatus.do";
        error_log($url);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        error_log("getDevStatus res=".$res);
        $res = json_decode($res,true);
        curl_close($curl);
        if("0"!=$res["retCode"]){
            return $devStatus;
        }
        $devStatus['currentcount']=$res["data"]['currentcount'];
        $devStatus['electricity']=$res["data"]['electricity'];
        return $devStatus;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devcode=$request["devcode"];
        $devname=$request["devname"];
        if(empty($devcode)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($devname)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devcode='".$devcode."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devname='".$devname."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $dev=M('dev');
        $data['devcode']=$devcode;
        $data['devname']=$devname;
        $time=date('Y-m-d h:i:s',time());
        $data['createtime']=$time;
        $data['updatetime']=$time;
        $dev->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devid=$request["devid"];
        $devcode=$request["devcode"];
        $devname=$request["devname"];
        if(empty($devid)){
            $this->output_commonerror('设备ID不能为空');
            return;
        }
        if(empty($devcode)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($devname)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devid=".$devid)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devcode='".$devcode."' and devid!=".$devid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $dev=M('dev');
        $result=$dev->where("devname='".$devname."' and devid!=".$devid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $dev=M('dev');
        $data['devcode']=$devcode;
        $data['devname']=$devname;
        $time=date('Y-m-d h:i:s',time());
        $data['updatetime']=$time;
        $dev->where('devid='.$devid)->save($data);
        $this->output_data();
        return;
    }
}