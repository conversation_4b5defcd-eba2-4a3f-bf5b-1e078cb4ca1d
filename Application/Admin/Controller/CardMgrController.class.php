<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
use Common\Common\CommonUtil;
class CardMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(46));

//        $user=M('user');
//        $operatorlist=$user->where("roleid=2")->select();
//        $this->assign("operatorlist",$operatorlist);
//        if(session('roleid')==1){
//            $this->assign("search_operatorid_visible",1);
//            $this->assign("search_propertyid_visible",1);
//        }else if(session('roleid')==2){
//            $propertylist=$user->where("roleid=3 and operatoruserid=".session('userid'))->select();
//            $this->assign("propertylist",$propertylist);
//            $this->assign("search_operatorid_visible",0);
//            $this->assign("search_propertyid_visible",1);
//        }else{
//            $this->assign("search_operatorid_visible",0);
//            $this->assign("search_propertyid_visible",0);
//        }
        $this->assign("search_visible",1);

        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $this->assign("search_propertyid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else if(session('roleid')==2){
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",1);
            $cond['operatoruserid']=session('userid');
            $propertylist=$user->where($cond)->select();
            $this->assign("propertylist",$propertylist);
        }else{
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",0);
        }

        $this->display();
    }

    public function add(){
        $user=M('user');
        $operatorlist=$user->where("roleid=2")->select();
        $this->assign("operatorlist",$operatorlist);

        if(session('roleid')==1){
            $this->display();
        }else if(session('roleid')==2){
            $propertylist=$user->where("roleid=3 and operatoruserid=".session('userid'))->select();
            $this->assign("propertylist",$propertylist);
            $this->display("addoperator");
        }else{
            $this->display("addproperty");
        }
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $card=M('card');
        $result=$card->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("card",$result);

        $user=M('user');
        $operatorlist=$user->where("roleid=2")->select();
        $this->assign("operatorlist",$operatorlist);

        if(session('roleid')==1){
            $this->display();
        }else if(session('roleid')==2){
            $propertylist=$user->where("roleid=3 and operatoruserid=".session('userid'))->select();
            $this->assign("propertylist",$propertylist);
            $this->display("updateoperator");
        }else{
            $this->display("updateproperty");
        }
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $cardnumber=$request["cardnumber"];
        $operatorid  =$request["operatorid"];
        $propertyid  =$request["propertyid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=1000;
        }
        $card=M('card');
        $cond['status']=0;
        if(session("roleid")==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session("roleid")==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }else{
        }

        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }
        if(!empty($cardnumber)){
            $cond['cardnumber']=array('like','%'.$cardnumber.'%');
        }

        $res['total']=$card->where($cond)->count("id");
        $result=$card->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($element){
        $createtime = $element["createtime"];
        if (!empty($createtime)) {
            $element["createtime"] = date('Y-m-d H:i', $createtime);
        }
        $user=M("user");
        $operatorid=$element["operatorid"];
        if(!empty($operatorid)){
            $userdata=$user->where("userid=".$operatorid)->find();
            if(!empty($userdata)){
                $element["operatorname"] = $userdata["name"];
            }
        }
        $propertyid=$element["propertyid"];
        if(!empty($propertyid)){
            $userdata=$user->where("userid=".$propertyid)->find();
            if(!empty($userdata)){
                $element["propertyname"] = $userdata["name"];
            }
        }
        $element["cardqrcode"] = sprintf('%09d', $element['id'])."001";
        $element['dishonestflagname']=CommonDao::getDishonestflagname($element['dishonestflag']);

        $feature=$element["feature"];
        if(!empty($feature)){
            $element["featurename"]="是";
        }else{
            $element["featurename"]="否";
        }
        return $element;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $cardnumber=$request["cardnumber"];
        $name=$request["name"];
        $idcardname=$request["idcardname"];
        $idcardnum=$request["idcardnum"];
        $phonenum=$request["phonenum"];
        $operatorid=$request["operatorid"];
        $propertyid=$request["propertyid"];
        if(empty($cardnumber)){
            $this->output_commonerror('卡号不能为空');
            return;
        }
        $card=M('card');
        $result=$card->where("cardnumber='".$cardnumber."'")->find();
        if(!empty($result)){
            if($result['status']==0){
                $this->output_commonerror('卡号已存在');
                return;
            }else{
                $card->where("id=".$result['id'])->delete();
            }
        }

        if(session('roleid')==2){
            $operatorid=session('userid');
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
        }
        if(empty($operatorid)){
            $this->output_commonerror('运营商不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('物业不能为空');
            return;
        }

        $card=M('card');
        $data['cardnumber']=$cardnumber;
        $data['name']=$name;
        $data['idcardname']=$idcardname;
        $data['idcardnum']=$idcardnum;
        $data['phonenum']=$phonenum;
        $data['operatorid']=$operatorid;
        $data['propertyid']=$propertyid;
        $data['status']=0;
        $time=time();
        $data['createtime']=$time;
        $data['updatetime']=$time;
        $data['updatetimepoint']=$time;
        $card->add($data);
        CommonUtil::updateUserNotify();
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $cardnumber=$request["cardnumber"];
        $name=$request["name"];
        $idcardname=$request["idcardname"];
        $idcardnum=$request["idcardnum"];
        $phonenum=$request["phonenum"];
        $operatorid=$request["operatorid"];
        $propertyid=$request["propertyid"];
        $point=$request["point"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($cardnumber)){
            $this->output_commonerror('卡号不能为空');
            return;
        }
        $card=M('card');
        $result=$card->where("cardnumber='".$cardnumber."' and id!=".$id)->find();
        if(!empty($result)){
            if($result['status']==0){
                $this->output_commonerror('卡号已存在');
                return;
            }else{
                $card->where("id=".$result['id'])->delete();
            }
        }

        if(session('roleid')==2){
            $operatorid=session('userid');
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
        }
        if(empty($operatorid)){
            $this->output_commonerror('运营商不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('物业不能为空');
            return;
        }

        $card=M('card');
        $data['cardnumber']=$cardnumber;
        $data['name']=$name;
        $data['idcardname']=$idcardname;
        $data['idcardnum']=$idcardnum;
        $data['phonenum']=$phonenum;
        $data['operatorid']=$operatorid;
        $data['propertyid']=$propertyid;
        if(isset($point)){
            $data['point']=$point;
        }
        $data['updatetime']=time();
        $card->where("id=".$id)->save($data);
        CommonUtil::updateUserNotify();
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $card=M('card');
        foreach ($idlist as $id){
            $data['status']=1;
            $data['point']=0;
            $data['updatetime']=time();
            $card->where('id='.$id)->save($data);
        }
        CommonUtil::updateUserNotify();
        $this->output_data("");
        return;
    }

    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }

    public function importFile(){
        $operatorid=I('operatorid');
        $propertyid=I('propertyid');
        if(session('roleid')==2){
            $operatorid=session('userid');
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
        }
        if(empty($operatorid)){
            $this->output_commonerror('运营商不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('物业不能为空');
            return;
        }
        $upload = new \Think\Upload();
        $upload->maxSize=0;//(不限制上传大小，但是同时要修改php.ini中的upload_max_filesize和post_max_size上传大小限制，否则过大会报错）
        $upload->rootPath='Public/Upload/';//上传图片目录
        $upload->exts=array('xls','xlsx');//允许上传的图片格式
        $upload->saveName=array('\Common\Common\CommonUtil::makeFileName',array('__FILE__'));
        $info=$upload->upload();//上传
//        \Think\Log::record("operatorid=".$operatorid,"INFO");
//        \Think\Log::record("propertyid=".$propertyid,"INFO");
        if(!$info){//上传错误，输出错误信息
            $result=json_encode(
                $upload->getError()
            );
        }else{//上传成功，输出图片地址和上传的结果，因为前端ajax接收的是json的数据，所以要进行json_encode
            $filepath="Public/Upload/".$info['picture']['savepath'].$info['picture']['savename'];
            $extension = strtolower(pathinfo($filepath, PATHINFO_EXTENSION));
            Vendor ('PHPExcel');
            //创建一个读Excel模版的对象
//            $objReader = \PHPExcel_IOFactory::createReader ( 'Excel2007');
            if ($extension =='xlsx') {
                $objReader = new \PHPExcel_Reader_Excel2007();
            } else if ($extension =='xls') {
                $objReader = \PHPExcel_IOFactory::createReader ( 'Excel5' );
            }else{
                return;
            }
            $objPHPExcel = $objReader->load ($filepath,$encode='utf-8');
            $sheet = $objPHPExcel -> getSheet(0);
            $highestRow = $sheet -> getHighestRow(); // 取得总行数
            \Think\Log::record("highestRow=".$highestRow,"INFO");
            $card=M('card');
            for($i=2;$i<=$highestRow;$i++){
                $cardnumber=$objPHPExcel -> getActiveSheet() -> getCell("A".$i)->getValue();
                if(empty($cardnumber)){
                    continue;
                }
                $cardnumber=trim($cardnumber);
                if(empty($cardnumber)){
                    continue;
                }

                $name=$objPHPExcel -> getActiveSheet() -> getCell("B".$i)->getValue();
                $name=trim($name);

                $phonenum=$objPHPExcel -> getActiveSheet() -> getCell("C".$i)->getValue();
                $phonenum=trim($phonenum);

                $carddatadb=$card->where("cardnumber='".$cardnumber."'")->find();
                if(empty($carddatadb)){
                    $data['cardnumber'] = $cardnumber;
                    $data['name'] = $name;
                    $data['phonenum'] = $phonenum;
                    $data['operatorid']=$operatorid;
                    $data['propertyid']=$propertyid;
                    $data['status']=0;
                    $data['point']=0;
                    $data['updatetimepoint']=time();
                    $data['createtime']=time();
                    $data['updatetime']=time();
                    $card->add($data);
                }else{
                    $data['name'] = $name;
                    $data['phonenum'] = $phonenum;
                    $data['operatorid']=$operatorid;
                    $data['propertyid']=$propertyid;
                    $data['status']=0;
                    $data['updatetime']=time();
                    $card->where("cardnumber='".$cardnumber."'")->save($data);
                }

            }
            CommonUtil::updateUserNotify();
//            \Think\Log::record("uploadFile=".json_encode($allData),"INFO");
            $result=json_encode(
                array(
                    'retCode'=>"0",
                )
            );
        }
        echo $result;//输出信息
    }



    public function api_exportqrzipfile(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_commonerror('请选择用户');
            return;
        }

        $cardqrcodes="";
        foreach ($idlist as $id){
            $cardqrcodes = $cardqrcodes.",".sprintf('%09d', $id)."001";
        }
        $req = array();
        $req['cardqrcodes'] = $cardqrcodes;

        $url = $this::server_url."getQRZip.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("openDoor res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        $this->output_data($this::selfurl."Public/zipqrcodefile/".$res['data']['zipfile']);
//        import('ORG.NET.HTTP');
//        $http=new \Org\NET\Http;
//        $http->download("/opt/lampp/htdocs/recycle/Public/zipqrcodefile/".$res['data']['zipfile'],$res['data']['zipfile']);
        return;
    }

    public function api_exportdudaotoudiqrzipfile(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $cardnumberlist=$request["cardnumberlist"];
        if(empty($cardnumberlist)){
            $this->output_commonerror('请选择卡用户');
            return;
        }

        $cardqrcodes="";
        foreach ($cardnumberlist as $cardnumber){
            $cardqrcodes = $cardqrcodes.",".trim($cardnumber);
        }
        $req = array();
        $req['cardqrcodes'] = $cardqrcodes;

        $url = $this::server_url."getDudaotoudiQRZip.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("getDudaotoudiQRZip res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);

        $this->output_data($this::devserverurl."Public/zipqrcodefile/".$res['data']['zipfile']);
//        import('ORG.NET.HTTP');
//        $http=new \Org\NET\Http;
//        $http->download("/opt/lampp/htdocs/recycle/Public/zipqrcodefile/".$res['data']['zipfile'],$res['data']['zipfile']);
        return;
    }

    public function costPoint(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $point=$request["point"];
        $remark=$request["remark"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($point)){
            $this->output_error('','1','消费积分不能为空');
            return;
        }
        $card=M('card');
        $carddata=$card->where('id='.$id)->find();
        if(empty($carddata)){
            $this->output_commonerror('记录不存在');
            return;
        }
        $pointdb=$carddata['point'];
        if(empty($pointdb)){
            $pointdb=0;
        }
        $newpoint=$pointdb-$point;
        $newdata['point']=$newpoint;
        $card->where('id='.$id)->save($newdata);

        $costpointlog=M('costpointlog');
        $costpointlogdata['carduserid']=$carddata['id'];
        $costpointlogdata['costpoint']=$point;
        $costpointlogdata['remark']=$remark;
        $costpointlogdata['createtime']=time();
        $costpointlogdata['beforepoint']=$pointdb;
        $costpointlogdata['afterpoint']=$newpoint;
        $costpointlogdata['opruserid']=session('userid');
        $costpointlogdata['propertyid']=$carddata['propertyid'];
        $costpointlog->add($costpointlogdata);
        $this->output_data("");
        return;
    }
}