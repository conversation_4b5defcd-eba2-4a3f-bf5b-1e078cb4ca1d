<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class ViolationMgrController extends CommonController {
    public function index(){
        $roleid=session('roleid');
        if(empty($roleid)){
            return;
        }
        $this->assign('privilege',$this->getPrivilege(51));
        $this->assign('roleid',$roleid);
        $this->display();
    }

    public function add(){
        $roleid=session('roleid');
        if(empty($roleid)){
            return;
        }

        $this->assign('roleid',$roleid);
        if($roleid==3){
            $violation=M('violation');
            $operatorlist=$violation->where("roleid=2")->select();
            $this->assign('operatorlist',$operatorlist);

            $roleid=session("roleid");
            if($roleid==1){
                $this->assign("canmodoperator",1);
            }else{
                $this->assign("canmodoperator",0);
            }

            $this->display("addproperty");
        }else{
            $this->display();
        }
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $violation=M('violation');
        $result=$violation->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("violation",$result);

        if($result['roleid']==3){
            $roleid=session("roleid");
            if($roleid==1){
                $this->assign("canmodoperator",1);
            }else{
                $this->assign("canmodoperator",0);
            }
            $violation=M('violation');
            $operatorlist=$violation->where("roleid=2")->select();
            $this->assign('operatorlist',$operatorlist);
            $this->display("updateproperty");
        }else{
            $this->display();
        }
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $name =$request["name"];
        $account =$request["account"];
        $cardnum =$request["cardnum"];
        $starttime =$request["starttime"];
        $endtime =$request["endtime"];
        $operatorid =$request["operatorid"];
        $propertyid =$request["propertyid"];

        $roleid=session('roleid');
        if(empty($roleid)){
            return;
        }
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }

        if(!empty($name)){
            $cond['name']=array('like','%'.$name.'%');
        }
        if(!empty($account)){
            $cond['account']=array('like','%'.$account.'%');
        }
        if(!empty($cardnum)){
            $cond['cardnumber']=array('like','%'.$cardnum.'%');
        }
        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }
        if(empty($starttime)){
            $starttime=time()-3600*24*90;
        }else{
            $starttime=strtotime($starttime);
        }
        if(empty($endtime)){
            $endtime=time()+100;
        }else{
            $endtime=strtotime($endtime);
        }
        $condtime=" createtime >=".$starttime." and createtime<".$endtime." ";

        $violation=M('violation');
        $res['total']=$violation->where($cond)->where($condtime)->count("id");
        $result=$violation->where($cond)->where($condtime)->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return null;
        }

        $elem['operatorname']=CommonDao::getUserName($elem['operatorid']);
        $elem['propertyname']=CommonDao::getUserName($elem['propertyid']);
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
//        if(!empty($elem['remark'])&&strlen($elem['remark'])>20){
//            $elem['remark']=substr($elem['remark'],20)."...";
//        }
        return $elem;
    }

    public function api_addViolation(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $remark=$request["remark"];
        $point=$request["point"];
        $usertype=$request["usertype"];
        if(empty($idlist)){
            $this->output_commonerror('用户不能为空');
            return;
        }
        if(empty($remark)){
            $this->output_commonerror('描述不能为空');
            return;
        }
        if(empty($point)){
            $this->output_commonerror('计分不能为空');
            return;
        }
        $violation=M('violation');
        $register=M('register');
        $card=M('card');
        if($usertype==0){
            foreach ($idlist as $id){
                $registerdata=$register->where('id='.$id)->find();
                if(empty($registerdata)){
                    continue;
                }
                $data['userid']=$id;
                $data['account']=$registerdata['account'];
                $data['name']=$registerdata['name'];
                $data['remark']=$remark;
                $data['point']=$point;
                $data['operatorid']=$registerdata['operatorid'];
                $data['propertyid']=$registerdata['propertyid'];
                $data['createtime']=time();
                $violation->add($data);
            }
        }else{
            foreach ($idlist as $id){
                $carddata=$card->where('id='.$id)->find();
                if(empty($carddata)){
                    continue;
                }
                $data['carduserid']=$id;
                $data['cardnumber']=$carddata['cardnumber'];
                $data['name']=$carddata['name'];
                $data['remark']=$remark;
                $data['point']=$point;
                $data['operatorid']=$carddata['operatorid'];
                $data['propertyid']=$carddata['propertyid'];
                $data['createtime']=time();
                $violation->add($data);
            }
        }
        $this->output_data('');
        return;
    }

    public function api_setDishonestflag(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $flag=$request["flag"];
        $usertype=$request["usertype"];
        if(empty($idlist)){
            $this->output_commonerror('用户不能为空');
            return;
        }
        $register=M('register');
        $card=M('card');
        if($usertype==0){
            foreach ($idlist as $id){
                $newdata['dishonestflag']=$flag;
                $newdata['updatetime']=time();
                $register->where('id='.$id)->save($newdata);
            }
        }else{
            foreach ($idlist as $id){
                $newdata['dishonestflag']=$flag;
                $newdata['updatetime']=time();
                $card->where('id='.$id)->save($newdata);
            }
        }
        $this->output_data('');
        return;
    }

    public function api_setUserflag(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $flag=$request["flag"];
        if(empty($idlist)){
            $this->output_commonerror('用户不能为空');
            return;
        }
        $user=M('user');
        foreach ($idlist as $id){
            $newdata['flag']=$flag;
            $user->where('userid='.$id)->save($newdata);
        }
        $this->output_data('');
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $account=$request["account"];
        $name=$request["name"];
        $violationpwd=$request["violationpwd"];
        $violationpwd1=$request["violationpwd1"];
        $operatorid=$request["operatorid"];
        if(empty($id)){
            $this->output_commonerror('用户ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('用户账号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $violation=M('violation');
        $result=$violation->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('用户ID不存在');
            return;
        }
        $violation=M('violation');
        $result=$violation->where("account='".$account."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('用户账号已存在');
            return;
        }

        if(session("roleid")==2){
            $operatorid=session("id");
        }

        $violation=M('violation');
        $data['account']=$account;
        $data['name']=$name;
        if(!empty($violationpwd)){
            $data['violationpwd']=$violationpwd;
            $data['violationpwd1']=$violationpwd1;
        }
        $data['operatorid']=$operatorid;
        $violation->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $violation=M('violation');
        foreach ($idlist as $id){
            $violation->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}