<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class ChargelogMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(33));
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $chargelog=M('chargelog');
        $result=$chargelog->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("chargelog",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $username=$request["username"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $chargelog=M('chargelog');
        $res['total']=$chargelog->count("id");
        $result=$chargelog->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($element){
        $createtime = $element["createtime"];
        if (!empty($createtime)) {
            $element["createtime"] = date('Y-m-d H:i', $createtime);
        }
        $starttime = $element["starttime"];
        if (!empty($starttime)) {
            $element["starttime"] = date('Y-m-d H:i', $starttime);
        }
        $endtime = $element["endtime"];
        if (!empty($endtime)) {
            $element["endtime"] = date('Y-m-d H:i', $endtime);
        }
        $register=M('register');
        $registerdata=$register->where("id=".$element["userid"])->find();
        $element["name"] = $registerdata['name'];
        $element["account"] = $registerdata['account'];
        $element["cardnum"] = $registerdata['cardnum'];

        $station=M('station');
        $pile=M('pile');
        $piledata=$pile->where("id=".$element["pileid"])->find();
        if(!empty($piledata)){
            $stationdata=$station->where("stationid=".$piledata["stationid"])->find();
            $element["stationname"] = $stationdata['name'];
            $element["pilecode"] = $piledata['code'];
        }

        $element['statusname']=CommonDao::getChargeStatusName($element['status']);
        $element['startchargemodename']=CommonDao::getStartchargemodeName($element['startchargemode']);
        $element['refundstatusname']=CommonDao::getRefundstatusName($element['refundstatus']);
        $element['chargestrategyname']=CommonDao::getChargestrategyName($element['chargestrategy']);
        $element['acdcname']=CommonDao::getPileAcdcName($element['acdc']);

        return $element;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $name=$request["name"];
        $openid=$request["openid"];
        $cardnum=$request["cardnum"];
        if(empty($account)){
            $this->output_commonerror('手机号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $chargelog=M('chargelog');
        $result=$chargelog->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $result=$chargelog->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $chargelog=M('chargelog');
        $data['account']=$account;
        $data['name']=$name;
        $data['openid']=$openid;
        $data['cardnum']=$cardnum;
        $time=time();
        $data['createtime']=$time;
        $data['updatetime']=$time;
        $chargelog->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $account=$request["account"];
        $name=$request["name"];
        $openid=$request["openid"];
        $cardnum=$request["cardnum"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('手机号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $chargelog=M('chargelog');
        $result=$chargelog->where("account='".$account."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $result=$chargelog->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $chargelog=M('chargelog');
        $data['account']=$account;
        $data['name']=$name;
        $data['openid']=$openid;
        $data['cardnum']=$cardnum;
        $time=time();
        $data['updatetime']=$time;
        $chargelog->where("id=".$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $chargelog=M('chargelog');
        foreach ($idlist as $id){
            $chargelog->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}