<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class WoXSListMgrController extends CommonController {
    public function index(){
        if(I('wkotype')==1){
            $this->assign('privilege',$this->getPrivilege(26));
        }else{
            $this->assign('privilege',$this->getPrivilege(28));
        }
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->display();
    }

    public function add(){
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->assign("stationList",CommonDao::getAllStation());
        $this->assign("statusList",CommonDao::getAllWoStatus());
        $this->assign("tasktypeList",CommonDao::getAllWoTasktype());
        $this->display();
    }

    public function update(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $wo=M('wo');
        $result=$wo->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->getWoInfo($result);
        $result['history']=str_replace("|","<br>",$result['history']);
        $this->assign("wo",$result);
//        $this->assign("stationList",CommonDao::getAllStation());
        $this->assign("statusList",CommonDao::getAllWoStatus());
        $this->assign("tasktypeList",CommonDao::getAllWoTasktype());
        $this->assign("wkotypename",CommonDao::getWotypeName($result['type']));

        $cooperation=M('cooperation');
        $cooperationdata=$cooperation->where("woid=".$woid)->select();
        $cooperations="";
        if(!empty($cooperationdata)){
            $user=M('user');
            for($i=0;$i<count($cooperationdata);$i++){
                $userinfo=$user->where("userid=".$cooperationdata[$i]['userid'])->find();
                if(!empty($userinfo)){
                    $cooperations=$cooperations.$userinfo['name'].", ";
                }
            }
        }
        $this->assign("cooperations",$cooperations);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $wkotype=$request["wkotype"];
//        $woname=$request["woname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if(empty($wkotype)){
            $this->output_commonerror('wkotype不能为空');
            return;
        }
        $wo=M('wo');
        $res['total']=$wo->where("type=".$wkotype)->count("woid");
//        $cond['roleid']=2;
        $result=$wo->where("type=".$wkotype)->order('woid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            $station=M('station');
            $user=M('user');
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->getWoInfo($result[$i]);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function getWoInfo($wodbinfo){
        $station=M('station');
        $user=M('user');
        $stationid=$wodbinfo['stationid'];
        if(!empty($stationid)){
            $stationdata=$station->where("stationid=".$stationid)->find();
            $wodbinfo['stationname']=$stationdata['name'];
        }
        $create_userid=$wodbinfo['create_userid'];
        if(!empty($create_userid)){
            $createuserdata=$user->where("userid=".$create_userid)->find();
            $wodbinfo['create_username']=$createuserdata['name'];
        }
        $accept_userid=$wodbinfo['accept_userid'];
        if(!empty($accept_userid)){
            $acceptuserdata=$user->where("userid=".$accept_userid)->find();
            $wodbinfo['accept_username']=$acceptuserdata['name'];
        }
        $wodbinfo['statusname']=CommonDao::getWoStatusName($wodbinfo['status']);
        $wodbinfo['tasktypename']=CommonDao::getWoTasktypeName($wodbinfo['tasktype']);

        $createtime=$wodbinfo['createtime'];
        if(!empty($createtime)){
            $wodbinfo['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $starttime=$wodbinfo['starttime'];
        if(!empty($starttime)){
            $wodbinfo['starttime']=date('Y-m-d H:i:s',$starttime);
        }
        $endtime=$wodbinfo['endtime'];
        if(!empty($endtime)){
            $wodbinfo['endtime']=date('Y-m-d H:i:s',$endtime);
        }
        return $wodbinfo;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $stationid=$request["stationid"];
        $status=$request["status"];
        $tasktype=$request["tasktype"];
        $starttime=$request["starttime"];
        $endtime=$request["endtime"];
        $reason=$request["reason"];
        $remarks=$request["remarks"];
        $wkotype=$request["wkotype"];
        $handleorgid=$request["handleorgid"];
//        $sex=$request["sex"];
        if(empty($stationid)){
            $this->output_commonerror('电站不能为空');
            return;
        }
//        if(empty($status)){
//            $this->output_commonerror('状态不能为空');
//            return;
//        }
        if(empty($tasktype)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($wkotype)){
            $this->output_commonerror('wkotype不能为空');
            return;
        }

        $wo=M('wo');
        $data['stationid']=$stationid;
        $data['status']=$status;
        $data['tasktype']=$tasktype;
        $data['type']=$wkotype;
        $data['create_userid']=session('userid');
        $data['starttime']=strtotime($starttime);
        $data['endtime']=strtotime($endtime);
        $data['reason']=$reason;
        $data['remarks']=$remarks;
        $data['handleorgid']=$handleorgid;
        $time=date('Y-m-d h:i:s',time());
        $data['history']=$time." ".session('account')."创建工单"."|";
        $data['createtime']=time();
        $wo->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woid=$request["woid"];
//        $stationid=$request["stationid"];
        $status=$request["status"];
        $tasktype=$request["tasktype"];
        $starttime=$request["starttime"];
        $endtime=$request["endtime"];
        $reason=$request["reason"];
        $remarks=$request["remarks"];
        $handleorgid=$request["handleorgid"];
        if(empty($woid)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        $wo=M('wo');
        $result=$wo->where("woid=".$woid)->find();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }

        $wo=M('wo');
//        $data['stationid']=$stationid;
        $data['status']=$status;
        $data['tasktype']=$tasktype;
//        $data['create_userid']=session('userid');
        $data['starttime']=strtotime($starttime);
        $data['endtime']=strtotime($endtime);
        $data['reason']=$reason;
        $data['remarks']=$remarks;
        $data['handleorgid']=$handleorgid;
        $time=date('Y-m-d H:i:s',time());
        $data['history']=$result['history'].$time." ".session('account')."修改工单"."|";
//        $data['createtime']=time();
        $wo->where('woid='.$woid)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $ids=$request["ids"];
        if(empty($ids)){
            $this->output_data();
            return;
        }
        $wo=M('wo');
        foreach ($ids as $id){
            $wo->where('woid='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}