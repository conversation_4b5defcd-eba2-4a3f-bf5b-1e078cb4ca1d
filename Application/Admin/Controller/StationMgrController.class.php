<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class StationMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(30));
        $this->display();
    }

    public function add(){
//        $this->assign("lat",32.822595);
//        $this->assign("lng",119.755946);
        $partnerList=CommonDao::getAllPartnerList();
        $this->assign("partnerList",$partnerList);
        $this->display();
    }

    public function update(){
        $stationid=I('stationid');
        if(empty($stationid)){
            return;
        }
        $station=M('station');
        $result=$station->where("stationid='".$stationid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("station",$result);
        $partnerList=CommonDao::getAllPartnerList();
        $this->assign("partnerList",$partnerList);

        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $stationname=$request["stationname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $station=M('station');
        $org=M('org');
        $res['total']=$station->count("stationid");
//        $cond['status']=1;
        $result=$station->order('stationid desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($info){
        $createtime=$info['createtime'];
        if(!empty($createtime)){
            $info['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $partneruserid=$info['partneruserid'];
        if(!empty($partneruserid)){
            $user=M('user');
            $partneruser=$user->where("userid=".$partneruserid)->find();
            $info['partner']=$partneruser['name'];
        }
        $pile=M('pile');
        $info['pileCount']=$pile->where("stationid=".$info['stationid'])->count();
        return $info;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $rate_e=$request["rate_e"];
        $rate_s=$request["rate_s"];
        $rate_p=$request["rate_p"];
        $businesshour=$request["businesshour"];
        $customerphone=$request["customerphone"];
        $partneruserid=$request["partneruserid"];
        $address=$request["address"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($businesshour)){
            $this->output_commonerror('营业时间不能为空');
            return;
        }
        if(empty($customerphone)){
            $this->output_commonerror('客服电话不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
        if(empty($lat)||empty($lng)){
            $this->output_commonerror('地理位置不能为空');
            return;
        }
        $station=M('station');
        $result=$station->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('编码已存在');
            return;
        }
        $station=M('station');
        $result=$station->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $station=M('station');
        $data['code']=$code;
        $data['name']=$name;
        $data['rate_e']=$rate_e;
        $data['rate_s']=$rate_s;
        $data['rate_p']=$rate_p;
        $data['businesshour']=$businesshour;
        $data['customerphone']=$customerphone;
        $data['partneruserid']=$partneruserid;
        $data['address']=$address;
        $data['lat']=$lat;
        $data['lng']=$lng;
        $data['createtime']=time();
        $station->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $stationid=$request["stationid"];
        $code=$request["code"];
        $name=$request["name"];
        $rate_e=$request["rate_e"];
        $rate_s=$request["rate_s"];
        $rate_p=$request["rate_p"];
        $businesshour=$request["businesshour"];
        $customerphone=$request["customerphone"];
        $partneruserid=$request["partneruserid"];
        $address=$request["address"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        if(empty($stationid)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($businesshour)){
            $this->output_commonerror('营业时间不能为空');
            return;
        }
        if(empty($customerphone)){
            $this->output_commonerror('客服电话不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
        if(empty($lat)||empty($lng)){
            $this->output_commonerror('地理位置不能为空');
            return;
        }
        $station=M('station');
        $result=$station->where("code='".$code."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('编码已存在');
            return;
        }
        $station=M('station');
        $result=$station->where("name='".$name."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $station=M('station');
        $data['code']=$code;
        $data['name']=$name;
        $data['rate_e']=$rate_e;
        $data['rate_s']=$rate_s;
        $data['rate_p']=$rate_p;
        $data['businesshour']=$businesshour;
        $data['customerphone']=$customerphone;
        $data['partneruserid']=$partneruserid;
        $data['address']=$address;
        $data['lat']=$lat;
        $data['lng']=$lng;
        $station->where("stationid=".$stationid)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $station=M('station');
        $pile=M('pile');
        foreach ($idlist as $id){
            $piledata=$pile->where('stationid='.$id)->select();
            if(!empty($piledata)){
                $this->output_error(null,"1001","删除失败，请先删除该站下的所有充电桩");
                return;
            }
        }
        foreach ($idlist as $id){
            $station->where('stationid='.$id)->delete();
        }
        $this->output_data();
        return;
    }

    public function findposition()
    {
        $this->assign("lat",$_REQUEST['lat']);
        $this->assign("lng",$_REQUEST['lng']);
        $this->display();
    }
}