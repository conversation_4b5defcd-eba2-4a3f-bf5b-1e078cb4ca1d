<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class LayoutMgrController extends CommonController {
    public function index(){
        $this->assign("pagetitle","布局模板列表");
        $this->display();
    }

    public function add(){
        $this->assign("pagetitle","新增布局模板");

        $this->assign("default_width","1280");
        $this->assign("default_height","800");
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $layout=M('layout');
        $result=$layout->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
        $this->assign("pagetitle","查看布局模板");
        $this->assign("default_width","1280");
        $this->assign("default_height","800");
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $layout=M('layout');
        $res['total']=$layout->count("id");
        $result=$layout->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $name=$request["name"];
        $content=$request["content"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($content)){
            $this->output_commonerror('布局不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $layout=M('layout');
        $result=$layout->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $layout=M('layout');
        $data['name']=$name;
        $data['content']=$content;
        $data['createuserid']=session('userid');
        $data['createtime']=time();
        $layout->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $code=$request["code"];
        $name=$request["name"];
        $address=$request["address"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($id)){
            $this->output_commonerror('设备ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $layout=M('layout');
        $result=$layout->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }
        $layout=M('layout');
        $result=$layout->where("code='".$code."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $layout=M('layout');
        $result=$layout->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $layout=M('layout');
        $data['code']=$code;
        $data['name']=$name;
        $data['address']=$address;
//        $data['lat']=$lat;
//        $data['lng']=$lng;
        $layout->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $layout=M('layout');
        $play=M('play');
        foreach ($idlist as $id){
            $layoutdata=$layout->where('id='.$id)->find();
            if(empty($layoutdata)){
                continue;
            }
            $playdata=$play->where('layoutid='.$id)->select();
            if(!empty($playdata)){
                $this->output_commonerror("布局模板[".$layoutdata['name']."]正在使用，请先删除对应播放实例：".$playdata[0]['name']);
                return;
            }
        }

        foreach ($idlist as $id){
            $layout->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}