<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class CashMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function detail(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $cash=M('cash');
        $result=$cash->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->transinfo($result);
        $this->assign("cash",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $cashname=$request["cashname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $roleid=session('roleid');
        if(empty($roleid)){
            return;
        }
        $cash=M('cash');
        $cond=array();
        if($roleid!=1) {
            $cond['operatorid'] = session('userid');
        }
        $res['total']=$cash->where($cond)->count("id");
        $result=$cash->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){

            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->transinfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function transinfo($element){
        $register=M('register');
        $result1=$register->where("id=".$element['createuserid'])->find();
        if(!empty($result1)){
            $element['createaccount']=$result1['account'];
        }
        $createtime = $element["createtime"];
        if (!empty($createtime)) {
            $element["createtime"] = date('Y-m-d H:i', $createtime);
        }
        $updatetime = $element["updatetime"];
        if (!empty($updatetime)) {
            $element["updatetime"] = date('Y-m-d H:i', $updatetime);
        }
        $element["statusname"] = CommonDao::getCashStatusName($element["status"]);
        $element["cashtypename"] = CommonDao::getCashtypeName($element["cashtype"]);
        return $element;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $cash=M('cash');
        foreach ($idlist as $id){
            $cash->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }

    public function api_confirm(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_commonerror("id不存在");
            return;
        }
        $cash=M('cash');
        $cashdata=$cash->where("id=".$id)->find();
        if(empty($cashdata)){
            $this->output_commonerror("申请信息不存在");
            return;
        }
        if($cashdata['status']!=0){
            $this->output_commonerror("申请信息状态错误");
            return;
        }
        $cashtype=$cashdata['cashtype'];

        $userid=$cashdata['createuserid'];
        $register=M('register');
        $registerdata=$register->where("id=".$userid)->find();
        if(empty($registerdata)){
            $this->output_commonerror("用户不存在");
            return;
        }
        $openid=$registerdata['openid'];
        if(empty($openid)){
            $this->output_commonerror("用户openid不存在");
            return;
        }
        $config=M('config');
        $money=$cashdata['money'];
        $max_cash_money=100;
        $configdata=$config->where("configkey='max_cash_money'")->find();
        if(!empty($configdata)){
            $max_cash_money=$configdata['configvalue'];
        }
        if($money>$max_cash_money){
            $this->output_commonerror("单笔不高于".$max_cash_money."元");
            return;
        }

        if($cashtype == 1){
            $out_trade_no=$cashdata['outtradeno'];
            $createuserid=$cashdata['createuserid'];
            $costpoint=$cashdata['point'];

            $res=$this->transfer($out_trade_no,$openid,$money);
    //        $res1=base64_encode(json_encode($res));
            \Think\Log::record("转账结果 userid=".$createuserid." openid=".$openid." money=".$money." ".json_encode($res),"INFO");

            if(empty($res)||$res["result_code"]!="SUCCESS"){
                if($res["msg"]=="PARAM_ERROR"&&!empty($maintenanceinfos)){
                    $newdata['remark']=$res["result_code"]." ".$res["msg"];
                }
                $newdata['status']=2;
                $newdata['updatetime']=time();
                $cash->where("id=".$id)->save($newdata);
                CommonDao::addPoint($createuserid,$costpoint);
    //            usleep(500*1000);

                $this->output_commonerror("转账失败");
                return;
            }else{
                $newdata['status']=1;
                $newdata['updatetime']=time();
                $cash->where("id=".$id)->save($newdata);
                //error_log($model4->_sql());
    //            $successNum++;
    //            $zxuserdata['balance']=0;
    //            $zxuser->where("id=".$zxuserid)->save($zxuserdata);
    //            usleep(500*1000);
                $this->output_data("转账成功");
                return;
            }
        }else{
            $data['status']=1;
            $data['updatetime']=time();
            $cash->where('id='.$id)->save($data);
            $this->output_data("");
            return;
        }
    }

    private function transfer($partner_trade_no,$openid,$money){
        if($money==0){
            return;
        }
        //支付信息
        $webdata = array(
            'mch_appid' => 'wxa5c68576571ccada',//商户账号appid
            'mchid'     => '1573079111',//商户号
            'nonce_str' => md5(time()),//随机字符串
            'partner_trade_no'=> $partner_trade_no, //商户订单号，需要唯一
            'openid' => $openid,//转账用户的openid
            'check_name'=> 'NO_CHECK', //OPTION_CHECK不强制校验真实姓名, FORCE_CHECK：强制 NO_CHECK：
            'amount' => $money*100, //付款金额单位为分
            'desc'   => '积分提现',//企业付款描述信息
            'spbill_create_ip' =>$this->get_client_ip(),//获取IP
        );
        foreach ($webdata as $k => $v) {
            $tarr[] =$k.'='.$v;
        }
        sort($tarr);
        $sign = implode($tarr, '&');
        $sign .= '&key=oaCYR41n5AuvsK7bgRPHA4Ft9KG0hpP6';
        $webdata['sign']=strtoupper(md5($sign));
        $wget = $this->ArrToXml($webdata);//数组转XML
        $pay_url = 'https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers';//api地址
        $res = $this->postData($pay_url,$wget);//发送数据
        if(!$res){
            return array('status'=>1, 'msg'=>"Can't connect the server" );
        }
        $content = simplexml_load_string($res, 'SimpleXMLElement', LIBXML_NOCDATA);
        if(strval($content->return_code) == 'FAIL'){
            return array('status'=>1, 'msg'=>strval($content->return_msg));
        }
        if(strval($content->result_code) == 'FAIL'){
            return array('status'=>1, 'msg'=>strval($content->err_code),':'.strval($content->err_code_des));
        }
        $rdata = array(
            'mch_appid'        => strval($content->mch_appid),
            'mchid'            => strval($content->mchid),
            'device_info'      => strval($content->device_info),
            'nonce_str'        => strval($content->nonce_str),
            'result_code'      => strval($content->result_code),
            'partner_trade_no' => strval($content->partner_trade_no),
            'payment_no'       => strval($content->payment_no),
            'payment_time'     => strval($content->payment_time),
        );
        return $rdata;
    }

    //数组转XML
    private function ArrToXml($arr)
    {
        if(!is_array($arr) || count($arr) == 0) return '';
        $xml = "<xml>";
        foreach ($arr as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        return $xml;
    }

    //发送数据
    private function postData($url,$postfields){
        $ch = curl_init();
        $params[CURLOPT_URL] = $url;    //请求url地址
        $params[CURLOPT_HEADER] = false; //是否返回响应头信息
        $params[CURLOPT_RETURNTRANSFER] = true; //是否将结果返回
        $params[CURLOPT_FOLLOWLOCATION] = true; //是否重定向
        $params[CURLOPT_POST] = true;
        $params[CURLOPT_POSTFIELDS] = $postfields;
        $params[CURLOPT_SSL_VERIFYPEER] = false;
        $params[CURLOPT_SSL_VERIFYHOST] = false;
        //以下是证书相关代码
        $params[CURLOPT_SSLCERTTYPE] = 'PEM';
        $params[CURLOPT_SSLCERT] = getcwd().'/Public/cert/apiclient_cert.pem';//绝对路径
        $params[CURLOPT_SSLKEYTYPE] = 'PEM';
        $params[CURLOPT_SSLKEY] = getcwd().'/Public/cert/apiclient_key.pem';//绝对路径
        curl_setopt_array($ch, $params); //传入curl参数
        $content = curl_exec($ch); //执行
        curl_close($ch); //关闭连接
        return $content;
    }

    private function get_client_ip(){
        if ($_SERVER['REMOTE_ADDR']) {
            $cip = $_SERVER['REMOTE_ADDR'];
        } elseif (getenv("REMOTE_ADDR")) {
            $cip = getenv("REMOTE_ADDR");
        } elseif (getenv("HTTP_CLIENT_IP")) {
            $cip = getenv("HTTP_CLIENT_IP");
        } else {
            $cip = "unknown";
        }
        return $cip;
    }
}