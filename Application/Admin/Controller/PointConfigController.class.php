<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;

class PointConfigController extends CommonController {
    public function index(){
        if(session('roleid')!=2){
            return;
        }
        $userid=session('userid');
        $rubbishtypepoint=M('rubbishtypepoint');
        $rubbishtypepointdata=$rubbishtypepoint->order("rubbishtype asc")->select();
        if(!empty($rubbishtypepointdata)){
            $operatorpoint=M('operatorpoint');
            $operatorpointdata=$operatorpoint->where("userid=".$userid)->select();
            foreach ($rubbishtypepointdata as $rubbishtypepointdata_elem){
                if(!$this->listcontain($rubbishtypepointdata_elem,$operatorpointdata)){
                    $operatorpoint_newdata['userid']=$userid;
                    $operatorpoint_newdata['rubbishtype']=$rubbishtypepointdata_elem['rubbishtype'];
                    $operatorpoint_newdata['point']=0;
                    $operatorpoint->add($operatorpoint_newdata);
                }
            }
        }
        $this->assign("pointrulelist",CommonDao::getAllPointrule());
        $this->display();
    }

    private function listcontain($rubbishtypepointdata_elem,$operatorpointdata){
        if(empty($operatorpointdata)){
            return false;
        }
        foreach ($operatorpointdata as $operatorpointdata_elem){
            if($operatorpointdata_elem['rubbishtype']==$rubbishtypepointdata_elem['rubbishtype']){
                return true;
            }
        }
        return false;
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $operatorpoint=M('operatorpoint');
        $result=$operatorpoint->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=1000;
        }
        $userid=session('userid');

        $cond=array();
        $cond['userid']=$userid;

        $operatorpoint=M('operatorpoint');
        $res['total']=$operatorpoint->where($cond)->count("id");
        $result=$operatorpoint->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $rubbishtype=$result['rubbishtype'];
        if(!empty($rubbishtype)){
            $rubbishtypepoint=M('rubbishtypepoint');
            $operatordata=$rubbishtypepoint->where("rubbishtype='".$rubbishtype."'")->find();
            if(!empty($operatordata)){
                $result['rubbishtypename']=$operatordata['name'];
            }
        }
        $result['pointrulename']=CommonDao::getPointrulename($result['pointrule']);
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $name=$request["name"];
        $price=$request["price"];
        $content=$request["content"];
        $mainpic=$request["mainpic"];
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($price)){
            $this->output_commonerror('单价不能为空');
            return;
        }
        if(empty($mainpic)){
            $this->output_commonerror('主图不能为空');
            return;
        }
        $operatorpoint=M('operatorpoint');
        $result=$operatorpoint->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        if(session('roleid')!=2){
            $this->output_commonerror('仅允许运营商用户添加');
            return;
        }
        $operatorid=session('userid');

        $operatorpoint=M('operatorpoint');
        $data['price']=$price;
        $data['name']=$name;
        $data['content']=$content;
        $data['mainpic']=$mainpic;
        $data['operatorid']=$operatorid;
        $data['createtime']=time();
        $data['updatetime']=time();
        $operatorpoint->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $point=$request["point"];
        $pointrule=$request["pointrule"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        $operatorpoint=M('operatorpoint');
        $result=$operatorpoint->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }

        $operatorpoint=M('operatorpoint');
        $data['point']=$point;
        $data['pointrule']=$pointrule;
        $operatorpoint->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $operatorpoint=M('operatorpoint');
        foreach ($idlist as $id){
            $operatorpoint->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}