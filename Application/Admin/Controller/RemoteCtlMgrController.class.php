<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class RemoteCtlMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(12));
        $this->display();
    }

    public function add(){

        $this->assign("addressTypeList",CommonDao::getAllAddressType());

        $this->assign("statusList",CommonDao::getAllStatus());

        $allcitys=CommonDao::getAllCitys();
        $this->assign("cityList",$allcitys);

        $allmaintenances=CommonDao::getAllMaintenances();
        $this->assign("maintenanceList",$allmaintenances);
//        $this->assign("lat",32.822595);
//        $this->assign("lng",119.755946);

        $this->display();
    }

    public function update(){
        $stationid=I('stationid');
        if(empty($stationid)){
            return;
        }
        $station=M('station');
        $result=$station->where("stationid='".$stationid."'")->find();
        if(empty($result)){
            return;
        }
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $onlinetime=$result['onlinetime'];
        if(!empty($onlinetime)){
            $result['onlinetime']=date('Y-m-d H:i:s',$onlinetime);
        }
        $this->assign("station",$result);

        $this->assign("addressTypeList",CommonDao::getAllAddressType());

        $this->assign("statusList",CommonDao::getAllStatus());

        $allcitys=CommonDao::getAllCitys();
        $this->assign("cityList",$allcitys);

        $allmaintenances=CommonDao::getAllMaintenances();
        $this->assign("maintenanceList",$allmaintenances);

        $this->display();
    }

    public function api_getStationlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $stationname=$request["stationname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $station=M('station');
        $org=M('org');
        $res['total']=$station->count("stationid");
//        $cond['status']=1;
        $result=$station->order('stationid desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]['statusname']=CommonDao::getStatusName($result[$i]['status']);
                $result[$i]['addressTypeName']=CommonDao::getAddressTypeName($result[$i]['addresstype']);
                $orgid=$result[$i]['orgid'];
                if(!empty($orgid)){
                    $orginfo=$org->where("id=".$orgid)->find();
                    if(!empty($orginfo)){
                        $result[$i]['orgname']=$orginfo['name'];
                    }
                }
                $createtime=$result[$i]['createtime'];
                if(!empty($createtime)){
                    $result[$i]['createtime']=date('Y-m-d H:i:s',$createtime);
                }
                $onlinetime=$result[$i]['onlinetime'];
                if(!empty($onlinetime)){
                    $result[$i]['onlinetime']=date('Y-m-d H:i:s',$onlinetime);
                }
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $status=$request["status"];
        $selected_orgId=$request["selected_orgId"];
        $onlinetime=$request["onlinetime"];
        $address=$request["address"];
        $addressType=$request["addressType"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($selected_orgId)){
            $this->output_commonerror('所属组织结构不能为空');
            return;
        }
        if(empty($onlinetime)){
            $this->output_commonerror('投运时间不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
        if(empty($addressType)){
            $this->output_commonerror('位置类型不能为空');
            return;
        }
        if(empty($lat)||empty($lng)){
            $this->output_commonerror('地理位置不能为空');
            return;
        }
        $station=M('station');
        $result=$station->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $station=M('station');
        $result=$station->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $station=M('station');
        $data['code']=$code;
        $data['name']=$name;
        $data['status']=$status;
        $data['orgid']=$selected_orgId;
        $data['onlinetime']=strtotime($onlinetime);
        $data['address']=$address;
        $data['addressType']=$addressType;
        $data['lat']=$lat;
        $data['lng']=$lng;
        $data['createtime']=time();
        $station->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $stationid=$request["stationid"];
        $code=$request["code"];
        $name=$request["name"];
        $status=$request["status"];
        $selected_orgId=$request["selected_orgId"];
        $onlinetime=$request["onlinetime"];
        $address=$request["address"];
        $addressType=$request["addressType"];
        $lat=$request["lat"];
        $lng=$request["lng"];
        if(empty($stationid)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($selected_orgId)){
            $this->output_commonerror('所属组织结构不能为空');
            return;
        }
        if(empty($onlinetime)){
            $this->output_commonerror('投运时间不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
        if(empty($addressType)){
            $this->output_commonerror('位置类型不能为空');
            return;
        }
        if(empty($lat)||empty($lng)){
            $this->output_commonerror('地理位置不能为空');
            return;
        }
        $station=M('station');
        $result=$station->where("code='".$code."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $station=M('station');
        $result=$station->where("name='".$name."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $station=M('station');
        $data['code']=$code;
        $data['name']=$name;
        $data['status']=$status;
        $data['orgid']=$selected_orgId;
        $data['onlinetime']=strtotime($onlinetime);
        $data['address']=$address;
        $data['addressType']=$addressType;
        $data['lat']=$lat;
        $data['lng']=$lng;
        $data['createtime']=time();
        $station->where("stationid=".$stationid)->save($data);
        $this->output_data();
        return;
    }

    public function api_update1(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $stationid=$request["stationid"];
        $stationcode=$request["stationcode"];
        $stationname=$request["stationname"];
        if(empty($stationid)){
            $this->output_commonerror('设备ID不能为空');
            return;
        }
        if(empty($stationcode)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($stationname)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        $station=M('station');
        $result=$station->where("stationid=".$stationid)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }
        $station=M('station');
        $result=$station->where("stationcode='".$stationcode."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $station=M('station');
        $result=$station->where("stationname='".$stationname."' and stationid!=".$stationid)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $station=M('station');
        $data['stationcode']=$stationcode;
        $data['stationname']=$stationname;
        $time=date('Y-m-d h:i:s',time());
        $data['updatetime']=$time;
        $station->where('stationid='.$stationid)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $stationidlist=$request["stationidlist"];
        if(empty($stationidlist)){
            $this->output_data();
            return;
        }
        $station=M('station');
        foreach ($stationidlist as $stationid){
            $station->where('stationid='.$stationid)->delete();
        }
        $this->output_data();
        return;
    }

    public function findposition()
    {
        $this->assign("lat",$_REQUEST['lat']);
        $this->assign("lng",$_REQUEST['lng']);
        $this->display();
    }

    public function pile(){
        $this->assign('privilege',$this->getPrivilege(12));
        $stationid=I('stationid');
        if(empty($stationid)){
            return;
        }
        $station=M('station');
        $stationdata=$station->where("stationid=".$stationid)->find();
        $this->assign("stationid",$stationid);
        $this->assign("stationname",$stationdata['name']);
        $this->display();
    }

    public function api_getPilelist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $stationid=$request["stationid"];
        if(empty($stationid)){
            return;
        }
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $pile=M('pile');
        $res['total']=$pile->where("stationid=".$stationid)->count("pileid");
//        $cond['status']=1;
        $result=$pile->where("stationid=".$stationid)->order('pileid desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makePileInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        echo json_encode($res);die;
        return;
    }

    private function makePileInfo($elem){
        if(empty($elem)){
            return $elem;
        }

        $elem['typename']=CommonDao::getPileTypeName($elem['type']);
        $elem['statusname']=CommonDao::getPileStatusName($elem['status']);
        $elem['acdcname']=CommonDao::getPileAcdcName($elem['acdc']);
        $manufacturerInfo=CommonDao::getManufacturerInfo($elem['manufacturerid']);
        if(!empty($manufacturerInfo)){
            $elem['manufacturername']=$manufacturerInfo['name'];
        }
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $onlinetime=$elem['onlinetime'];
        if(!empty($onlinetime)){
            $elem['onlinetime']=date('Y-m-d H:i:s',$onlinetime);
        }
        return $elem;
    }
}