<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class RegisterMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(39));
        $this->assign("search_visible",1);
        $this->assign("cansetrole",0);
        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $this->assign("search_propertyid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else if(session('roleid')==2){
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",1);
            $cond['operatoruserid']=session('userid');
            $propertylist=$user->where($cond)->select();
            $this->assign("propertylist",$propertylist);
        }else{
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",0);
            $this->assign("cansetrole",1);
        }
        $this->assign("rolelist",CommonDao::getAllRegisterRole());
        $this->display();
    }

    public function add(){
        $this->assign("rolelist",CommonDao::getAllRegisterRole());
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $register=M('register');
        $result=$register->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("register",$result);
        $this->display();
    }

    public function point(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $register=M('register');
        $result=$register->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("register",$result);
        $this->assign("rolelist",CommonDao::getAllRegisterRole());
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $account=$request["account"];
//        $operatorid  =$request["operatorid"];
//        $propertyid  =$request["propertyid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $register=M('register');

        if(session('roleid')==1){
            $cond=array();
            if(!empty($account)){
                $cond['account']=array('like','%'.$account.'%');
            }
            $res['total']=$register->where($cond)->count("id");
            $result=$register->where($cond)->order('id desc')->limit($offset,$limit)->select();
        }else if(session('roleid')==2){
            $operatorid=session('userid');
            $cond="";
            if(!empty($account)){
                $cond=$cond." and account like '%".$account."%'";
            }
            $result_num=$register->query('select count(1) as num from think_register where id in( select userid from think_registerproperty where operatorid='.$operatorid.') '.$cond);
            $res['total']=$result_num[0]['num'];
            $result=$register->query('select * from think_register where id in( select userid from think_registerproperty where operatorid='.$operatorid.') '.$cond.' limit '.$offset.','.$limit);
        }else if(session('roleid')==3){
            $propertyid=session('userid');
            $cond="";
            if(!empty($account)){
                $cond=$cond." and account like '%".$account."%'";
            }
            $result_num=$register->query('select count(1) as num from think_register where id in( select userid from think_registerproperty where propertyid='.$propertyid.') '.$cond);
            $res['total']=$result_num[0]['num'];
            $result=$register->query('select * from think_register where id in( select userid from think_registerproperty where propertyid='.$propertyid.') '.$cond.' limit '.$offset.','.$limit);
        }else if(session('roleid')==6){
            $propertyid=session('propertyid');
//            $propertyid=session('userid');
            $cond="";
            if(!empty($account)){
                $cond=$cond." and account like '%".$account."%'";
            }
            $result_num=$register->query('select count(1) as num from think_register where id in( select userid from think_registerproperty where propertyid='.$propertyid.') '.$cond);
            $res['total']=$result_num[0]['num'];
            $result=$register->query('select * from think_register where id in( select userid from think_registerproperty where propertyid='.$propertyid.') '.$cond.' limit '.$offset.','.$limit);
        }

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($element){
        $createtime = $element["createtime"];
        if (!empty($createtime)) {
            $element["createtime"] = date('Y-m-d H:i', $createtime);
        }
        $element['dishonestflagname']=CommonDao::getDishonestflagname($element['dishonestflag']);

        if(session('roleid')==3) {
            $registerproperty = M('registerproperty');
            $registerpropertydata = $registerproperty->where('userid=' .$element['id'].' and propertyid='.session('userid'))->find();
            if(!empty($registerpropertydata)){
                $element['rolename']=CommonDao::getRegisterRolename($registerpropertydata['roleid']);
            }else{
                $element['rolename']=CommonDao::getRegisterRolename(0);
            }
        }

        return $element;
    }



    public function api_getpointlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $userid=$request["userid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }
        $cond['userid']=$userid;
        $registerproperty=M('registerproperty');
        $res['total']=$registerproperty->where($cond)->count("id");
        $result=$registerproperty->where($cond)->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makePointInfo($result[$i]);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makePointInfo($element){
        $element['rolename']=CommonDao::getRegisterRolename($element['roleid']);
        $carduserid = $element["carduserid"];
        if (!empty($carduserid)) {
            $card=M('card');
            $carddata=$card->where("id=".$carduserid)->find();
            if(!empty($carddata)){
                $element['cardnum']=$carddata['cardnumber'];
                $element['cardpoint']=$carddata['point'];
            }
        }

        $user=M('user');
        if(!empty($element['propertyid'])){
            $userdata=$user->where("userid=".$element['propertyid'])->find();
            if(!empty($userdata)){
                $element['propertyname']=$userdata['name'];
            }
        }
        if(!empty($element['operatorid'])){
            $userdata=$user->where("userid=".$element['operatorid'])->find();
            if(!empty($userdata)){
                $element['operatorname']=$userdata['name'];
            }
        }
        $element['cardmasterflagname']=CommonDao::getCardmasterflagname($element['cardmasterflag']);
        return $element;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $name=$request["name"];
        $openid=$request["openid"];
        $cardnum=$request["cardnum"];
        $point=$request["point"];
        $roleid=$request["roleid"];
        $fingerprint=$request["fingerprint"];
        $feature=$request["feature"];
        if(empty($account)){
            $this->output_commonerror('手机号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $register=M('register');
        $result=$register->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $result=$register->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $register=M('register');
        $data['account']=$account;
        $data['name']=$name;
        if(!empty($openid)) {
            $data['openid'] = $openid;
        }
        $data['cardnum']=$cardnum;
        $data['point']=$point;
        $data['roleid']=$roleid;
        $data['fingerprint']=$fingerprint;
        $data['feature']=$feature;
        $time=time();
        $data['createtime']=$time;
        $data['updatetime']=$time;
        $register->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $account=$request["account"];
        $name=$request["name"];
        $openid=$request["openid"];
        $cardnum=$request["cardnum"];
//        $point=$request["point"];
//        $roleid=$request["roleid"];
//        $fingerprint=$request["fingerprint"];
//        $feature=$request["feature"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('手机号不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        $register=M('register');
        $result=$register->where("account='".$account."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $result=$register->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('名称已存在');
            return;
        }

        $register=M('register');
        $data['account']=$account;
        $data['name']=$name;
        if(!empty($openid)){
            $data['openid']=$openid;
        }
        $data['cardnum']=$cardnum;
//        $data['point']=$point;
//        $data['roleid']=$roleid;
//        $data['fingerprint']=$fingerprint;
//        $data['feature']=$feature;
        $time=time();
        $data['updatetime']=$time;
        $register->where("id=".$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_modpoint(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $point=$request["point"];
        $roleid=$request["roleid"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }

        $registerproperty=M('registerproperty');
        $data['point']=$point;
        $data['roleid']=$roleid;
        $registerproperty->where("id=".$id)->save($data);
        $this->output_data('');
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $register=M('register');
        foreach ($idlist as $id){
            $register->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }

    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }

    public function api_setRole(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        $roleid=$request["roleid"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $propertyid=session('userid');
        $data['roleid']=$roleid;
        $registerproperty=M('registerproperty');
        foreach ($idlist as $id){
            $registerproperty->where('userid='.$id.' and propertyid='.$propertyid)->save($data);
        }
        $this->output_data('');
        return;
    }

    public function costPoint(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $point=$request["point"];
        $remark=$request["remark"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($point)){
            $this->output_error('','1','消费积分不能为空');
            return;
        }
        $registerproperty=M('registerproperty');
        $registerpropertydata=$registerproperty->where('id='.$id)->find();
        if(empty($registerpropertydata)){
            $this->output_commonerror('记录不存在');
            return;
        }
        $carduserid=$registerpropertydata['carduserid'];
        $pointdb=$registerpropertydata['point'];
        if(empty($pointdb)){
            $pointdb=0;
        }
        $newpoint=$pointdb-$point;
        $newdata['point']=$newpoint;
        $registerproperty->where('id='.$id)->save($newdata);

        $costpointlog=M('costpointlog');
        $costpointlogdata['userid']=$registerpropertydata['userid'];
        $costpointlogdata['costpoint']=$point;
        $costpointlogdata['remark']=$remark;
        $costpointlogdata['createtime']=time();
        $costpointlogdata['beforepoint']=$pointdb;
        $costpointlogdata['afterpoint']=$newpoint;
        $costpointlogdata['opruserid']=session('userid');
        $costpointlogdata['propertyid']=$registerpropertydata['propertyid'];
        $costpointlog->add($costpointlogdata);
        $this->output_data("");
        return;
    }
}