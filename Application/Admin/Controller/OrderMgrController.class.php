<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class OrderMgrController extends CommonController {
    public function index(){

        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else{
            $this->assign("search_operatorid_visible",0);
        }

        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $order=M('order');
        $result=$order->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("modelvo",$result);

        if(session('roleid')==2){
            $this->assign("change_able",1);
        }else{
            $this->assign("change_able",0);
        }
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $account=$request["account"];
        $name=$request["name"];
        $operatorid  =$request["operatorid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $cond['operatorid']=session('userid');
        }else if(session('roleid')==1){
            if(!empty($operatorid)){
                $cond['operatorid']=$operatorid;
            }
        }
        if(!empty($name)){
            $cond['goodsname']=array('like','%'.$name.'%');
        }
        if(!empty($account)){
            $cond['account']=array('like','%'.$account.'%');
        }

        $order=M('order');
        $res['total']=$order->where($cond)->count("id");
        $result=$order->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $updatetime=$result['updatetime'];
        if(!empty($updatetime)){
            $result['updatetime']=date('Y-m-d H:i:s',$updatetime);
        }
//        $registerid=$result['registerid'];
//        if(!empty($registerid)){
//            $register=M('register');
//            $registerdata=$register->where("id=".$registerid)->find();
//            if(!empty($registerdata)){
//                $result['account']=$registerdata['account'];
//            }
//        }
//        $goodsid=$result['goodsid'];
//        if(!empty($goodsid)){
//            $goods=M('goods');
//            $goodsdata=$goods->where("id=".$goodsid)->find();
//            if(!empty($goodsdata)){
//                $result['goodsname']=$goodsdata['name'];
//            }
//        }
        $result['statusname']=CommonDao::getStatusName($result['status']);

        $operatorid=$result['operatorid'];
        if(!empty($operatorid)){
            $user=M('user');
            $operatordata=$user->where("userid=".$operatorid)->find();
            if(!empty($operatordata)){
                $result['operatorname']=$operatordata['name'];
            }
        }
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $address=$request["address"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $order=M('order');
        $result=$order->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $order=M('order');
        $result=$order->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $order=M('order');
        $data['code']=$code;
        $data['name']=$name;
        $data['address']=$address;
//        $data['lat']=$lat;
//        $data['lng']=$lng;
        $data['createtime']=time();
        $order->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $status=$request["status"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($status)){
            $this->output_commonerror('状态不能为空');
            return;
        }
        $order=M('order');
        $result=$order->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }

        $order=M('order');
        $data['status']=$status;
        $data['updatetime']=time();
        $order->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $order=M('order');
        foreach ($idlist as $id){
            $order->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}