<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class RoleMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(36));
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $roleid=I('roleid');
        if(empty($roleid)){
            return;
        }
        $role=M('role');
        $result=$role->where("roleid='".$roleid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("user",$result);
        $this->assign("rolelist",CommonDao::getAllRole());
        $this->assign("citylist",CommonDao::getAllCitys());
        $this->assign("maintenancelist",CommonDao::getAllMaintenances());
        $this->assign("manufacturerist",CommonDao::getAllManufacturer());
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $rolename=$request["username"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $role=M('role');
        $res['total']=$role->count("roleid");
        $result=$role->order('roleid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return null;
        }
        $org=M('org');
        $elem['rolename']=CommonDao::getRoleName($elem['roleid']);
        $elem['manufacturername']=CommonDao::getManufacturerInfo($elem['manufacturerid']);

        $orgid=$elem['orgid'];
        if(!empty($orgid)){
            $orginfo=$org->where("id=".$orgid)->find();
            if(!empty($orginfo)){
                $elem['orgname']=$orginfo['name'];
            }
        }
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $elem;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $rolename=$request["rolename"];
        if(empty($rolename)){
            $this->output_commonerror('角色名称不能为空');
            return;
        }
        $role=M('role');
        $result=$role->where("rolename='".$rolename."'")->select();
        if(!empty($result)){
            $this->output_commonerror('角色名称已存在');
            return;
        }

        $role=M('role');
        $data['rolename']=$rolename;
        $role->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $roleid=$request["roleid"];
        $rolename=$request["rolename"];
        if(empty($roleid)){
            $this->output_commonerror('角色ID不能为空');
            return;
        }
        if(empty($rolename)){
            $this->output_commonerror('角色名称不能为空');
            return;
        }
        $role=M('role');
        $result=$role->where("roleid=".$roleid)->select();
        if(empty($result)){
            $this->output_commonerror('角色ID不存在');
            return;
        }
        $role=M('role');
        $result=$role->where("rolename='".$rolename."' and roleid!=".$roleid)->select();
        if(!empty($result)){
            $this->output_commonerror('角色账号已存在');
            return;
        }

        $role=M('role');
        $data['rolename']=$rolename;
        $role->where('roleid='.$roleid)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $roleidlist=$request["roleidlist"];
        if(empty($roleidlist)){
            $this->output_error(null,"1026","角色为空");
            return;
        }
        $role=M('role');
        foreach ($roleidlist as $roleid){
            $roledata=$role->where("roleid=".$roleid)->find();
            if(!empty($roledata)&&$roledata['builtin']==1){
                $this->output_error(null,"1028",$roledata['rolename']."角色为系统内置角色，不可删除");
                return;
            }
        }
        $user=M('user');
        foreach ($roleidlist as $roleid){
            $users=$user->where("roleid=".$roleid)->select();
            if(!empty($users)){
                $this->output_error(null,"1027","该角色已存在用户，请先删除用户");
                return;
            }
        }

        foreach ($roleidlist as $roleid){
            $role->where('roleid='.$roleid)->delete();
        }
        $this->output_data();
        return;
    }

    public function getPrivilegeTree(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $roleid=$request["roleid"];
        if(empty($roleid)){
            $this->output_data();
            return;
        }

        $menutree=$this->getAllMenuTree();
        $rolemenu=M('rolemenu');
        $rolemenus=$rolemenu->where("roleid=".$roleid)->select();
        $this->makeTreeCheck($menutree,$rolemenus);
        $this->output_data($menutree);
        return;
    }

    private function makeTreeCheck(&$menutree,$rolemenus){
        if(!is_array($menutree)||empty($menutree)||empty($rolemenus)){
            return;
        }
//        \Think\Log::record("gggggggggggggg sss".json_encode($menutree),"INFO");
        foreach ($menutree as &$item){
//            \Think\Log::record("gggggggggggggg sss".json_encode($item),"INFO");
            if(isset($item['privilege'])&&$item['privilege']==2){
                foreach ($rolemenus as $rolemenu){
                    if($rolemenu['menuid']==$item['menuid']&&$rolemenu['privilege']==1){
                        $state['checked']=true;
                        $item['state']=$state;
                        break;
                    }
                }
            }else{
                foreach ($rolemenus as $rolemenu){
                    if(isset($item['dataid'])&&$rolemenu['menuid']==$item['dataid']){
                        $state['checked']=true;
                        $item['state']=$state;
                        break;
                    }
                }
                $this->makeTreeCheck($item,$rolemenus);
            }
        }
    }

    private function getAllMenuTree(){
        $menu=M('menu');
        $count=$menu->count();
        if(empty($count)){
            return;
        }
        $menutree=$this->makeMenuPrivilegeTree(-1);
        return $menutree;
    }

    private function makeMenuPrivilegeTree($menuid){
        $menu=M('menu');
        $subs=$menu->where("parentid=".$menuid)->select();
        if(empty($subs)){
            return;
        }
        $nodes=array();
        foreach ($subs as $sub){
            $node['text']=$sub['menutitle'];
            $node['dataid']=$sub['menuid'];
            $subnodes=$this->makeMenuPrivilegeTree($sub['menuid']);
            if(empty($subnodes)&&$sub['editable']==1){
                $prinodes=array();
                $prinodes[0]['text']="可修改";
                $prinodes[0]['privilege']=2;
                $prinodes[0]['menuid']=$sub['menuid'];
                $node['nodes']=$prinodes;
            }else{
                $node['nodes']=$subnodes;
            }
            $nodes[]=$node;
        }
        return $nodes;
    }

    public function setPrivilege(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $roleid=$request["roleid"];
        $privileges=$request["privileges"];
        if(empty($roleid)){
            $this->output_data();
            return;
        }
        \Think\Log::record(json_encode($privileges),"INFO");
        $this->clearRolemenu($roleid);
        if(!empty($privileges)){
            $rolemenu=M('rolemenu');
            foreach ($privileges as $privilege) {
                if(!empty($privilege['privilege'])&&$privilege['privilege']==2){
                    if(!empty($privilege['menuid'])){
                        $data['privilege']=1;
                        $rolemenu->where("roleid=".$roleid." and menuid=".$privilege['menuid'])->save($data);
                    }
                }else{
                    $data['roleid']=$roleid;
                    $data['menuid']=$privilege['dataid'];
                    $data['privilege']=0;
                    $rolemenu->add($data);
                }
            }
        }
        $this->output_data("");
        return;
    }

    private function clearRolemenu($roleid){
        $rolemenu=M('rolemenu');
        $rolemenu->where("roleid=".$roleid)->delete();
    }
}