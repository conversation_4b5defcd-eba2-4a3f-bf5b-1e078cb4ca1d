<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class HomepageController extends CommonController {
    public function index(){
//        $this->staticMain();
        $this->assign("rubbishtypelist",CommonDao::getAllRubbishtype());
        
        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $this->assign("search_propertyid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
            
            // 管理员角色，默认加载第一个运营商的物业列表
            $propertylist = array();
            if(!empty($operatorlist) && count($operatorlist) > 0){
                $propertylist = $user->where("operatoruserid=" . $operatorlist[0]['userid'])->select();
            }
            $this->assign("propertylist", $propertylist);
        }else if(session('roleid')==2){
            // 运营商角色，只能看到自己的数据，不能选择运营商
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",1);
            
            // 获取该运营商下的物业列表
            $cond['operatoruserid']=session('userid');
            $propertylist=$user->where($cond)->select();
            $this->assign("propertylist",$propertylist);
        }else{
            // 物业角色，只能看到自己的数据，不能选择运营商和物业
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",0);
        }
        
        $this->display();
    }

    private function staticMain(){
        $register=M('register');
        $card=M('card');
        $dev=M('dev');
        $chargelog=M('chargelog');
        $alarm=M('alarm');
    }
    
    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $rubbishtype = $request["rubbishtype"];
        $starttime = $request["starttime"];
        $endtime = $request["endtime"];
        $timespan = $request["timespan"];
        $operatorid = $request["operatorid"];
        $propertyid = $request["propertyid"];
        
        $cond = array();
        if(session('roleid')==2){
            $operatorid = session('userid');
            $cond['operatorid'] = $operatorid;
        }else if(session('roleid')==3){
            $operatorid = session('operatoruserid');
            $propertyid = session('userid');
            $cond['propertyid'] = $propertyid;
        }
        
        if(!empty($rubbishtype)){
            $cond['rubbishtype'] = $rubbishtype;
        }
        if(!empty($operatorid)){
            $cond['operatorid'] = $operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid'] = $propertyid;
        }
        
        if(empty($starttime)){
            $starttime = time() - 3600*24*365; // 默认一年
        }else{
            $starttime = strtotime($starttime);
        }
        if(empty($endtime)){
            $endtime = time() + 100;
        }else{
            $endtime = strtotime($endtime);
        }
        $condtime = " throwtime >=" . $starttime . " and throwtime<" . $endtime . " ";
        
        $throwlog = M('throwlog');
        $result = array();
        
        // 获取所有垃圾类型
        $rubbishtypes = CommonDao::getAllRubbishtype();
        $rubbishtypeNames = array();
        foreach($rubbishtypes as $type){
            $rubbishtypeNames[$type['rubbishtype']] = $type['name'];
        }
        
        // 根据时间跨度获取数据
        if(empty($timespan) || $timespan == 'year'){
            // 按年统计
            $result = $this->getStatsByYear($throwlog, $cond, $condtime, $rubbishtypeNames);
        }else if($timespan == 'halfyear'){
            // 按半年统计
            $result = $this->getStatsByHalfYear($throwlog, $cond, $condtime, $rubbishtypeNames);
        }else if($timespan == 'month'){
            // 按月统计
            $result = $this->getStatsByMonth($throwlog, $cond, $condtime, $rubbishtypeNames);
        }else if($timespan == 'week'){
            // 按周统计
            $result = $this->getStatsByWeek($throwlog, $cond, $condtime, $rubbishtypeNames);
        }
        
        echo json_encode($result);
        die;
    }
    
    // 按年统计
    private function getStatsByYear($throwlog, $cond, $condtime, $rubbishtypeNames){
        $result = array();
        $years = array();
        $data = array();
        
        // 获取年份列表
        $yearData = $throwlog->field('FROM_UNIXTIME(throwtime, "%Y") as year')
                            ->where($cond)
                            ->where($condtime)
                            ->group('year')
                            ->order('year asc')
                            ->select();
        
        foreach($yearData as $item){
            $years[] = $item['year'];
        }
        
        // 获取每种垃圾类型在每年的投放量
        foreach($rubbishtypeNames as $type => $name){
            $typeCond = array_merge($cond, array('rubbishtype' => $type));
            $typeData = array();
            
            foreach($years as $year){
                $yearCondtime = $condtime . " AND FROM_UNIXTIME(throwtime, '%Y') = '" . $year . "'";
                $count = $throwlog->where($typeCond)
                                 ->where($yearCondtime)
                                 ->sum('throwweight');
                $typeData[] = $count ? $count : 0;
            }
            
            $data[] = array(
                'name' => $name,
                'type' => 'bar',
                'data' => $typeData
            );
        }
        
        $result['xAxis'] = $years;
        $result['series'] = $data;
        $result['legend'] = array_values($rubbishtypeNames);
        
        return $result;
    }
    
    // 按半年统计
    private function getStatsByHalfYear($throwlog, $cond, $condtime, $rubbishtypeNames){
        $result = array();
        $halfYears = array();
        $data = array();
        
        // 获取半年列表
        $halfYearData = $throwlog->field('CONCAT(FROM_UNIXTIME(throwtime, "%Y"), "-", IF(MONTH(FROM_UNIXTIME(throwtime)) <= 6, "上半年", "下半年")) as halfyear')
                                ->where($cond)
                                ->where($condtime)
                                ->group('halfyear')
                                ->order('halfyear asc')
                                ->select();
        
        foreach($halfYearData as $item){
            $halfYears[] = $item['halfyear'];
        }
        
        // 获取每种垃圾类型在每半年的投放量
        foreach($rubbishtypeNames as $type => $name){
            $typeCond = array_merge($cond, array('rubbishtype' => $type));
            $typeData = array();
            
            foreach($halfYears as $halfYear){
                list($year, $half) = explode('-', $halfYear);
                $startMonth = ($half == '上半年') ? 1 : 7;
                $endMonth = ($half == '上半年') ? 6 : 12;
                
                $halfYearCondtime = $condtime . " AND FROM_UNIXTIME(throwtime, '%Y') = '" . $year . "' AND MONTH(FROM_UNIXTIME(throwtime)) >= " . $startMonth . " AND MONTH(FROM_UNIXTIME(throwtime)) <= " . $endMonth;
                $count = $throwlog->where($typeCond)
                                 ->where($halfYearCondtime)
                                 ->sum('throwweight');
                $typeData[] = $count ? $count : 0;
            }
            
            $data[] = array(
                'name' => $name,
                'type' => 'bar',
                'data' => $typeData
            );
        }
        
        $result['xAxis'] = $halfYears;
        $result['series'] = $data;
        $result['legend'] = array_values($rubbishtypeNames);
        
        return $result;
    }
    
    // 按月统计
    private function getStatsByMonth($throwlog, $cond, $condtime, $rubbishtypeNames){
        $result = array();
        $months = array();
        $data = array();
        
        // 获取月份列表
        $monthData = $throwlog->field('FROM_UNIXTIME(throwtime, "%Y-%m") as month')
                             ->where($cond)
                             ->where($condtime)
                             ->group('month')
                             ->order('month asc')
                             ->select();
        
        foreach($monthData as $item){
            $months[] = $item['month'];
        }
        
        // 获取每种垃圾类型在每月的投放量
        foreach($rubbishtypeNames as $type => $name){
            $typeCond = array_merge($cond, array('rubbishtype' => $type));
            $typeData = array();
            
            foreach($months as $month){
                $monthCondtime = $condtime . " AND FROM_UNIXTIME(throwtime, '%Y-%m') = '" . $month . "'";
                $count = $throwlog->where($typeCond)
                                 ->where($monthCondtime)
                                 ->sum('throwweight');
                $typeData[] = $count ? $count : 0;
            }
            
            $data[] = array(
                'name' => $name,
                'type' => 'bar',
                'data' => $typeData
            );
        }
        
        $result['xAxis'] = $months;
        $result['series'] = $data;
        $result['legend'] = array_values($rubbishtypeNames);
        
        return $result;
    }
    
    // 按周统计
    private function getStatsByWeek($throwlog, $cond, $condtime, $rubbishtypeNames){
        $result = array();
        $weeks = array();
        $data = array();
        
        // 获取周列表
        $weekData = $throwlog->field('CONCAT(FROM_UNIXTIME(throwtime, "%Y"), "-第", FROM_UNIXTIME(throwtime, "%u"), "周") as week')
                            ->where($cond)
                            ->where($condtime)
                            ->group('week')
                            ->order('week asc')
                            ->select();
        
        foreach($weekData as $item){
            $weeks[] = $item['week'];
        }
        
        // 获取每种垃圾类型在每周的投放量
        foreach($rubbishtypeNames as $type => $name){
            $typeCond = array_merge($cond, array('rubbishtype' => $type));
            $typeData = array();
            
            foreach($weeks as $week){
                list($year, $weekNum) = explode('-第', $week);
                $weekNum = intval($weekNum);
                
                $weekCondtime = $condtime . " AND FROM_UNIXTIME(throwtime, '%Y') = '" . $year . "' AND FROM_UNIXTIME(throwtime, '%u') = '" . $weekNum . "'";
                $count = $throwlog->where($typeCond)
                                 ->where($weekCondtime)
                                 ->sum('throwweight');
                $typeData[] = $count ? $count : 0;
            }
            
            $data[] = array(
                'name' => $name,
                'type' => 'bar',
                'data' => $typeData
            );
        }
        
        $result['xAxis'] = $weeks;
        $result['series'] = $data;
        $result['legend'] = array_values($rubbishtypeNames);
        
        return $result;
    }
    
    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }
}