<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class CostpointlogMgrController extends CommonController {
    public function index(){
		$this->assign("pagetitle","线下积分消费记录");
        $this->display();
    }

    public function add(){
		$this->assign("pagetitle","添加消费记录");
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
		$this->assign("pagetitle","查看消费记录");
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $cond=array();
        if(session('roleid')==6){
            $cond['opruserid']=session('userid');
        }
        $costpointlog=M('costpointlog');
        $res['total']=$costpointlog->where($cond)->count("id");
        $result=$costpointlog->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        $register=M('register');
        $card=M('card');
        $user=M('user');

        $userid=$result['userid'];
        if(!empty($userid)){
            $registerdata=$register->where('id='.$userid)->find();
            if(!empty($registerdata)){
                $result['username']=$registerdata['name'];
            }
        }

        $carduserid=$result['carduserid'];
        if(!empty($carduserid)){
            $carddata=$card->where('id='.$carduserid)->find();
            if(!empty($carddata)){
                $result['cardnumber']=$carddata['cardnumber'];
            }
        }

        $opruserid=$result['opruserid'];
        if(!empty($opruserid)){
            $userdata=$user->where('userid='.$opruserid)->find();
            if(!empty($userdata)){
                $result['oprusername']=$userdata['name'];
            }
        }

        $propertyid=$result['propertyid'];
        if(!empty($propertyid)){
            $userdata=$user->where('userid='.$propertyid)->find();
            if(!empty($userdata)){
                $result['propertyname']=$userdata['name'];
            }
        }

        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $address=$request["address"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($code)){
            $this->output_commonerror('消费记录编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('消费记录名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('消费记录编码已存在');
            return;
        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('消费记录名称已存在');
            return;
        }

        $costpointlog=M('costpointlog');
        $data['code']=$code;
        $data['name']=$name;
        $data['address']=$address;
//        $data['lat']=$lat;
//        $data['lng']=$lng;
        $data['createtime']=time();
        $costpointlog->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $code=$request["code"];
        $name=$request["name"];
        $address=$request["address"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($id)){
            $this->output_commonerror('消费记录ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('消费记录编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('消费记录名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('消费记录ID不存在');
            return;
        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("code='".$code."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('消费记录编码已存在');
            return;
        }
        $costpointlog=M('costpointlog');
        $result=$costpointlog->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('消费记录名称已存在');
            return;
        }

        $costpointlog=M('costpointlog');
        $data['code']=$code;
        $data['name']=$name;
        $data['address']=$address;
//        $data['lat']=$lat;
//        $data['lng']=$lng;
        $costpointlog->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $costpointlog=M('costpointlog');
        foreach ($idlist as $id){
            $costpointlog->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }
}