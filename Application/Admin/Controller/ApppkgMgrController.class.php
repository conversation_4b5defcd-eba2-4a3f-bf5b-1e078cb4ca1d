<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class ApppkgMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $apppkg=M('apppkg');
        $result=$apppkg->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $apppkg=M('apppkg');
        $res['total']=$apppkg->count("id");
        $result=$apppkg->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $starttime=$result['starttime'];
        if(!empty($starttime)){
            $result['starttime']=date('Y-m-d H:i:s',$starttime);
        }
        $endtime=$result['endtime'];
        if(!empty($endtime)){
            $result['endtime']=date('Y-m-d H:i:s',$endtime);
        }
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        $updatetime=$result['updatetime'];
        if(!empty($updatetime)){
            $result['updatetime']=date('Y-m-d H:i:s',$updatetime);
        }
//        $result['typename']=CommonDao::getTypeName($result['type']);
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $version=$request["version"];
        $url=$request["url"];
        $md5=$request["md5"];
//        $starttime=$request["starttime"];
//        $endtime=$request["endtime"];
        if(empty($version)){
            $this->output_commonerror('版本号不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
        if(empty($md5)){
            $this->output_commonerror('md5不能为空');
            return;
        }
//        if(empty($starttime)){
//            $this->output_commonerror('起始时间不能为空');
//            return;
//        }
//        if(empty($endtime)){
//            $this->output_commonerror('结束时间不能为空');
//            return;
//        }
        $filename = end(explode('/',$url));

        $config=M('config');
        $localurl=$config->where("configkey='localurl'")->find();

        $apppkg=M('apppkg');
        $data['version']=$version;
        $data['url']=$url;
        $data['filename']=$filename;
//        $data['starttime']=strtotime($starttime);
//        $data['endtime']=strtotime($endtime);
        $data['createtime']=time();
        $data['updatetime']=time();
        $content=$localurl['configvalue'].$url."#".$md5."#".$version;
        $data['content']=$content;
        $apppkgid=$apppkg->add($data);
//        $this->addFiletask($apppkgid,0,$content);
        $this->output_data("");
        return;
    }

    private function addFiletask($apppkgid,$filetype,$content){
        $dev=M('dev');
        $devdata=$dev->order("id asc")->select();
        if(!empty($devdata)){
            $filetask=M('filetask');
            foreach ($devdata as $devdata_elem){
                $filetaskdata['devid']=$devdata_elem['id'];
                $filetaskdata['status']=0;
                $filetaskdata['filetype']=$filetype;
                $filetaskdata['failtimes']=0;
                $filetaskdata['content']=$content;
                $filetaskdata['createtime']=time();
                $filetaskdata['apppkgid']=$apppkgid;
                $filetask->add($filetaskdata);
            }
        }
    }

    private function delFileTask($apppkgid){
        $filetask=M('filetask');
        $filetask->where("apppkgid=".$apppkgid)->delete();
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $version=$request["version"];
        $url=$request["url"];
//        $starttime=$request["starttime"];
//        $endtime=$request["endtime"];
        if(empty($version)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
//        if(empty($starttime)){
//            $this->output_commonerror('起始时间不能为空');
//            return;
//        }
//        if(empty($endtime)){
//            $this->output_commonerror('结束时间不能为空');
//            return;
//        }
        $filename = end(explode('/',$url));

        $apppkg=M('apppkg');
        $data['version']=$version;
        $data['url']=$url;
        $data['filename']=$filename;
//        $data['starttime']=strtotime($starttime);
//        $data['endtime']=strtotime($endtime);
        $data['updatetime']=time();
        $apppkg->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $apppkg=M('apppkg');
        foreach ($idlist as $id){
            $apppkg->where('id='.$id)->delete();
            $this->delFileTask($id);
        }
        $this->output_data("");
        return;
    }
}