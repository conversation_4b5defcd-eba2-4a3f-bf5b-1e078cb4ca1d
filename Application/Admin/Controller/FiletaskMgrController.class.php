<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class FiletaskMgrController extends CommonController {
    public function index(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $apppkg=M('apppkg');
        $apppkgdata=$apppkg->where("id=".$id)->find();
        $this->assign("apppkg",$apppkgdata);
        $this->display();
    }

    public function add(){
        $apppkgid=I('apppkgid');
        $this->assign("apppkgid",$apppkgid);

        $user=M('user');
        $this->assign("search_operatorid_visible",1);
        $this->assign("search_propertyid_visible",1);
        $cond['roleid']=2;
        $operatorlist=$user->where($cond)->select();
        $this->assign("operatorlist",$operatorlist);
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $filetask=M('filetask');
        $result=$filetask->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $apppkgid=$request["apppkgid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if(empty($apppkgid)){
            $this->output_commonerror('apppkgid不能为空');
            return;
        }
        $filetask=M('filetask');
        $cond['apppkgid']=$apppkgid;
        $res['total']=$filetask->where($cond)->count("id");
        $result=$filetask->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $starttime=$result['starttime'];
        if(!empty($starttime)){
            $result['starttime']=date('Y-m-d H:i:s',$starttime);
        }else{
            $result['starttime']=null;
        }
        $completetime=$result['completetime'];
        if(!empty($completetime)){
            $result['completetime']=date('Y-m-d H:i:s',$completetime);
        }else{
            $result['completetime']=null;
        }
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }else{
            $result['createtime']=null;
        }
        $devid=$result['devid'];
        if(!empty($devid)){
            $dev=M('dev');
            $devdata=$dev->where("id=".$devid)->find();
            $result['devname']=$devdata['name'];
            $result['devcode']=$devdata['code'];
        }
        $result['statusname']=CommonDao::getFiletaskStatusName($result['status']);
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $apppkgid=$request["apppkgid"];
        $idlist=$request["idlist"];
        if(empty($apppkgid)){
            $this->output_commonerror('apppkgid不能为空');
            return;
        }
        if(empty($idlist)){
            $this->output_commonerror('目标设备不能为空');
            return;
        }
        $apppkg=M('apppkg');
        $apppkgdata=$apppkg->where("id=".$apppkgid)->find();
        if(empty($apppkgdata)){
            $this->output_commonerror('版本不存在');
            return;
        }
        $content=$apppkgdata['content'];

        $filetask=M('filetask');
        foreach ($idlist as $id){
            $data['devid']=$id;
            $data['status']=0;
            $data['filetype']=0;
            $data['failtimes']=0;
            $data['content']=$content;
            $data['createtime']=time();
            $data['apppkgid']=$apppkgid;
            $filetask->add($data);
        }
        $this->output_data("");
        return;
    }

    private function addFiletask($filetaskid,$filetype,$content){
        $dev=M('dev');
        $devdata=$dev->order("id asc")->select();
        if(!empty($devdata)){
            $filetask=M('filetask');
            foreach ($devdata as $devdata_elem){
                $filetaskdata['devid']=$devdata_elem['id'];
                $filetaskdata['status']=0;
                $filetaskdata['filetype']=$filetype;
                $filetaskdata['failtimes']=0;
                $filetaskdata['content']=$content;
                $filetaskdata['createtime']=time();
                $filetaskdata['filetaskid']=$filetaskid;
                $filetask->add($filetaskdata);
            }
        }
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $version=$request["version"];
        $url=$request["url"];
//        $starttime=$request["starttime"];
//        $endtime=$request["endtime"];
        if(empty($version)){
            $this->output_commonerror('类型不能为空');
            return;
        }
        if(empty($url)){
            $this->output_commonerror('URL不能为空');
            return;
        }
//        if(empty($starttime)){
//            $this->output_commonerror('起始时间不能为空');
//            return;
//        }
//        if(empty($endtime)){
//            $this->output_commonerror('结束时间不能为空');
//            return;
//        }
        $filename = end(explode('/',$url));

        $filetask=M('filetask');
        $data['version']=$version;
        $data['url']=$url;
        $data['filename']=$filename;
//        $data['starttime']=strtotime($starttime);
//        $data['endtime']=strtotime($endtime);
        $data['updatetime']=time();
        $filetask->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $filetask=M('filetask');
        foreach ($idlist as $id){
            $filetask->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }

    public function resetFiletask(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_data("");
            return;
        }
        $filetask=M('filetask');
        $data['status']=0;
        $filetask->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_getdevlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $devcode     =$request["devcode"];
        $devname     =$request["devname"];
        $operatorid  =$request["operatorid"];
        $propertyid  =$request["propertyid"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }

        if(!empty($devcode)){
            $cond['code']=array('like','%'.$devcode.'%');
        }
        if(!empty($devname)){
            $cond['name']=array('like','%'.$devname.'%');
        }
        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }

        $dev=M('dev');
        $res['total']=$dev->where($cond)->count("id");
        $result=$dev->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeDevInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeDevInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        $time=time()-C('onlineTimeDelta');
        if($result['heartbeattime']>$time){
            $result['onlinestatusname']="在线";
            $result['onlinestatus']=1;
        }else{
            $result['onlinestatusname']="离线";
            $result['onlinestatus']=0;
        }

        return $result;
    }

    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }
}