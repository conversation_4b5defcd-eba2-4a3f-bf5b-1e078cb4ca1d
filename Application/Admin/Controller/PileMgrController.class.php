<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class PileMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(31));
        $stationid=I('stationid');
        if(!empty($stationid)){
            $station=M('station');
            $stationdata=$station->where("stationid=".$stationid)->find();
            $this->assign("stationname",$stationdata['name']);
        }
        $this->assign("stationid",$stationid);
        $this->display();
    }

    public function add(){
        $stationid=I('stationid');
        if(!empty($stationid)){
            $this->assign("stationid",$stationid);
        }
        $this->assign("pileAcdcList",CommonDao::getAllPileAcdc());
        $allStations=CommonDao::getAllStation();
        $this->assign("stationList",$allStations);
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $pile=M('pile');
        $result=$pile->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("pile",$result);
        $this->assign("pileAcdcList",CommonDao::getAllPileAcdc());

        $allStations=CommonDao::getAllStation();
        $this->assign("stationList",$allStations);

        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $stationid=$request["stationid"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $cond=array();
        if(!empty($stationid)){
            $cond['stationid']=$stationid;
        }

        $pile=M('pile');
        $res['total']=$pile->where($cond)->count("id");
        $result=$pile->where($cond)->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        if(empty($elem)){
            return $elem;
        }

        $elem['acdcname']=CommonDao::getPileAcdcName($elem['acdc']);
        $stationInfo=CommonDao::getStattionInfo($elem['stationid']);
        if(!empty($stationInfo)){
            $elem['stationname']=$stationInfo['name'];
        }
        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $elem;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $code=$request["code"];
        $name=$request["name"];
        $stationid=$request["stationid"];
        $acdc=$request["acdc"];
        $guncount=$request["guncount"];
        $rate_e=$request["rate_e"];
        $rate_s=$request["rate_s"];
        $rate_p=$request["rate_p"];
        $supplier=$request["supplier"];
        if(empty($code)){
            $this->output_commonerror('编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($stationid)){
            $this->output_commonerror('所属站不能为空');
            return;
        }
        if(empty($acdc)){
            $this->output_commonerror('acdc不能为空');
            return;
        }
        if(empty($guncount)){
            $this->output_commonerror('充电枪个数不能为空');
            return;
        }
        if(empty($supplier)){
            $this->output_commonerror('供应商不能为空');
            return;
        }
        $pile=M('pile');
        $result=$pile->where("code='".$code."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $pile=M('pile');
        $result=$pile->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $pile=M('pile');
        $data['code']=$code;
        $data['name']=$name;
        $data['stationid']=$stationid;
        $data['acdc']=$acdc;
        $data['guncount']=$guncount;
        $data['rate_e']=$rate_e;
        $data['rate_s']=$rate_s;
        $data['rate_p']=$rate_p;
        $data['supplier']=$supplier;
        $data['createtime']=time();
        $pile->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $code=$request["code"];
        $name=$request["name"];
        $stationid=$request["stationid"];
        $acdc=$request["acdc"];
        $guncount=$request["guncount"];
        $rate_e=$request["rate_e"];
        $rate_s=$request["rate_s"];
        $rate_p=$request["rate_p"];
        $supplier=$request["supplier"];
        if(empty($id)){
            $this->output_commonerror('id不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('名称不能为空');
            return;
        }
        if(empty($stationid)){
            $this->output_commonerror('所属站不能为空');
            return;
        }
        if(empty($acdc)){
            $this->output_commonerror('acdc不能为空');
            return;
        }
        if(empty($guncount)){
            $this->output_commonerror('充电枪个数不能为空');
            return;
        }
        if(empty($supplier)){
            $this->output_commonerror('供应商不能为空');
            return;
        }
        $pile=M('pile');
        $result=$pile->where("code='".$code."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $pile=M('pile');
        $result=$pile->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $pile=M('pile');
        $data['code']=$code;
        $data['name']=$name;
        $data['stationid']=$stationid;
        $data['acdc']=$acdc;
        $data['guncount']=$guncount;
        $data['rate_e']=$rate_e;
        $data['rate_s']=$rate_s;
        $data['rate_p']=$rate_p;
        $data['supplier']=$supplier;
        $pile->where("id=".$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $pile=M('pile');
        foreach ($idlist as $id){
            $pile->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }

    public function findposition()
    {
        $this->assign("lat",$_REQUEST['lat']);
        $this->assign("lng",$_REQUEST['lng']);
        $this->display();
    }
}