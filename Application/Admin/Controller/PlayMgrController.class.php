<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;

class PlayMgrController extends CommonController {
    public function index(){
		$this->assign("pagetitle","播放实例列表");
        $this->display();
    }

    public function add(){
		$this->assign("pagetitle","添加播放实例");
		$layout=M('layout');
        $layoutlist=$layout->order('id desc')->field('id,name')->select();
        $this->assign("layoutlist",$layoutlist);
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $play=M('play');
        $result=$play->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
		$this->assign("pagetitle","查看播放实例");

        $layout=M('layout');
        $layoutlist=$layout->order('id desc')->field('id,name')->select();
        $this->assign("layoutlist",$layoutlist);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $play=M('play');
        $res['total']=$play->count("id");
        $result=$play->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $createtime=$result['createtime'];
        if(!empty($createtime)){
            $result['createtime']=date('Y-m-d H:i:s',$createtime);
        }

        $layoutid=$result['layoutid'];
        if(!empty($layoutid)){
            $layout=M('layout');
            $layoutdata=$layout->where('id='.$layoutid)->find();
            if(!empty($layoutdata)){
                $result['layoutname']=$layoutdata['name'];
            }
        }
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $name=$request["name"];
        $layoutid=$request["layoutid"];
        $content=$request["content"];
        $playlistmap=$request["playlistmap"];
        if(empty($name)){
            $this->output_commonerror('播放实例名称不能为空');
            return;
        }
        if(empty($layoutid)){
            $this->output_commonerror('布局模板不能为空');
            return;
        }
        if(empty($content)||empty($playlistmap)){
            $this->output_commonerror('布局内容不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $play=M('play');
        $result=$play->where("name='".$name."'")->select();
        if(!empty($result)){
            $this->output_commonerror('播放实例名称已存在');
            return;
        }

        $play=M('play');
        $data['name']=$name;
        $data['content']=$content;
        $data['layoutid']=$layoutid;
        $data['playlistmap']=json_encode($playlistmap);
        $data['createuserid']=session('userid');
        $data['createtime']=time();
        $playid=$play->add($data);

        $url=$this->makePlaylistFile($playid,$playlistmap,$content);
        $this->updatePlayadsfile($playid,$playlistmap);

        $newdata['url']=$url;
        $play->where('id='.$playid)->save($newdata);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $name=$request["name"];
        $layoutid=$request["layoutid"];
        $content=$request["content"];
        $playlistmap=$request["playlistmap"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('播放实例名称不能为空');
            return;
        }
        if(empty($layoutid)){
            $this->output_commonerror('布局模板不能为空');
            return;
        }
        if(empty($content)||empty($playlistmap)){
            $this->output_commonerror('布局内容不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $play=M('play');
        $result=$play->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('播放实例名称已存在');
            return;
        }

        $dbdata=$play->where('id='.$id)->find();
        if(empty($dbdata)){
            $this->output_commonerror('播放实例不存在');
            return;
        }

        $play=M('play');
        $data['name']=$name;
        $data['content']=$content;
        $data['layoutid']=$layoutid;
        $data['playlistmap']=json_encode($playlistmap);;
        $play->where('id='.$id)->save($data);

        $oldurl=$dbdata['url'];
        if(!empty($oldurl)){
            $ROOT = $_SERVER['DOCUMENT_ROOT'];
            unlink($ROOT.$oldurl);
        }

//        \Think\Log::record("ffffffff ".$id,"INFO");
        $url=$this->makePlaylistFile($id,$playlistmap,$content);
        $this->updatePlayadsfile($id,$playlistmap);

        $newdata['url']=$url;
        $play->where('id='.$id)->save($newdata);
        $this->output_data("");
        return;
    }

    private function makePlaylistFile($playid,$playlistmap,$content){
        if(empty($playid)||empty($playlistmap)||empty($content)){
            return;
        }

        $main_window=$playlistmap['main_window'];
        if(empty($main_window)){
            return;
        }

        $contentdom = new \DOMDocument();
        $contentdom->loadXML($content);
        $divs=$contentdom->getElementsByTagName('div');
        if(empty($divs)){
            return;
        }
        $sub_windows=array();
        foreach ($divs as $divs_elem) {
            $divid=$divs_elem->attributes[0]->nodeValue;
            \Think\Log::record("divid = ".$divid,"INFO");
            if(strpos($divid,'screen_window')===0){
                $style=$divs_elem->attributes[1]->nodeValue;
                $res_screen_window=$this->parseStyle($style);
            }else if(strpos($divid,'main_window')===0){
                $style=$divs_elem->attributes[2]->nodeValue;
                $res_main_window=$this->parseStyle($style);
            }else if(strpos($divid,'sub_window_')===0){
                $style=$divs_elem->attributes[3]->nodeValue;
                $res=$this->parseStyle($style);
                $res['id']=$divid;
                $sub_windows[$divid]=$res;
            }else{
                continue;
            }
        }


        $filename='playlist_'.$playid.'__'.date('YmdHis',time()).'.xml';
        $currentdate=date('Y-m-d',time());
        $ROOT = $_SERVER['DOCUMENT_ROOT'];
        $urlpath=__ROOT__."/Public/playlistfile/".$filename;
        $url=$ROOT.$urlpath;
        $doc = new \DOMDocument('1.0','utf-8');
        $WINDOWS = $doc->createElement("WINDOWS");
        $doc->appendChild($WINDOWS);
        if(!empty($res_screen_window)){
            $WINDOWS->setAttribute("x",$res_screen_window['x']);
            $WINDOWS->setAttribute("y",$res_screen_window['y']);
            $WINDOWS->setAttribute("w",$res_screen_window['w']);
            $WINDOWS->setAttribute("h",$res_screen_window['h']);
        }

        $VERSIONINFO = $doc->createElement("VERSIONINFO");
        $WINDOWS->appendChild($VERSIONINFO);
        $VERSIONINFO->setAttribute("version","1.0.0");
        $VERSIONINFO->setAttribute("timeDate",$currentdate);

        $MAIN_WINDOW = $doc->createElement("MAIN_WINDOW");
        $WINDOWS->appendChild($MAIN_WINDOW);

        $ITEMS = $doc->createElement("ITEMS");
        $MAIN_WINDOW->appendChild($ITEMS);

        foreach ($main_window as $main_window_elem){
            $ITEM = $doc->createElement("ITEM");
            $ITEMS->appendChild($ITEM);
            $ITEM->setAttribute("id",$main_window_elem['id']);
            $ITEM->setAttribute("type",CommonDao::getTypeFromTypename($main_window_elem['typename']));
            $ITEM->setAttribute("file",$main_window_elem['filename']);
            $ITEM->setAttribute("fullscreen",'true');
            $ITEM->setAttribute("startDate",$main_window_elem['starttime']);
            $ITEM->setAttribute("endDate",$main_window_elem['endtime']);
        }

        $MAINWIN = $doc->createElement("MAINWIN");
        $MAIN_WINDOW->appendChild($MAINWIN);
        if(!empty($res_main_window)){
            $MAINWIN->setAttribute("x",$res_main_window['x']);
            $MAINWIN->setAttribute("y",$res_main_window['y']);
            $MAINWIN->setAttribute("w",$res_main_window['w']);
            $MAINWIN->setAttribute("h",$res_main_window['h']);
        }

        $PLAYCOMMON = $doc->createElement("PLAYCOMMON");
        $MAINWIN->appendChild($PLAYCOMMON);

        $PLAYLIST = $doc->createElement("PLAYLIST");
        $MAINWIN->appendChild($PLAYLIST);

        $PLAY = $doc->createElement("PLAY");
        $PLAYLIST->appendChild($PLAY);
        $PLAY->setAttribute("startTime","00:00");
        $PLAY->setAttribute("endTime","23:59");

        foreach ($main_window as $main_window_elem){
            $ACTION = $doc->createElement("ACTION");
            $PLAY->appendChild($ACTION);
            $ACTION->setAttribute("id",$main_window_elem['id']);
            $ACTION->setAttribute("interval",$main_window_elem['interval']);
        }

        $SUB_WINDOW = $doc->createElement("SUB_WINDOW");
        $WINDOWS->appendChild($SUB_WINDOW);
        $SUB_WINDOW_ITEMS = $doc->createElement("ITEMS");
        $SUB_WINDOW->appendChild($SUB_WINDOW_ITEMS);

        foreach ($playlistmap as $playlistmap_key => $playlistmap_value){
            if(strpos($playlistmap_key,'sub_window_')===0){
                foreach ($playlistmap_value as $playlistmap_value_elem){
                    $SUB_WINDOW_ITEM = $doc->createElement("ITEM");
                    $SUB_WINDOW_ITEMS->appendChild($SUB_WINDOW_ITEM);
                    $SUB_WINDOW_ITEM->setAttribute("id",$playlistmap_value_elem['id']);
                    $SUB_WINDOW_ITEM->setAttribute("type",CommonDao::getTypeFromTypename($playlistmap_value_elem['typename']));
                    $SUB_WINDOW_ITEM->setAttribute("file",$playlistmap_value_elem['filename']);
                    $SUB_WINDOW_ITEM->setAttribute("fullscreen",'true');
                    $SUB_WINDOW_ITEM->setAttribute("startDate",$playlistmap_value_elem['starttime']);
                    $SUB_WINDOW_ITEM->setAttribute("endDate",$playlistmap_value_elem['endtime']);
                }
            }
        }

        $SUBWINS = $doc->createElement("SUBWINS");
        $SUB_WINDOW->appendChild($SUBWINS);
        foreach ($playlistmap as $playlistmap_key => $playlistmap_value){
            if(strpos($playlistmap_key,'sub_window_')===0){
                $SUBWIN = $doc->createElement("SUBWIN");
                $SUBWINS->appendChild($SUBWIN);
                if(!empty($sub_windows[$playlistmap_key])){
                    $SUBWIN->setAttribute("x",$sub_windows[$playlistmap_key]['x']);
                    $SUBWIN->setAttribute("y",$sub_windows[$playlistmap_key]['y']);
                    $SUBWIN->setAttribute("w",$sub_windows[$playlistmap_key]['w']);
                    $SUBWIN->setAttribute("h",$sub_windows[$playlistmap_key]['h']);
                }
                foreach ($playlistmap_value as $playlistmap_value_elem){
                    $ACTION = $doc->createElement("ACTION");
                    $SUBWIN->appendChild($ACTION);
                    $ACTION->setAttribute("id",$playlistmap_value_elem['id']);
                    $ACTION->setAttribute("interval",$playlistmap_value_elem['interval']);
                }
            }
        }

        $doc->save($url);
        return $urlpath;
    }

    private function updatePlayadsfile($playid,$playlistmap){
        if(empty($playid)){
            return;
        }
        $playadsfile=M('playadsfile');
        $playadsfile->where('playid='.$playid)->delete();
        if(empty($playlistmap)){
            return;
        }
        $fileids=array();
        foreach ($playlistmap as $playlistmap_key => $playlistmap_value){
            foreach ($playlistmap_value as $playlistmap_value_elem){
                $adsfileid=$playlistmap_value_elem['id'];
                if(!in_array($adsfileid,$fileids)){
                    $fileids[]=$adsfileid;
                }
            }
        }
        foreach ($fileids as $fileids_elem){
            $data['playid']=$playid;
            $data['adsfileid']=$fileids_elem;
            $playadsfile->add($data);
        }
    }

    private function parseStyle($style){
        if(empty($style)) {
            return null;
        }

        $res=array();
        $styles=explode(';',$style);
        if(!empty($styles)){
            foreach ($styles as $styles_elem){
                $styles_elems=explode(':',$styles_elem);
                if(count($styles_elems)!=2){
                    continue;
                }
                foreach ($styles_elems as &$styles_elems_elem){
                    $styles_elems_elem=trim($styles_elems_elem);
                }
                if($styles_elems[0]=='width'){
                    $res['w']=$styles_elems[1];
                    $res['w']=$this->parseValue($res['w']);
                }else if($styles_elems[0]=='height'){
                    $res['h']=$styles_elems[1];
                    $res['h']=$this->parseValue($res['h']);
                }else if($styles_elems[0]=='top'){
                    $res['y']=$styles_elems[1];
                    $res['y']=$this->parseValue($res['y']);
                }else if($styles_elems[0]=='left'){
                    $res['x']=$styles_elems[1];
                    $res['x']=$this->parseValue($res['x']);
                }else{

                }
            }
        }
        return $res;
    }

    private function parseValue($value){
        $ratio=2;
        if(strpos($value,'px')!==false||strpos($value,'PX')!==false){
            return intval(substr($value,0,strlen($value)-2))*$ratio;
        }
        return intval($value)*$ratio;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $play=M('play');
        $playadsfile=M('playadsfile');
        $dev=M('dev');
        foreach ($idlist as $id){
            $playdb=$play->where('id='.$id)->find();
            if(empty($playdb)){
                continue;
            }
            $devdata=$dev->where('playid='.$id)->select();
            if(!empty($devdata)){
                $this->output_commonerror("播放实例[".$playdb['name']."]正在被设备[".$devdata[0]['name']."]使用");
                return;
            }
        }

        foreach ($idlist as $id){
            $playdb=$play->where('id='.$id)->find();
            if(empty($playdb)){
                continue;
            }
            $play->where('id='.$id)->delete();

            $playadsfile->where('playid='.$id)->delete();

            $oldurl=$playdb['url'];
            if(!empty($oldurl)){
                $ROOT = $_SERVER['DOCUMENT_ROOT'];
                unlink($ROOT.$oldurl);
            }
        }
        $this->output_data("");
        return;
    }

    public function api_getlayout(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        if(empty($id)){
            $this->output_data("");
            return;
        }
        $layout=M('layout');
        $layoutdata=$layout->where('id='.$id)->find();
        if(empty($layoutdata)){
            $this->output_data("");
            return;
        }
        $this->output_data($layoutdata);
        return;
    }
}