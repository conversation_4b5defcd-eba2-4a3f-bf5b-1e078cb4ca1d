<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">

    <title>积富回收</title>

    <meta name="keywords" content="平台">
    <meta name="description" content="平台">

    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <link rel="shortcut icon" href="__PUBLIC__/img/favicon.png">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.4.0" rel="stylesheet">
    <!--<link href="css/style.css?v=4.1.0" rel="stylesheet">-->
    <!--<link rel="stylesheet" href="css/my.css">-->
    <style>
        .fl{
            float:left;
        }
        .fr{
            float:right;
        }
        .clearfix:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
        }
        .clearfix_my:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
        }
        .anchorBL{

            display:none;

        }
        /* wraper1 用来控制宽度 */
        .wraper1{
            width: 100%;
        }
        /* wraper2 用来实现宽高等比例 */
        .wraper2{
            width: 100%;
            /*padding-bottom: 56.25%;*/
            padding-bottom: 54%;
            height: 0;
            position: relative;
        }
        /* wraper3 用来放置全部的子元素 */
        .wraper3{
            width: 100%;
            height: 100%;
            position: absolute;
        }
        .header{
            height: 8%;
            text-align: center;
            background: url("__PUBLIC__/img/topbag.png") no-repeat;
            background-size:100% 100%;
        }
        .header > .fl {
            width: 33%;
        }
        .content1{
            text-align: center;
            height: 92%;
        }
        .content1 > .fl {

        }
        .module-wraper{
            height: 32%;
            width: 100%;
            padding: 0;
            float: left;
        }
        .module-wraper1{
            height: 100%;
            width: 100%;
            padding: 0;
            float: left;
        }
        .module{
            float: left;
            margin: 0 4%;
            height: 100%;
            width: 92%;
            /*background-color: #12418e;*/
            background: url("__PUBLIC__/img/bgk.png") no-repeat;
            background-size: 100% 100%;
            /*border: solid 1px #efefef;*/
        }
        .space{
            float: left;
            height: 1.8%;
            width: 100%;
        }
        .space1{
            float: left;
            height: 2.2%;
            width: 100%;
        }
        .module-content{
            padding: 16px 18px;
            font-size: 12px;
            height: 100%;
        }
        .module-title{
            text-align: left;
            font-size: 14px;
            color: #c0e7fe;
            font-weight: bold;
        }
        .icon1{
            width: 24px;
            height: 24px;
        }
        .icon2{
            width: 14px;
            height: 14px;
            margin: 0 4px 0 0;
        }
        .wraper1001{
            margin:6px 0 14px 0;
            color: #c0e7fe;
        }
        .icon11{
            display: inline-block;
            width: 4px;
            height: 14px;
            line-height: 14px;
            background-color: #0fffb8;
            margin: 0 4px 0 4px;
        }
        .icon12{
            display: inline-block;
            width: 6px;
            height: 6px;
            line-height: 6px;
            border-radius: 6px;
            background-color: #0fffb8;
            margin: 0 4px 0 4px;
        }
        .text111{
            width: 45%;
            color: #c0e7fe;
            text-align: left;
        }
        .text112{
            width: 40%;
            color: #0fffb8;
            text-align: right;
        }
        .text113{
            width: 40%;
            color: #c0e7fe;
            text-align: right;
        }
        .text114{
            width: 15%;
            color: #c0e7fe;
            text-align: left;
        }
        .wraper1003{
            margin: 6px 0;
            font-size: 10px;
        }
        .wraper1004{
            margin: 6px 0;
            font-size: 10px;
        }
        .btn11{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.7;width: 150px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn11x{
            color: #ffffff;
            cursor: pointer;
            position: absolute;opacity:0.7;width: 12%;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            background: url("__PUBLIC__/img/bgk.png") no-repeat;
            height:9%;
        }
        .newbtn11{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.7;width: 25%;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            height:25%;
            top:75%;
            background: url("__PUBLIC__/img/bgk.png") no-repeat;
            background-size: 100% 100%;
        }
        .btn12{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #217db9;opacity:0.8;width: 100px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn13{
            color: #ffffff;
            cursor: pointer;
            position: absolute;background-color: #268ed4;opacity:0.9;width: 240px;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
        }
        .btn14{
            color: #ffffff;
            position: absolute;background-color: #268ed4;opacity:0.6;padding: 5px;border-radius: 2px;border: solid 1px #2988cb;
            padding-left: 6px;
            padding-right: 6px;
        }

        ::-webkit-scrollbar-track {
            background-color: #F5F5F5;
        }

        ::-webkit-scrollbar {
            width: 4px;
            background-color: #F5F5F5;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #999;
        }

        .myscroll{
            overflow-y: scroll;
            height: 70%;
            padding: 0 6px;
            color:#FFFFFF;
        }
        .myscroll::-webkit-scrollbar{ //设置整个滚动条宽高
            background-color: #F5F5F5;
            width: 0!important;
        }
        .myscroll::-webkit-scrollbar-thumb{ //设置滑块
            background: rgba(0,0,0,0.0)!important;
        }
        .myscroll::-webkit-scrollbar-track
        {
            background: rgba(0,0,0,0.0)!important;
        }
        body{
            background: url("__PUBLIC__/img/bag.png") no-repeat;
            background-size:100% 100%;
        }
    </style>
</head>
<body style="height: auto;min-height:100%">
<div class="wraper1">
    <div class="wraper2">
        <div class="wraper3">
            <div class="header clearfix">
                <div class="fl" style="width: 30%;text-align: left;">
                    <div id="" style="font-size: 12px;margin:10px 0 0 15px;display: inline-block;"></div>
                </div>
                <div class="fl" style="width: 40%;padding-top:1%;font-size: 26px;font-weight: bold;color:#85C7DA">积富回收管理后台</div>
                <div class="fl" style="width: 30%;text-align: right;height: 30px;line-height: 30px;padding: 5px 15px 0 0;">
                    <div id="goto_mgr" style="display: inline-block;width: 100px;">
                        <!-- <div style="display: inline-block"><img src="__PUBLIC__/img/mgr.png" style="height: 24px;cursor: pointer;"></div>
                        <div style="display: inline-block;font-size: 12px;line-height: 30px;height: 30px;cursor: pointer;">管理</div> -->
                    </div>
                    <eq name="backurlflag" value="1">
                    <div id=""  style="display: inline-block;width: 100px;">
                        <button id="goback" onclick="javascript:window.location.href='{$backurl}'" class="btn btn-warning btn-sm" style="width: 90px;" type="button">返回</button>
                    </div>
                    </eq>
                </div>
            </div>
            <div class="content1 clearfix">
                <div class="fl" style="width: 25%;height: 100%;">
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title"><div class="icon11">&nbsp;</div>垃圾分类参与人数统计</div>
                                <div id="canyurenshu" style="width: 90%;height: 80%;color:white"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>垃圾分类参与次数统计</div>
                                <div id="toufangcishu" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>全国垃圾分类社区违法统计</div>
                                <div class='clearfix wraper1003'><div class='fl text114'>排名</div><div class='fl text111'>小区</div><div class='fr text113'>违法次数</div></div>
                                <div id="shequweifatongji" class="myscroll"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fl" style="width: 50%;height: 100%;">
                    <div style="width: 100%;height: 65.5%;position: relative">
                        <div style="width:100%;height:100%;" id="map">
                        </div>
                        <!--  -->
                        <div class="btn11x" style="top:10px;">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">用户总数（人）</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">652</div>
                        </div>
                        <div class="btn11x" style="top:10px;left: 13%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">参与人数（人）</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">200</div>
                        </div>
                        <div class="btn11x" style="top:10px;left: 26%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">参与率</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">30%</div>
                        </div>
                         <div class="btn11x" style="top:10px;left: 39%">
                            <div style="width:100%;width:50%line-height:25px;text-align:center">正确率</div>
                            <div style="width:100%;width:50%line-height:25px;text-align:center">98%</div>
                         </div>
                        <!-- <div class="btn11x" style="top:10px;left: 3%"></div>
                        <div class="btn11x" style="top:10px;left: 3%"></div> -->
                        <!-- <div class="btn11" style="top:10px;left: 10px;">设备总数：<span id="totaldev"></span></div>
                        <div class="btn11" style="top:10px;left: 150px;">在线设备：<span id="onlinedev"></span></div> -->
                        <div class="newbtn11">
                            <div style="width:94%;float:left;margin-left:3%;height:20%;line-height:25px;text-align:left">设备详情</div>
                            <!--  -->
                            <div style="width:94%;float:left;margin-left:3%;height:45%;line-height:55px;font-size:18px;font-weight:bold">设备总数：<span id="totaldev"></span></div>
                            <!--  -->
                            <div style="width:47%;float:left;margin-left:3%;height:29%;font-size:15px;">
                                <div style="width:100%;height:50%;text-align: center;"><span id="onlinedev"></span></div>
                                <div style="width:100%;height:50%;text-align: center;">在线设备</div>
                            </div>
                            <div style="width:47%;float:left;margin-left:3%;height:29%;font-size:15px;">
                                <div style="width:100%;height:50%;text-align: center;">125台</div>
                                <div style="width:100%;height:50%;text-align: center;">离线设备</div>
                            </div>
                        </div>

                        <div id="markerinfo" class="btn13" style="top:50px;left: 10px;display: none;">
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">设备名称:</div><div id="devname" class="fr" style="width: 65%;text-align: right;"></div></div>
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">设备编码:</div><div id="devcode" class="fr" style="width: 65%;text-align: right;"></div></div>
                            <div class="clearfix_my"><div class="fl" style="width: 35%;text-align: left;">在线状态:</div><div id="onlinename" class="fr" style="width: 65%;text-align: right;"></div></div>
                        </div>
                    </div>
                    <div class="space1"></div>
                    <div style="width: 100%;height: 32%;">
                        <div class="module-wraper1 fl" style="width: 50%;">
                            <div class="module" style="margin: 0 2% 0 0;width: 98%;">
                                <div class="module-content">
                                    <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>垃圾不分类失信人</div>
                                    <div class='clearfix wraper1003' style="color: #c0e7fe;padding: 0 7px;"><div class='fl' style="width: 40%;text-align: left">用户名</div><div class='fr' style="width: 60%;text-align: right">小区</div></div>
                                    <div id="shixinren" class="myscroll"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-wraper1 fr" style="width: 50%;">
                            <div class="module" style="margin: 0 0 0 2%;width: 98%;">
                                <div class="module-content">
                                    <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>垃圾总量控制不达标社区</div>
                                    <div class='clearfix wraper1003' style="color: #c0e7fe;padding: 0 7px;"><div class='fl' style="width: 40%;text-align: left">小区</div><div class='fr' style="width: 60%;text-align: right">本月垃圾量</div></div>
                                    <div id="zongliangbudabiao" class="myscroll"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="fl" style="width: 25%;height: 100%;">
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title"><div class="icon11">&nbsp;</div>各类垃圾总量实况</div>
                                <div class="clearfix" style="margin: 20px 0;font-size: 14px;height:100%;">
                                    <div class="fl" style="display:none;width: 50%;height:100%;color: #ffffff;">
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #0fffb8;">&nbsp;</div>厨余垃圾</div>
                                            <div class="fr" class="text112">1456</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #03ace9;">&nbsp;</div>塑料类</div>
                                            <div class="fr" class="text112">654</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #4659f0;">&nbsp;</div>纸张类</div>
                                            <div class="fr" class="text112">465</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #ac39c5;">&nbsp;</div>金属类</div>
                                            <div class="fr" class="text112">156</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #c53964;">&nbsp;</div>有害类</div>
                                            <div class="fr" class="text112">45</div>
                                        </div>
                                        <div class="clearfix wraper1004">
                                            <div class="fl text111"><div class="icon12" style="background-color: #cab556;">&nbsp;</div>织物类</div>
                                            <div class="fr" class="text112">33</div>
                                        </div>
                                    </div>
                                    <div class="fl" style="width: 100%;height:100%;color: #0fffb8;">
                                        <div id="fenleitongji" style="width: 100%;height: 85%;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content">
                                <div class="module-title"><div class="icon11">&nbsp;</div>总量变化</div>
                                <div id="zongliangbianhua" style="width: 90%;height: 80%;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="space"></div>
                    <div class="module-wraper">
                        <div class="module">
                            <div class="module-content" style="padding: 16px 28px">
                                <div class="module-title" style="margin-bottom: 20px;"><div class="icon11">&nbsp;</div>各社区垃圾量排名</div>
                                <div class='clearfix wraper1003'><div class='fl text114'>排名</div><div class='fl text111'>小区</div><div class='fr text113'>垃圾量</div></div>
                                <div id="shequlajipaiming" class="myscroll"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<script src="__PUBLIC__/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="__PUBLIC__/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
<script src="__PUBLIC__/js/plugins/layer/layer.min.js"></script>
<script src="__PUBLIC__/js/hplus.js?v=4.1.0"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script type="text/javascript" src="__PUBLIC__/js/contabs.js"></script>
<script type="text/javascript" src="__PUBLIC__/js/my.js"></script>
<script type="text/javascript" src="__PUBLIC__/js/common.js"></script>
<script type="text/javascript" src="__PUBLIC__/js/echarts.min.js"></script>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=5wyVAcV0Kr2V4OUGpOM4WPOibz2gQ8rS&s=1"></script>

<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@4/map/js/china.js"></script>
<script>
    var markerArr;
    $(document).ready(function(){
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#goto_mgr").on("click",function(){
            window.location.href = "__MODULE__/Main/index";
        });
        //标注点数组
        markerArr=new Array();
        postjson("__CONTROLLER__/api_getDevlist",null,function (data) {
            if(data){
                markerArr.length=0;
                data.forEach(function (value,index,array) {
                    // console.log(value)
                    markerArr.push({title:null,content:value.name,point:value.lng+"|"+value.lat,isOpen:0,icon:{w:23,h:25,l:46,t:21,x:9,lb:12},devid:value.id,icontype:value.online});
                })
                initMap();//创建和初始化地图
            }
        })
        regreshTime();
        setInterval("regreshTime()",1000*60);
        initCanyurenshu();
        initToufangcishu();
        initFenleitongji();
        initZongliangbianhua();
        initShequlajipaiming();
        initData();
        initShequweifatongji();
        initShixinren();
        initZongliangbudabiao();
        //initMap();

    });
    
    function regreshTime() {
        postjson("__CONTROLLER__/getDashboardTime",null,function (data) {
            $('#currenttime').text(data);
        })
    }

    function initData() {
        postjson("__CONTROLLER__/getDashboardData",null,function (data) {
            $('#totaldev').text(data.totaldev);
            $('#onlinedev').text(data.onlinedev);
        })
    }

    function initCanyurenshu(){
        postjson("getCanyurenshu",null,function (data) {
            if(!data){
                return;
            }
            var myChart = echarts.init(document.getElementById("canyurenshu"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                    
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                    
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'bar',
                    barWidth: 10,
                    itemStyle:{
                        normal:{
                            color:'#50eae0'
                        }
                    },
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };

            myChart.setOption(option);
        })
    }

    function initToufangcishu(){
        postjson("getToufangcishu",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("toufangcishu"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'bar',
                    barWidth: 10,
                    itemStyle:{
                        normal:{
                            color:'#50eae0'
                        }
                    },
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };

            myChart.setOption(option);
        });
    }

    function initFenleitongji(){
        postjson("getFenleitongji",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("fenleitongji"));
            var option = {
                tooltip : {
                    trigger: 'item',
                    formatter: "{b} : {c} ({d}%)"
                },
                legend: {
                    orient: 'vertical',
                    x: 'left',
                    data:data.rubbishtypenamelist,
                    show:true,
                    itemWidth:10,
                    itemHeight:10,
                    textStyle:{
                        fontSize: 12,
                        color:'#ffffff',
                    }
                },
                series: [
                    {
                        name:'垃圾分类',
                        type:'pie',
                        radius: ['40%', '60%'],
                        center: ['70%', '43%'],
                        avoidLabelOverlap: false,
                        label: {
                            show : false,
                        },
                        // 设置值域的那指向线
                        labelLine: {
                            normal: {
                                show: false   // show设置线是否显示，默认为true，可选值：true ¦ false
                            }
                        },
                        data:data.valuelist
                    }
                ]
            };
            myChart.setOption(option);
        });
    }

    function initZongliangbianhua() {
        postjson("getZongliangbianhua",null,function (data) {
            if (!data) {
                return;
            }
            var myChart = echarts.init(document.getElementById("zongliangbianhua"));
            var option = {
                xAxis: {
                    type: 'category',
                    data: data.monthlist,
                    splitLine:{
                        show:false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    splitLine:{
                        show:true,
                        lineStyle:{
                            color: ['#27495f'],
                            width: 1,
                            type: 'solid'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#FFFFFF'
                        }
                    },
                },
                tooltip : {
                    trigger: 'axis'
                },
                series: [{
                    data: data.valuelist,
                    type: 'line',
                    smooth:true,    //曲线平滑
                    itemStyle : {
                        normal : {
                            areaStyle : {
                                type : 'default',
                                //渐变色实现
                                color : new echarts.graphic.LinearGradient(0, 0, 0, 1,//变化度
                                        //三种由深及浅的颜色
                                        [ {
                                            offset : 0,
                                            color : '#23c94b'
                                        }, {
                                            offset : 0.5,
                                            color : '#1a6686'
                                        }, {
                                            offset : 1,
                                            color : '#12418e'
                                        } ]),
                            },
                            lineStyle : {  //线的颜色
                                color : '#349e85'
                            },
                            //以及在折线图每个日期点顶端显示数字
                            label: {
                                show: true,
                                position: 'top',
                                textStyle: {
                                    color: 'white'
                                }
                            }
                        },
                    },
                    areaStyle: {normal: {}},
                }],
                grid:{
                    x:50,
                    y:30,
                    x2:5,
                    y2:20,
                    borderWidth:0
                }
            };
            myChart.setOption(option);
        });
    }

    function initShequlajipaiming() {
        postjson("getShequlajipaiming",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                    html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text114'>";
                }else{
                    html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text114'>";// 奇数  
                }
                
                html+=data[i].index;
                html+="</div><div class='fl' class='text111'>";
                html+=data[i].propertyname;
                html+="</div><div class='fr' class='text113'>";
                html+=data[i].weight;
                html+="kg</div></div>";
            }
            $('#shequlajipaiming').html(html);
        });
    }

    function initShequweifatongji() {
        postjson("getShequweifatongji",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                   html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text114'>";
                }else{
                   html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text114'>"; 
                }
                
                html+=data[i].index;
                html+="</div><div class='fl' class='text111'>";
                html+=data[i].propertyname;
                html+="</div><div class='fr' class='text113'>";
                html+=data[i].num;
                html+="</div></div>";
            }
            $('#shequweifatongji').html(html);
        });
    }

    function initShixinren() {
        postjson("getShixinren",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                  html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text111'>";
                }else{
                  html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text111'>";
                }
                
                html+=data[i].name;
                html+="</div><div class='fr' class='text112'>";
                html+=data[i].propertyname;
                html+="</div></div>";
            }
            $('#shixinren').html(html);
        });
    }

    function initZongliangbudabiao() {
        postjson("getZongliangbudabiao",null,function (data) {
            if (!data) {
                return;
            }
            var html="";
            for(var i=0;i<data.length;i++){
                if( i%2 == 0 ){
                  html+="<div style='background-color:#076FA6' class='clearfix wraper1003'><div class='fl text111'>";
                }else{
                  html+="<div style='background-color:#053C8E' class='clearfix wraper1003'><div class='fl text111'>";
                }
                
                html+=data[i].name;
                html+="</div><div class='fr' class='text112'>";
                html+=data[i].weight;
                html+="kg</div></div>";
            }
            $('#zongliangbudabiao').html(html);
        });
    }

    //创建和初始化地图函数：
    function initMap(){
        createMap();//创建地图
        setMapEvent();//设置地图事件
        addMapControl();//向地图添加控件
       addMarker();//向地图中添加marker
    }

    //创建地图函数：
    // function createMap(){
    //     var map = new BMap.Map("map");//在百度地图容器中创建一个地图
    //     var point = new BMap.Point(109.236222,30.749918);//定义一个中心点坐标
    //     map.centerAndZoom(point,5);//设定地图的中心点和坐标并将地图显示在地图容器中
    //     map.setMapStyle({
    //         styleJson:[
    //             {
    //                 "featureType": "water",
    //                 "elementType": "all",
    //                 "stylers": {
    //                     "color": "#021019"
    //                 }
    //             },
    //             {
    //                 "featureType": "highway",
    //                 "elementType": "geometry.fill",
    //                 "stylers": {
    //                     "color": "#9e7d60ff"
    //                 }
    //             },
    //             {
    //                 "featureType": "highway",
    //                 "elementType": "geometry.stroke",
    //                 "stylers": {
    //                     "color": "#184a92ff"
    //                 }
    //             },
    //             {
    //                 "featureType": "arterial",
    //                 "elementType": "geometry.fill",
    //                 "stylers": {
    //                     "color": "#38414eff"
    //                 }
    //             },
    //             {
    //                 "featureType": "arterial",
    //                 "elementType": "geometry.stroke",
    //                 "stylers": {
    //                     "color": "#554631fa"
    //                 }
    //             },
    //             {
    //                 "featureType": "local",
    //                 "elementType": "geometry",
    //                 "stylers": {
    //                     "color": "#000000"
    //                 }
    //             },
    //             {
    //                 "featureType": "land",
    //                 "elementType": "all",
    //                 "stylers": {
    //                     "color": "#1b57abff"
    //                 }
    //             },
    //             {
    //                 "featureType": "railway",
    //                 "elementType": "geometry.fill",
    //                 "stylers": {
    //                     "visibility": "off",
    //                     "color": "#000000ff"
    //                 }
    //             },
    //             {
    //                 "featureType": "railway",
    //                 "elementType": "geometry.stroke",
    //                 "stylers": {
    //                     "visibility": "off",
    //                     "color": "#08304b"
    //                 }
    //             },
    //             {
    //                 "featureType": "subway",
    //                 "elementType": "geometry",
    //                 "stylers": {
    //                     "lightness": -70
    //                 }
    //             },
    //             {
    //                 "featureType": "building",
    //                 "elementType": "geometry.fill",
    //                 "stylers": {
    //                     "color": "#000000"
    //                 }
    //             },
    //             {
    //                 "featureType": "all",
    //                 "elementType": "labels.text.fill",
    //                 "stylers": {
    //                     "color": "#857f7f"
    //                 }
    //             },
    //             {
    //                 "featureType": "all",
    //                 "elementType": "labels.text.stroke",
    //                 "stylers": {
    //                     "color": "#000000"
    //                 }
    //             },
    //             {
    //                 "featureType": "building",
    //                 "elementType": "geometry",
    //                 "stylers": {
    //                     "color": "#022338"
    //                 }
    //             },
    //             {
    //                 "featureType": "green",
    //                 "elementType": "geometry",
    //                 "stylers": {
    //                     "color": "#263b3eff"
    //                 }
    //             },
    //             {
    //                 "featureType": "boundary",
    //                 "elementType": "all",
    //                 "stylers": {
    //                     "color": "#8c8a8a"
    //                 }
    //             },
    //             {
    //                 "featureType": "manmade",
    //                 "elementType": "geometry",
    //                 "stylers": {
    //                     "color": "#184a92ff"
    //                 }
    //             },
    //             {
    //                 "featureType": "poi",
    //                 "elementType": "all",
    //                 "stylers": {
    //                     "visibility": "off"
    //                 }
    //             },
    //             {
    //                 "featureType": "all",
    //                 "elementType": "labels.icon",
    //                 "stylers": {
    //                     "visibility": "off"
    //                 }
    //             },
    //             {
    //                 "featureType": "all",
    //                 "elementType": "labels.text.fill",
    //                 "stylers": {
    //                     "color": "#d69563",
    //                     "visibility": "on"
    //                 }
    //             }
    //         ]
    //     });
    //     window.map = map;//将map变量存储在全局
    // }
    function createMap(){
        var dom = document.getElementById("map");
        var myChart = echarts.init(dom);
        var app = {};
        option = null;
        option = {
            
            tooltip : {
                trigger: 'item'
            },
            
            series : [
                {
                    name: '设备',
                    type: 'map',
                    mapType: 'china',
                    roam: false,
                    label: {
                        normal: {
                            show: false
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    itemStyle: {
                        normal:{
                            borderWidth:2,//边框大小
                            borderColor:'#6A7EBF',
                            areaColor: '#002A56',//背景颜色
                            label: {
                                show: true//显示名称
                            }
                        },
                        emphasis: {
                            borderWidth:2,
                            areaColor: '#08EAFE',
                            label: {
                                show: true,
                                textStyle: {
                                    color: '#fff'
                                }
                            }
                         }    
                    },

                    data:[
                        {name: '北京',value: 1},
                        {name: '天津',value: 0},
                        {name: '上海',value: 0},
                        {name: '重庆',value: 0},
                        {name: '河北',value: 0},
                        {name: '河南',value: 0},
                        {name: '云南',value: 0},
                        {name: '辽宁',value: 0},
                        {name: '黑龙江',value: 0},
                        {name: '湖南',value: 0},
                        {name: '安徽',value: 2},
                        {name: '山东',value: 3},
                        {name: '新疆',value: 0},
                        {name: '江苏',value: 12},
                        {name: '浙江',value: 0},
                        {name: '江西',value: 0},
                        {name: '湖北',value: 0},
                        {name: '广西',value: 0},
                        {name: '甘肃',value: 0},
                        {name: '山西',value: 0},
                        {name: '内蒙古',value: 0},
                        {name: '陕西',value: 0},
                        {name: '吉林',value: 0},
                        {name: '福建',value: 0},
                        {name: '贵州',value: 0},
                        {name: '广东',value: 0},
                        {name: '青海',value: 0},
                        {name: '西藏',value: 0},
                        {name: '四川',value: 0},
                        {name: '宁夏',value: 0},
                        {name: '海南',value: 0},
                        {name: '台湾',value: 0},
                        {name: '香港',value: 0},
                        {name: '澳门',value: 0}
                    ]
                }
            ]
        };;
        if (option && typeof option === "object") {
            myChart.setOption(option, true);
        }
    }

    //地图事件设置函数：
    function setMapEvent(){
        map.enableDragging();//启用地图拖拽事件，默认启用(可不写)
        map.enableScrollWheelZoom();//启用地图滚轮放大缩小
        map.enableDoubleClickZoom();//启用鼠标双击放大，默认启用(可不写)
        map.enableKeyboard();//启用键盘上下左右键移动地图
        map.addEventListener("click", function(e){
            $('#markerinfo').css('display','none');
        });
    }

    //地图控件添加函数：
    function addMapControl(){
        //向地图中添加缩放控件
        // var ctrl_nav = new BMap.NavigationControl({anchor:BMAP_ANCHOR_TOP_LEFT,type:BMAP_NAVIGATION_CONTROL_SMALL});
        // map.addControl(ctrl_nav);
    }


    //创建marker
    function addMarker(){
        var mypoints = [];//坐标点,用于计算中心点和缩放比例
        for(var i=0;i<markerArr.length;i++){
            var json = markerArr[i];
            var p0 = json.point.split("|")[0];
            var p1 = json.point.split("|")[1];
            var point = new BMap.Point(p0,p1);
            mypoints.push(point);
            var iconImg = createIcon(json.icontype);
            var marker = new BMap.Marker(point,{icon:iconImg});
            var iw = createInfoWindow(i);
            var label = new BMap.Label(json.title,{"offset":new BMap.Size(json.icon.lb-json.icon.x+10,-20)});
            var devid=json.devid;
            //marker.setLabel(label);
            map.addOverlay(marker);
            label.setStyle({
                borderColor:"#808080",
                color:"#333",
                cursor:"pointer"
            });

            (function(){
                var index = i;
                var _iw = createInfoWindow(i);
                var _marker = marker;
                var _devid=devid;
                _marker.addEventListener("click",function(){
                    var req={
                        id:_devid
                    }
                    postjson("__CONTROLLER__/getDevInfo",req,function (data) {
                        if(!data){
                            return;
                        }
                        $('#markerinfo').css('display','block');
                        $('#devname').text(data.name);
                        $('#devcode').text(data.code);
                        $('#onlinename').text(data.onlinename);
                    })
                });
                _iw.addEventListener("open",function(){
                    // _marker.getLabel().hide();
                })
                _iw.addEventListener("close",function(){
                    // _marker.getLabel().show();
                })
                label.addEventListener("click",function(){
                    // _marker.openInfoWindow(_iw);
                })
                if(!!json.isOpen){
                    label.hide();
                    _marker.openInfoWindow(_iw);
                }
            })()
        }
        var view = map.getViewport(mypoints);// 获取所有坐标中心点以及缩放比例
        map.setZoom(view.zoom-1);// 设置缩放比例
        map.setCenter(view.center);// 设置中心点
    }
    //创建InfoWindow
    function createInfoWindow(i){
        var json = markerArr[i];
        var iw = new BMap.InfoWindow("<div class='iw_poi_content'>"+json.content+"</div>");
        return iw;
    }
    //创建一个Icon
    function createIcon(icontype){
        // var icon = new BMap.Icon("__PUBLIC__/img/position.png", new BMap.Size(json.w,json.h),{imageOffset: new BMap.Size(-json.l,-json.t),infoWindowOffset:new BMap.Size(json.lb+5,1),offset:new BMap.Size(json.x,json.h)})
        if(icontype==1){
            var filepath="__PUBLIC__/img/devon1.png";
            var myIcon = new BMap.Icon(filepath, new BMap.Size(24, 24), {
                offset: new BMap.Size(0, 0), // 指定定位位置
                imageOffset: new BMap.Size(0, 0) // 设置图片偏移
            });
            return myIcon;
        }else{
            var filepath="__PUBLIC__/img/devoff1.png";
            var myIcon = new BMap.Icon(filepath, new BMap.Size(24, 24), {
                offset: new BMap.Size(0, 0), // 指定定位位置
                imageOffset: new BMap.Size(0, 0) // 设置图片偏移
            });
            return myIcon;
        }

    }

</script>
</body>
</html>