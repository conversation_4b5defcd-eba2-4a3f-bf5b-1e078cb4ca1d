<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class SolveXSMgrController extends CommonController {
    public function index(){
        if(I('wkotype')==1){
            $this->assign('privilege',$this->getPrivilege(15));
        }else if(I('wkotype')==2){
            $this->assign('privilege',$this->getPrivilege(16));
        }else{
            $this->assign('privilege',$this->getPrivilege(17));
        }
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->display();
    }

    public function add(){
        $this->assign("wkotype",I('wkotype'));
        $this->assign("wkotypename",CommonDao::getWotypeName(I('wkotype')));
        $this->display();
    }

    public function update(){
        $woid=I('woid');
        if(empty($woid)){
            return;
        }
        $solve=M('solve');
        $result=$solve->where("woid='".$woid."'")->find();
        if(empty($result)){
            return;
        }
        $result=$this->makeInfo($result);
        $this->assign("wkotypename",CommonDao::getWotypeName($result['wotype']));
        $this->assign("statusList",CommonDao::getAllSolveStatus());
        $this->assign("levelList",CommonDao::getAllSolveLevel());
        $this->assign("userList",CommonDao::getAllUser());
        $this->assign("solve",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $wkotype=$request["wkotype"];
//        $solvename=$request["solvename"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        if(empty($wkotype)){
            $this->output_commonerror('wkotype不能为空');
            return;
        }
        $solve=M('solve');
        $res['total']=$solve->where("wotype=".$wkotype)->count("woid");
//        $cond['roleid']=2;
        $result=$solve->where("wotype=".$wkotype)->order('woid desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            foreach ($result as &$result_elem){
                $result_elem=$this->makeInfo($result_elem);
            }
        }
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($elem){
        $station=M('station');
        $user=M('user');
        $stationid=$elem['stationid'];
        if(!empty($stationid)){
            $stationdata=$station->where("stationid=".$stationid)->find();
            $elem['stationname']=$stationdata['name'];
        }
        $solveuserid=$elem['solveuserid'];
        if(!empty($solveuserid)){
            $userdata=$user->where("userid=".$solveuserid)->find();
            $elem['solveuseridname']=$userdata['name'];
        }
        $elem['statusname']=CommonDao::getSolveStatusName($elem['status']);
        $elem['levelname']=CommonDao::getSolveLevelName($elem['level']);

        $createtime=$elem['createtime'];
        if(!empty($createtime)){
            $elem['createtime']=date('Y-m-d H:i:s',$createtime);
        }
        return $elem;
    }

//    public function api_add(){
//        $request = file_get_contents('php://input');
//        $request = json_decode($request,true);
//        stationid=$request["stationid"];
//        $nickname=$request["nickname"];
//        $solvepwd=$request["solvepwd"];
//        $roleid=$request["roleid"];
//        $wkotype=$request["wkotype"];
//        if(empty($account)){
//            $this->output_commonerror('账号不能为空');
//            return;
//        }
//        if(empty($solvepwd)){
//            $this->output_commonerror('密码不能为空');
//            return;
//        }
//        if(empty($roleid)){
//            $this->output_commonerror('角色不能为空');
//            return;
//        }
//        if(empty($wkotype)){
//            $this->output_commonerror('wkotype不能为空');
//            return;
//        }
//        $solve=M('solve');
//        $result=$solve->where("account='".$account."'")->select();
//        if(!empty($result)){
//            $this->output_commonerror('账号已存在');
//            return;
//        }
//        $nickname=empty($nickname)?$account:$nickname;
//        $sex=1;
//
//        $solve=M('solve');
//        $data['account']=$account;
//        $data['nickname']=$nickname;
//        $data['solvepwd']=$solvepwd;
//        $data['roleid']=$roleid;
//        $data['status']=1;
//        $data['sex']=$sex;
//        $time=date('Y-m-d h:i:s',time());
//        $data['createtime']=$time;
//        $data['updatetime']=$time;
//        $solve->add($data);
//        $this->output_data();
//        return;
//    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woid=$request["woid"];
        $status=$request["status"];
        $level=$request["level"];
        $reason=$request["reason"];
        $solveuserid=$request["solveuserid"];
        if(empty($woid)){
            $this->output_commonerror('ID不能为空');
            return;
        }

        $solve=M('solve');
        $result=$solve->where("woid=".$woid)->select();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }
        $user=M('user');
        $userresult=$user->where("userid=".$solveuserid)->select();
        if(empty($userresult)){
            $this->output_commonerror('用户不存在');
            return;
        }

        $solve=M('solve');
        $data['status']=$status;
        $data['level']=$level;
        $data['reason']=$reason;
        $data['solveuserid']=$solveuserid;
        $solve->where('woid='.$woid)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $woidlist=$request["woidlist"];
        if(empty($woidlist)){
            $this->output_data();
            return;
        }
        $solve=M('solve');
        foreach ($woidlist as $woid){
            $solve->where('woid='.$woid)->delete();
        }
        $this->output_data();
        return;
    }
}