<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class WoJCMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $userid=I('userid');
        if(empty($userid)){
            return;
        }
        $user=M('user');
        $result=$user->where("userid='".$userid."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("user",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $username=$request["username"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $user=M('user');
        $res['total']=$user->count("userid");
//        $cond['roleid']=2;
        $result=$user->order('userid desc')->limit($offset,$limit)->select();
        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $account=$request["account"];
        $nickname=$request["nickname"];
        $userpwd=$request["userpwd"];
        $roleid=$request["roleid"];
//        $sex=$request["sex"];
        if(empty($account)){
            $this->output_commonerror('账号不能为空');
            return;
        }
        if(empty($userpwd)){
            $this->output_commonerror('密码不能为空');
            return;
        }
        if(empty($roleid)){
            $this->output_commonerror('角色不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."'")->select();
        if(!empty($result)){
            $this->output_commonerror('账号已存在');
            return;
        }
        $nickname=empty($nickname)?$account:$nickname;
        $sex=1;

        $user=M('user');
        $data['account']=$account;
        $data['nickname']=$nickname;
        $data['userpwd']=$userpwd;
        $data['roleid']=$roleid;
        $data['status']=1;
        $data['sex']=$sex;
        $time=date('Y-m-d h:i:s',time());
        $data['createtime']=$time;
        $data['updatetime']=$time;
        $user->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $userid=$request["userid"];
        $account=$request["account"];
        $nickname=$request["nickname"];
        $userpwd=$request["userpwd"];
        $roleid=$request["roleid"];
        $status=$request["status"];
        if(empty($userid)){
            $this->output_commonerror('用户ID不能为空');
            return;
        }
        if(empty($account)){
            $this->output_commonerror('用户账号不能为空');
            return;
        }
        if(empty($nickname)){
            $this->output_commonerror('昵称不能为空');
            return;
        }
        if(empty($roleid)){
            $this->output_commonerror('角色不能为空');
            return;
        }
        if(empty($status)){
            $this->output_commonerror('状态不能为空');
            return;
        }
        $user=M('user');
        $result=$user->where("userid=".$userid)->select();
        if(empty($result)){
            $this->output_commonerror('用户ID不存在');
            return;
        }
        $user=M('user');
        $result=$user->where("account='".$account."' and userid!=".$userid)->select();
        if(!empty($result)){
            $this->output_commonerror('用户账号已存在');
            return;
        }

        $user=M('user');
        $data['account']=$account;
        $data['nickname']=$nickname;
        if(!empty($userpwd)){
            $data['userpwd']=$userpwd;
        }
        $data['roleid']=$roleid;
        $data['status']=$status;
        $time=date('Y-m-d h:i:s',time());
        $data['updatetime']=$time;
        $user->where('userid='.$userid)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $useridlist=$request["useridlist"];
        if(empty($useridlist)){
            $this->output_data();
            return;
        }
        $user=M('user');
        foreach ($useridlist as $userid){
            $user->where('userid='.$userid)->delete();
        }
        $this->output_data();
        return;
    }
}