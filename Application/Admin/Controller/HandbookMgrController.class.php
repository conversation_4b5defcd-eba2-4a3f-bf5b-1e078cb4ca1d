<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class HandbookMgrController extends CommonController {
    public function index(){
        $this->assign('privilege',$this->getPrivilege(7));
        $this->display();
    }

    public function add(){
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $handbook=M('handbook');
        $result=$handbook->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("handbook",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $handbookname=$request["handbookname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $handbook=M('handbook');
        $res['total']=$handbook->count("id");
//        $cond['roleid']=2;
        $result=$handbook->order('id desc')->limit($offset,$limit)->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]['updatetime']=date('Y-m-d h:i:s',$result[$i]['updatetime']);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $title=$request["title"];
        $content=$request["content"];
        if(empty($title)){
            $this->output_commonerror('标题不能为空');
            return;
        }
        if(empty($content)){
            $this->output_commonerror('内容不能为空');
            return;
        }
        $handbook=M('handbook');
        $result=$handbook->where("title='".$title."'")->select();
        if(!empty($result)){
            $this->output_commonerror('标题已存在');
            return;
        }

        $handbook=M('handbook');
        $data['title']=$title;
        $data['content']=$content;
//        $time=date('Y-m-d h:i:s',time());
        $time=time();
        $data['updatetime']=$time;
        $handbook->add($data);
        $this->output_data();
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $title=$request["title"];
        $content=$request["content"];
        if(empty($id)){
            $this->output_commonerror('ID不能为空');
            return;
        }
        if(empty($title)){
            $this->output_commonerror('标题不能为空');
            return;
        }
        if(empty($content)){
            $this->output_commonerror('内容不能为空');
            return;
        }
        $handbook=M('handbook');
        $result=$handbook->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('ID不存在');
            return;
        }
        $handbook=M('handbook');
        $result=$handbook->where("title='".$title."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('标题已存在');
            return;
        }

        $handbook=M('handbook');
        $data['title']=$title;
        $data['content']=$content;
//        $time=date('Y-m-d h:i:s',time());
        $time=time();
        $data['updatetime']=$time;
        $handbook->where('id='.$id)->save($data);
        $this->output_data();
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $handbook=M('handbook');
        foreach ($idlist as $id){
            $handbook->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}