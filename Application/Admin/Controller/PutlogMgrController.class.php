<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
class PutlogMgrController extends CommonController {
    public function index(){
        $this->display();
    }

    public function detail(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $putlog=M('putlog');
        $result=$putlog->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("putlog",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
//        $putlogname=$request["putlogname"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }
        $putlog=M('putlog');
        $res['total']=$putlog->count("id");
        $result=$putlog->order('id desc')->limit($offset,$limit)->select();

        if(!empty($result)){
            $register=M('register');
            $dev=M('dev');
            for($i=0;$i<count($result);$i++){
                $result1=$register->where("userid=".$result[$i]['userid'])->find();
                if(!empty($result1)){
                    $result[$i]['account']=$result1['account'];
                    $result[$i]['nickname']=$result1['nickname'];
                }
                $result2=$dev->where("devid=".$result[$i]['devid'])->find();
                if(!empty($result2)){
                    $result[$i]['devcode']=$result2['devcode'];
                    $result[$i]['devname']=$result2['devname'];
                }
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data();
            return;
        }
        $putlog=M('putlog');
        foreach ($idlist as $id){
            $putlog->where('id='.$id)->delete();
        }
        $this->output_data();
        return;
    }
}