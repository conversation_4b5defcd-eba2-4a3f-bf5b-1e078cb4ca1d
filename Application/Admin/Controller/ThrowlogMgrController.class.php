<?php
namespace Admin\Controller;
use Admin\Common\CommonController;
use Common\Common\CommonDao;
class ThrowlogMgrController extends CommonController {
    public function index(){
        $this->assign("rubbishtypelist",CommonDao::getAllRubbishtype());
        $this->assign("violationflaglist",CommonDao::getAllViolationflag());

        $user=M('user');
        if(session('roleid')==1){
            $this->assign("search_operatorid_visible",1);
            $this->assign("search_propertyid_visible",1);
            $cond['roleid']=2;
            $operatorlist=$user->where($cond)->select();
            $this->assign("operatorlist",$operatorlist);
        }else if(session('roleid')==2){
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",1);
            $cond['operatoruserid']=session('userid');
            $propertylist=$user->where($cond)->select();
            $this->assign("propertylist",$propertylist);
        }else{
            $this->assign("search_operatorid_visible",0);
            $this->assign("search_propertyid_visible",0);
        }
        $this->display();
    }

    public function add(){
        $this->assign("rubbishtypelist",CommonDao::getAllRubbishtype());
        $user=M('user');
        $cond['roleid']=2;
        $operatorlist=$user->where($cond)->select();
        $this->assign("operatorlist",$operatorlist);
        $cond['roleid']=3;
        $propertylist=$user->where($cond)->select();
        $this->assign("propertylist",$propertylist);
        $this->display();
    }

    public function update(){
        $id=I('id');
        if(empty($id)){
            return;
        }
        $throwlog=M('throwlog');
        $result=$throwlog->where("id='".$id."'")->find();
        if(empty($result)){
            return;
        }
        $this->assign("modelvo",$result);
        $this->display();
    }

    public function api_getlist(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $offset=$request["offset"];
        $limit=$request["limit"];
        $rubbishtype =$request["rubbishtype"];
        $devcode     =$request["devcode"];
        $account     =$request["account"];
        $cardnum     =$request["cardnum"];
        $starttime   =$request["starttime"];
        $endtime     =$request["endtime"];
        $operatorid  =$request["operatorid"];
        $propertyid  =$request["propertyid"];
        $violationflag  =$request["violationflag"];
//        $name=$request["name"];
        if(empty($offset)){
            $offset=0;
        }
        if(empty($limit)){
            $limit=10;
        }

        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }

        if($violationflag>=0){
            $cond['violationflag']=$violationflag;
        }

        if(!empty($rubbishtype)){
            $cond['rubbishtype']=$rubbishtype;
        }
        if(!empty($devcode)){
            $cond['devcode']=array('like','%'.$devcode.'%');
        }
        if(!empty($account)){
            $cond['account']=array('like','%'.$account.'%');
        }
        if(!empty($cardnum)){
            $cond['cardnum']=array('like','%'.$cardnum.'%');
        }
        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }
        if(empty($starttime)){
            $starttime=time()-3600*24*90;
        }else{
            $starttime=strtotime($starttime);
        }
        if(empty($endtime)){
            $endtime=time()+100;
        }else{
            $endtime=strtotime($endtime);
        }
        $condtime=" throwtime >=".$starttime." and throwtime<".$endtime." ";
        $throwlog=M('throwlog');
        $res['total']=$throwlog->where($cond)->where($condtime)->count("id");
        $result=$throwlog->where($cond)->where($condtime)->order('id desc')->limit($offset,$limit)->select();
        $totalpoint=$throwlog->where($cond)->where($condtime)->sum("point");
        $totalweight=$throwlog->where($cond)->where($condtime)->sum("throwweight");
        $res['totalpoint']=$totalpoint;
        $res['totalweight']=$totalweight;

        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo($result[$i]);
            }
        }

        $res['rows']=$result;
        \Think\Log::record(json_encode($res),"INFO");
        echo json_encode($res);die;
        return;
    }

    private function makeInfo($result){
        $result['createtime']=CommonDao::time2str($result['createtime']);
        $result['throwtime']=CommonDao::time2str($result['throwtime']);
//        $dev=M('dev');
//        $devdata=$dev->where("code='".$result['devcode']."'")->find();
//        if(!empty($devdata)){
//            $result['devname']=$devdata['name'];
//        }

//        $register=M('register');
//        $registerdata=$register->where("id=".$result['userid'])->find();
//        if(!empty($registerdata)){
//            $result['account']=$registerdata['account'];
//        }
//
//        $card=M('card');
//        $carddata=$card->where("id=".$result['carduserid'])->find();
//        if(!empty($carddata)){
//            $result['cardnum']=$carddata['cardnumber'];
//        }

        $result['rubbishtypename']=CommonDao::getRubbishtypeName($result['rubbishtype']);
        $result['violationflagname']=CommonDao::getViolationflagname($result['violationflag']);
        return $result;
    }

    public function api_export(){
        $rubbishtype =I("rubbishtype");
        $devcode     =I("devcode");
        $account     =I("account");
        $cardnum     =I("cardnum");
        $starttime   =I("starttime");
        $endtime     =I("endtime");
        $operatorid  =I("operatorid");
        $propertyid  =I("propertyid");

        $cond=array();
        if(session('roleid')==2){
            $operatorid=session('userid');
            $cond['operatorid']=$operatorid;
        }else if(session('roleid')==3){
            $operatorid=session('operatoruserid');
            $propertyid=session('userid');
            $cond['propertyid']=$propertyid;
        }

        if(!empty($rubbishtype)){
            $cond['rubbishtype']=$rubbishtype;
        }
        if(!empty($devcode)){
            $cond['devcode']=array('like','%'.$devcode.'%');
        }
        if(!empty($account)){
            $cond['account']=array('like','%'.$account.'%');
        }
        if(!empty($cardnum)){
            $cond['cardnum']=array('like','%'.$cardnum.'%');
        }
        if(!empty($operatorid)){
            $cond['operatorid']=$operatorid;
        }
        if(!empty($propertyid)){
            $cond['propertyid']=$propertyid;
        }
        if(empty($starttime)){
            $starttime=time()-3600*24*90;
        }else{
            $starttime=strtotime($starttime);
        }
        if(empty($endtime)){
            $endtime=time()+100;
        }else{
            $endtime=strtotime($endtime);
        }
        $condtime=" throwtime >=".$starttime." and throwtime<".$endtime." ";

        $throwlog=M('throwlog');
        $result=$throwlog->where($cond)->where($condtime)->order('id desc')->field("id,devcode,devname,cardnum,account,point,throwweight,rubbishtype,throwtime")->select();
        if(!empty($result)){
            for($i=0;$i<count($result);$i++){
                $result[$i]=$this->makeInfo_exort($result[$i]);
            }
        }

        $file="投放记录";
        $title="投放记录";
        $subtitle='';
        $excelname="投放记录";
        $th_array=array("ID","设备编码","设备名称","卡号","微信用户","获得积分","重量或次数","垃圾类型","投放时间");
        $this->createExel($file,$title,$subtitle,$th_array,$result,$excelname);
    }

    private function makeInfo_exort($result){
        $result['createtime']=CommonDao::time2str($result['createtime']);
        $result['throwtime']=CommonDao::time2str($result['throwtime']);
        $result['rubbishtype']=CommonDao::getRubbishtypeName($result['rubbishtype']);
        return $result;
    }

    public function api_add(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $devcode=$request["devcode"];
        $point=$request["point"];
        $throwweight=$request["throwweight"];
        $rubbishtype=$request["rubbishtype"];
        $propertyid=$request["propertyid"];
        $operatorid=$request["operatorid"];
        if(empty($devcode)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($point)){
            $this->output_commonerror('积分不能为空');
            return;
        }
        if(empty($throwweight)){
            $this->output_commonerror('投递重量不能为空');
            return;
        }
        if(empty($rubbishtype)){
            $this->output_commonerror('垃圾类型不能为空');
            return;
        }
        if(empty($propertyid)){
            $this->output_commonerror('所属物业不能为空');
            return;
        }
        if(empty($operatorid)){
            $this->output_commonerror('所属运营商不能为空');
            return;
        }
        $roleid=session('roleid');
        if($roleid!=1){
            $this->output_commonerror('无权限操作');
            return;
        }
        $dev=M('dev');
        $devdata=$dev->where("code='".$devcode."'")->find();
        if(empty($devdata)){
            $this->output_commonerror('设备编码不存在');
            return;
        }
        $currenttime=time();

        $throwlog=M('throwlog');
        $data['devcode']=$devcode;
        $data['point']=$point;
        $data['throwtime']=$currenttime;
        $data['throwweight']=$throwweight;
        $data['throwmode']=0;
        $data['rubbishtype']=$rubbishtype;
        $data['propertyid']=$propertyid;
        $data['operatorid']=$operatorid;
        $data['devname']=$devdata['name'];
        $data['throwmonth']=date('Ym', $currenttime);
        $data['throwdate']=date('Ymd', $currenttime);
        $data['violationflag']=0;
        $data['throwtype']=0;
        $data['throwhour']=date("H",$currenttime);
        $data['createtime']=$currenttime;
        $throwlog->add($data);
        $this->output_data("");
        return;
    }

    public function api_update(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $id=$request["id"];
        $code=$request["code"];
        $name=$request["name"];
        $address=$request["address"];
//        $lat=$request["lat"];
//        $lng=$request["lng"];
        if(empty($id)){
            $this->output_commonerror('设备ID不能为空');
            return;
        }
        if(empty($code)){
            $this->output_commonerror('设备编码不能为空');
            return;
        }
        if(empty($name)){
            $this->output_commonerror('设备名称不能为空');
            return;
        }
        if(empty($address)){
            $this->output_commonerror('地址不能为空');
            return;
        }
//        if(empty($lat)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
//        if(empty($lng)){
//            $this->output_commonerror('位置不能为空');
//            return;
//        }
        $throwlog=M('throwlog');
        $result=$throwlog->where("id=".$id)->select();
        if(empty($result)){
            $this->output_commonerror('设备ID不存在');
            return;
        }
        $throwlog=M('throwlog');
        $result=$throwlog->where("code='".$code."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备编码已存在');
            return;
        }
        $throwlog=M('throwlog');
        $result=$throwlog->where("name='".$name."' and id!=".$id)->select();
        if(!empty($result)){
            $this->output_commonerror('设备名称已存在');
            return;
        }

        $throwlog=M('throwlog');
        $data['code']=$code;
        $data['name']=$name;
        $data['address']=$address;
//        $data['lat']=$lat;
//        $data['lng']=$lng;
        $throwlog->where('id='.$id)->save($data);
        $this->output_data("");
        return;
    }

    public function api_del(){
        $request = file_get_contents('php://input');
        $request = json_decode($request,true);
        $idlist=$request["idlist"];
        if(empty($idlist)){
            $this->output_data("");
            return;
        }
        $throwlog=M('throwlog');
        foreach ($idlist as $id){
            $throwlog->where('id='.$id)->delete();
        }
        $this->output_data("");
        return;
    }

    public function api_getPropertys(){
        $operatorid=I('operatorid');
        if(empty($operatorid)){
            $this->output_data("");
            return;
        }
        $user=M('user');
        $data=$user->where("operatoruserid=".$operatorid)->select();
        $this->output_data($data);
        return;
    }

    function createExel($file,$title,$subtitle,$array_th,$data,$excelname=""){
        Vendor ('PHPExcel');

        //创建一个读Excel模版的对象
        $objReader = \PHPExcel_IOFactory::createReader ( 'Excel5' );
//        $objReader = new \PHPExcel_Reader_Excel2007();
        $objPHPExcel = $objReader->load ("Public/template/template_exportthrowlog.xls" );
        //获取当前活动的表
        $objActSheet = $objPHPExcel->getActiveSheet ();
        $objActSheet->setTitle ($title);

        $objActSheet->setCellValue ( 'A1', $title );
//        $objActSheet->setCellValue ( 'A2', '导出时间：' . date ( 'Y-m-d H:i:s' ) );
//        $objActSheet->setCellValue ( 'F2', $subtitle);

        if($array_th==null)
        {
            $array_th=array_keys($data[0]);
        }

        foreach($array_th as $key=>$value)
        {
            $objActSheet->getCellByColumnAndRow($key,1)->setValue($value);
        }

        $baseRow = 2; //数据从N-1行开始往下输出  这里是避免头信息被覆盖
        foreach ( $data as $r => $dataRow )
        {
            $row = $baseRow + $r;
            //将数据填充到相对应的位置
            $arraykeys=array_keys($dataRow);//数组键值
            $keyscnt=count($arraykeys);
            foreach($arraykeys as $key=>$value)
            {
                $objPHPExcel->getActiveSheet ()->getCellByColumnAndRow($key,$row)->setValue($dataRow [$value]);
            }
        }
        $style_array = array(
            'borders' => array(
                'allborders' => array(
                    'style' => \PHPExcel_Style_Border::BORDER_THIN
                )
            ));
        $objPHPExcel->getActiveSheet()->getStyle('A1:I'.(count($data)+1))->applyFromArray($style_array);
        $objPHPExcel->getActiveSheet()->getColumnDimension()->setAutoSize(true);

        //$filename = $file;
        $filename = $excelname."_".time();

        header ( 'Content-Type: application/vnd.ms-excel' );
        header ( 'Content-Disposition: attachment;filename="' . $filename . '.xls"' ); //"'.$filename.'.xls"
        header ( 'Cache-Control: max-age=0' );
        ob_clean();
        flush();

        $objWriter = \PHPExcel_IOFactory::createWriter ( $objPHPExcel, 'Excel5' ); //在内存中准备一个excel2003文件
        $objWriter->save ( 'php://output' );

    }
}