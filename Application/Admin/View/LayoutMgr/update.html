<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>{$pagetitle}</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <style>
        .mysubwindow{
            position: absolute;top: 0;left: 0;
            border: solid 1px #aaaaaa;
        }
        .mysubwindow-selected{
            background-color: rgba(255, 99, 71,0.3);
        }
    </style>

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>{$pagetitle}</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button id="saveBtn" type="button" class="btn btn-primary" style="display: none">确定</button>
                        <button type="button" class="btn btn-white"
                                onclick="javascript:window.history.go(-1);">返回</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>
                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" readonly="readonly" class="form-control" value="{$modelvo.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">模板名称：</label>
                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" readonly="readonly" class="form-control" value="{$modelvo.name}">
                            </div>
                        </div>
                        <!--
                        <div class="form-group">
                            <label class="col-sm-2 control-label">屏幕宽高：</label>
                            <div class="col-sm-10">
                                <span>宽：</span>
                                <input id="screen_width" name="screen_width" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_width}">
                                <span>高：</span>
                                <input id="screen_height" name="screen_height" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_height}">
                            </div>
                        </div>-->
                        <div class="hr-line-dashed"></div>
                        <div class="form-group" style="height: 1920px;padding-left: 20px;">
                            <div class="clearfix" style="text-align:left;">
                                <!--
                                <button id="setMainWindow" type="button" class="btn btn-success">设置主窗口</button>
                                <button id="addSubWindow" type="button" class="btn btn-info">添加子窗口</button>-->
                                <div style="columns: #666666;display: inline-block;margin-left: 20px;">提示：绿色为屏幕边框，蓝色为主窗口，灰色为子窗口</div>
                            </div>
                            <div id="content_wraper" style="position: relative;">
                                {$modelvo.content}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="setMainWindowModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 700px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">设置主窗口</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" >
                    <div class="form-group">
                        <label class="col-sm-2 control-label">宽高：</label>
                        <div class="col-sm-10">
                            <span>宽：</span>
                            <input id="main_window_width" name="main_window_width" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_width}">
                            <span>高：</span>
                            <input id="main_window_height" name="main_window_height" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_height}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">左上角坐标：</label>
                        <div class="col-sm-10">
                            <span>x：</span>
                            <input id="main_window_x" name="main_window_x" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                            <span>y：</span>
                            <input id="main_window_y" name="main_window_y" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button id="setMainWindowModal_commit" type="button" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="addSubWindowModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 700px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">添加子窗口</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" >
                    <div class="form-group">
                        <label class="col-sm-2 control-label">宽高：</label>
                        <div class="col-sm-10">
                            <span>宽：</span>
                            <input id="sub_window_width" name="sub_window_width" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_width/2}">
                            <span>高：</span>
                            <input id="sub_window_height" name="sub_window_height" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_height/2}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">左上角坐标：</label>
                        <div class="col-sm-10">
                            <span>x：</span>
                            <input id="sub_window_x" name="sub_window_x" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                            <span>y：</span>
                            <input id="sub_window_y" name="sub_window_y" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button id="addSubWindowModal_commit" type="button" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="setSubWindowModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 700px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">设置子窗口</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" >
                    <input id="setsub_window_id" style="display: none;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">宽高：</label>
                        <div class="col-sm-10">
                            <span>宽：</span>
                            <input id="setsub_window_width" name="setsub_window_width" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_width/2}">
                            <span>高：</span>
                            <input id="setsub_window_height" name="setsub_window_height" type="text" class="form-control" style="display: inline-block;width: 100px;" value="{$default_height/2}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">左上角坐标：</label>
                        <div class="col-sm-10">
                            <span>x：</span>
                            <input id="setsub_window_x" name="setsub_window_x" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                            <span>y：</span>
                            <input id="setsub_window_y" name="setsub_window_y" type="text" class="form-control" style="display: inline-block;width: 100px;" value="0">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="setSubWindowModal_cancel" type="button" class="btn btn-white" >取消</button>
                <button id="setSubWindowModal_commit" type="button" class="btn btn-primary">确定</button>
                <button id="delSubWindowModal_commit" type="button" class="btn btn-danger">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script>
    var ratio=0.5;
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                name:$("#name").val(),
                content:$("#content_wraper").html(),
            };
            $.ajax({
                url:'__CONTROLLER__/api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        /*
        var screen_width=$('#screen_width').val()*ratio;
        var screen_height=$('#screen_height').val()*ratio;
        $('#screen_window').css('width',screen_width);
        $('#screen_window').css('height',screen_height);
        $('#main_window').css('width',screen_width);
        $('#main_window').css('height',screen_height);

        $('#screen_width').on('change',function () {
            var screen_width=$(this).val()*ratio;
            $('#screen_window').css('width',screen_width);
        })
        $('#screen_height').on('change',function () {
            var screen_height=$(this).val()*ratio;
            $('#screen_window').css('height',screen_height);
        })
        $('#setMainWindow').on('click',function () {
            $('#setMainWindowModal').modal();
        })
        $('#addSubWindow').on('click',function () {
            $('#addSubWindowModal').modal();
        })
        $('#setMainWindowModal_commit').on('click',function () {
            var main_window_width = $('#main_window_width').val()*ratio;
            var main_window_height = $('#main_window_height').val()*ratio;
            var main_window_x = $('#main_window_x').val()*ratio;
            var main_window_y = $('#main_window_y').val()*ratio;
            $('#main_window').css('width',main_window_width);
            $('#main_window').css('height',main_window_height);
            $('#main_window').css('left',main_window_x);
            $('#main_window').css('top',main_window_y);
            $('#setMainWindowModal').modal('hide');
        })
        $('#addSubWindowModal_commit').on('click',function () {
            var sub_window_width = $('#sub_window_width').val()*ratio;
            var sub_window_height = $('#sub_window_height').val()*ratio;
            var sub_window_x = $('#sub_window_x').val()*ratio;
            var sub_window_y = $('#sub_window_y').val()*ratio;
            var timestamp = new Date().getTime();
            var id="sub_window_"+timestamp;
            var html="<div id='"+id+"' class='mysubwindow' name='mysubwindow' style='top: "+sub_window_y+"px;left: "+sub_window_x+"px;width:"+sub_window_width+"px;height:"+sub_window_height+"px;'></div>";
            $('#screen_window').append(html);
            $("#"+id).on('dblclick',function () {
                $(this).addClass('mysubwindow-selected');
                $('#setsub_window_id').val(id);
                $('#setsub_window_width').val(parseFloat($(this).css('width'))/ratio);
                $('#setsub_window_height').val(parseFloat($(this).css('height'))/ratio);
                $('#setsub_window_x').val(parseFloat($(this).css('left'))/ratio);
                $('#setsub_window_y').val(parseFloat($(this).css('top'))/ratio);
                $('#setSubWindowModal').modal();
            })
            $('#delSubWindowModal_commit').on('click',function () {
                var setsub_window_id = $('#setsub_window_id').val();
                if(!setsub_window_id){
                    return;
                }
                $('#'+setsub_window_id).remove();
                $('#setSubWindowModal').modal('hide');
            })
            $('#setSubWindowModal_commit').on('click',function () {
                var setsub_window_id = $('#setsub_window_id').val();
                if(!setsub_window_id){
                    return;
                }
                var setsub_window_width=$('#setsub_window_width').val()*ratio;
                var setsub_window_height=$('#setsub_window_height').val()*ratio;
                var setsub_window_x=$('#setsub_window_x').val()*ratio;
                var setsub_window_y=$('#setsub_window_y').val()*ratio;
                var setsub_window=$('#'+setsub_window_id);
                setsub_window.removeClass('mysubwindow-selected');
                setsub_window.css('width',setsub_window_width);
                setsub_window.css('height',setsub_window_height);
                setsub_window.css('top',setsub_window_y);
                setsub_window.css('left',setsub_window_x);
                $('#setSubWindowModal').modal('hide');
            })
            $('#setSubWindowModal_cancel').on('click',function () {
                $('#setSubWindowModal').modal('hide');
                var setsub_window_id = $('#setsub_window_id').val();
                if(!setsub_window_id){
                    return;
                }
                var setsub_window=$('#'+setsub_window_id);
                setsub_window.removeClass('mysubwindow-selected');
            })
            var div1 = document.getElementById(id);
            div1.onmousedown = function(ev){
                var oevent = ev || event;

                var distanceX = oevent.clientX - div1.offsetLeft;
                var distanceY = oevent.clientY - div1.offsetTop;

                document.onmousemove = function(ev){
                    var oevent = ev || event;
                    div1.style.left = oevent.clientX - distanceX + 'px';
                    div1.style.top = oevent.clientY - distanceY + 'px';
                };
                document.onmouseup = function(){
                    document.onmousemove = null;
                    document.onmouseup = null;
                };
                ;
            };
            $('#addSubWindowModal').modal('hide');
        })
        */

    });

</script>


</body>

</html>
