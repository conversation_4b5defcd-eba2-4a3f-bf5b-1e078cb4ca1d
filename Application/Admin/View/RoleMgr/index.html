<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>角色列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_listEventsToolbar"
                                     role="group">
                                    <eq name="privilege" value="1">
                                        <button id="addRoleBtn" type="button"
                                                class="btn btn-outline btn-default">
                                            <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline btn-default"
                                                style="display: none;">
                                            <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                        </button>
                                        <button id="tb_deleteBtn" type="button"
                                                class="btn btn-outline btn-default">
                                            <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                        </button>
                                    </eq>
                                </div>
                                <table id="tb_list" style="cursor: pointer;">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6" style="display: none">
                <div class="ibox-title"><h5 id="privilegeTitle">操作权限</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <eq name="privilege" value="1">
                            <button id="btnSavePrivilege" type="button" class="btn btn-success">保存</button>
                        </eq>
                    </div>
                </div>
                <div class="ibox-content">
                    <div id="treeview1" class="test"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addRoleModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">新增角色</h4>
                </div>
                <form id="addRoleForm" method="get" class="form-horizontal" style="padding: 20px;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">角色名称</label>
                        <div class="col-sm-10">
                            <input class="form-control" id="add_rolename">
                        </div>
                    </div>
                </form>
                <div class="modal-footer" style="text-align: center">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button id="addRoleSave" type="button" class="btn btn-primary">提交</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>

    <div class="modal fade" id="modRoleModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">修改角色</h4>
                </div>
                <form id="modRoleForm" method="get" class="form-horizontal" style="padding: 20px;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">ID</label>
                        <div class="col-sm-10">
                            <input class="form-control" id="mod_roleid" readonly="readonly">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">角色名称</label>
                        <div class="col-sm-10">
                            <input class="form-control" id="mod_rolename">
                        </div>
                    </div>
                </form>
                <div class="modal-footer" style="text-align: center">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button id="modRoleSave" type="button" class="btn btn-primary">提交</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>

    <div id="loading" class="loading" style="display: none">加载中...</div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
    <!-- Peity -->
    <script src="__PUBLIC__/js/demo/bootstrap-table-demo.js"></script>
    <script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
    <script src="__PUBLIC__/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

            $('#btnSavePrivilege').on("click",function () {
                var checkeds = $('#treeview1').treeview('getChecked');
                console.log(checkeds)
                var selects = $('#tb_list').bootstrapTable('getSelections');
                if(selects.length!=1){
                    toastr.error("请选择一个角色！");
                    return;
                }
                var roleid=selects[0].roleid;
                console.log("roleid="+roleid)
                var req={
                    roleid:roleid,
                    privileges:checkeds
                }

                $('#loading').css("display","block");
                postjson2("RoleMgr/setPrivilege",req,function () {
                    toastr.success("修改成功！");
                },function () {
                    $('#loading').css("display","none");
                })
            })
        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_list').bootstrapTable({
                    url: 'RoleMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 50,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: true,                //是否启用点击选中行
                    singleSelect : true,
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "roleid",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'roleid',
                        title: '角色ID'
                    }, {
                        field: 'rolename',
                        title: '角色名称'
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                // window.location.href = "initusermodify.do?userid="+this.previousSibling.innerText;
                                $('#modRoleForm')[0].reset();
                                $('#mod_roleid').val(this.previousSibling.innerText);
                                $('#mod_rolename').val(this.nextSibling.innerText);
                                $('#modRoleModal').modal({
                                    keyboard: false
                                });
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            if({$privilege}!=1){
                return;
            }
            if(row['builtin']==1){
                return;
            }
            var html = '';
            html +='<div style="width:100px;text-align:center;">';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
            html += '<div style="display:none;">'+row['roleid']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">修改</button>';
            html += '<div style="display:none;">'+row['rolename']+'</div>';
            html += '<div style="display:none;">'+row['loginentrance']+'</div>';
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#tb_deleteBtn").on("click",function(){
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].roleid);
                    }
                    var req = {
                        roleidlist:users
                    }


                    $.ajax({
                        url:'RoleMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                if("1027"==data.retCode||"1028"==data.retCode){
                                    toastr.error(data.retMsg);
                                    return;
                                }
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#addRoleBtn').on("click",function () {
                    $('#addRoleForm')[0].reset();
                    $('#addRoleModal').modal({
                        keyboard: false
                    });
                })

                $('#addRoleSave').on('click',function () {
                    var req={
                        rolename:$('#add_rolename').val(),
                    }
                    postjson("RoleMgr/api_add",req,function () {
                        $('#addRoleModal').modal('hide');
                        $('#tb_list').bootstrapTable('refresh');
                    })
                })

                $('#modRoleSave').on('click',function () {
                    var req={
                        roleid:$('#mod_roleid').val(),
                        rolename:$('#mod_rolename').val(),
                    }
                    postjson("RoleMgr/api_update",req,function () {
                        $('#modRoleModal').modal('hide');
                        $('#tb_list').bootstrapTable('refresh');
                    })
                })

                // $('#tb_list').on('click-row.bs.table', function (e, row, element){
                //     // console.log(row);
                // });
                // $('#tb_list').on('check.bs.table', function (e, row, element){
                //     console.log(row);
                //     console.log("check");
                //     if(row){
                //         $('#privilegeTitle').html("["+row.rolename+"]操作权限")
                //         initPrivilegeTree(row.roleid,row.loginentrance);
                //     }
                // });
                // $('#tb_list').on('uncheck.bs.table', function (e, row, element){
                //     console.log(row);
                //     console.log("uncheck");
                // });
            };

            return oInit;
        };

        function initPrivilegeTree(roleid,loginentrance) {
            if(loginentrance==1){
                $('#treeview1').treeview({
                    data: null,
                });
            }else{
                // $('#treeview1').treeview({
                //     data: null,
                // });
                var req={
                    roleid:roleid
                }
                $('#loading').css("display","block");
                postjson2("RoleMgr/getPrivilegeTree",req,function (data) {
                    var menus=data;
                    console.log(data);
                    $('#treeview1').treeview({
                        data: data,
                        showCheckbox: true,
                        selectable: true,
                        onNodeChecked:function(event, node){
                            console.log(node)
                            setParentNodeCheck(node,'1');
                        },
                        onNodeUnchecked:function(event, node){
                            var selectNodes = getChildNodeIdArr(node); //获取所有子节点
                            if (selectNodes) { //子节点不为空，则选中所有子节点
                                $("#treeview1").treeview('uncheckNode', [selectNodes, { silent: true }]);
                            }
                        },
                        onNodeSelected: function (event, node) {
                            level=node.level;
                            levelid=node.dataid;
                            // $('#tb_bootstraptblist').bootstrapTable('refresh');
                            // console.log(node)
                            // var selectNodes = getChildNodeIdArr(node); //获取所有子节点
                            // if (selectNodes) { //子节点不为空，则选中所有子节点
                            //     $("#treeview1").treeview('selectNode', [selectNodes, { silent: true }]);
                            // }
                            // setParentNodeCheck(node,'1');//自动选择父节点
                        }
                    });
                },function () {
                    $('#loading').css("display","none");
                });
            }
        }

        function getChildNodeIdArr(node) {
            var ts = [];
            if (node.nodes) {
                for (x in node.nodes) {
                    ts.push(node.nodes[x].nodeId);
                    if (node.nodes[x].nodes) {
                        var getNodeDieDai = getChildNodeIdArr(node.nodes[x]);
                        for (j in getNodeDieDai) {
                            ts.push(getNodeDieDai[j]);
                        }
                    }
                }
            } else {
                ts.push(node.nodeId);
            }
            return ts;
        }

        function setParentNodeCheck(node,type) {
            var parentNode = $("#treeview1").treeview("getParent", node);
            if (parentNode.nodes) {
                if(type=='1'){
                    $("#treeview1").treeview('checkNode', [ parentNode, { silent: true } ]);
                    setParentNodeCheck(parentNode,'1');
                }else{
                    $("#treeview1").treeview('uncheckNode', [ parentNode, { silent: true } ]);
                    setParentNodeCheck(parentNode,'2');
                }
            }
        }
    </script>
</body>

</html>
