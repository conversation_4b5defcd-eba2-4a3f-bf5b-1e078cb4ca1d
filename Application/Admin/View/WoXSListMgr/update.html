<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>{$wkotypename}工单详情</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>
                            <div class="col-sm-10">
                                <input id="woid" name="woid" type="text" readonly="readonly" class="form-control" value="{$wo.woid}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">电站：</label>
                            <div class="col-sm-10">
                                <!--<select class="form-control m-b" id="stationid">-->
                                    <!--<volist name="stationList" id="vo">-->
                                        <!--<option value="{$vo.stationid}">{$vo.name}</option>-->
                                    <!--</volist>-->
                                <!--</select>-->
                                <input id="stationid" name="stationid" type="text" readonly="readonly" class="form-control" value="{$wo.stationname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="status">
                                    <volist name="statusList" id="vo">
                                        <option value="{$vo.value}" <eq name="wo.status" value="$vo.value">selected</eq> >{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">任务类型：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="tasktype">
                                    <volist name="tasktypeList" id="vo">
                                        <option value="{$vo.value}" <eq name="wo.tasktype" value="$vo.value">selected</eq> >{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">创建时间：</label>

                            <div class="col-sm-10">
                                <input id="createtime" class="form-control layer-date" value="{$wo.createtime}" readonly="readonly" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">开始处理时间：</label>

                            <div class="col-sm-10">
                                <input id="starttime" class="form-control layer-date" value="{$wo.starttime}" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">截止时间：</label>

                            <div class="col-sm-10">
                                <input id="endtime" class="form-control layer-date" value="{$wo.endtime}" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">原因：</label>

                            <div class="col-sm-10">
                                <input id="reason" name="reason" type="text" class="form-control" value="{$wo.reason}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">创建人：</label>

                            <div class="col-sm-10">
                                <input id="create_username" name="create_username" type="text" readonly="readonly" class="form-control" value="{$wo.create_username}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">接受人：</label>

                            <div class="col-sm-10">
                                <input id="accept_username" name="accept_username" type="text" readonly="readonly" class="form-control" value="{$wo.accept_username}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">协助人员：</label>

                            <div class="col-sm-10">
                                <input id="cooperations" name="cooperations" type="text" readonly="readonly" class="form-control" value="{$cooperations}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">巡检结果：</label>

                            <div class="col-sm-10">
                                <button id="gotoResult" class="btn btn-primary" type="button">查看巡检结果</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>

                            <div class="col-sm-10">
                                <input id="remarks" name="remarks" type="text" class="form-control" value="{$wo.remarks}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">指定运维单位：</label>

                            <div class="col-sm-10">
                                <div id="selected_org" style="display: inline-block"></div>
                                <input id="handleorgid" value="{$wo.handleorgid}" style="display: none">
                                <button id="btn_setorg" type="button" class="btn btn-primary">选择</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">操作历史：</label>

                            <div class="col-sm-10">
                                <div style="padding: 2px;">{$wo.history}</div>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="OrgTree/orgtree" />

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        initDate();
        $("#saveBtn").on("click",function(){
            var req = {
                woid:$("#woid").val(),
                status:$("#status").val(),
                tasktype:$("#tasktype").val(),
                starttime:$("#starttime").val(),
                endtime:$("#endtime").val(),
                reason:$("#reason").val(),
                remarks:$("#remarks").val(),
                handleorgid:$("#handleorgid").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        $('#gotoResult').on("click",function(){
            window.location.href = "__MODULE__/WoResultMgr/index?woid="+$("#woid").val();
        });

        var id=$('#handleorgid').val();
        if(id){
            var req={
                id:id
            }
            postjson("__MODULE__/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
        }

        $('#btn_setorg').on('click',function () {
            $('#orgTreeModal').modal({keyboard:false})
            initOrgzTree();
        })
        $('#btnOrgSave').on('click',function () {
            var select = $('#orgTreeview').treeview('getSelected');
            if(select.length!=1){
                toastr.error("请先选择一个节点！");
                return;
            }
            var id=select[0].dataid;
            var name=select[0].text;
            // console.log(getAllParent(select[0]))
            $('#orgTreeModal').modal('hide');

            console.log("__MODULE__/OrgzMgr/api_getOrgPath?id="+id)
            var req={
                id:id
            }
            postjson("__MODULE__/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
            $('#handleorgid').val(id);
        })
    });
    function initDate() {
    }
</script>


</body>

</html>
