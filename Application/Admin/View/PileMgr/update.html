<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改桩</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" class="form-control" readonly="readonly" value="{$pile.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">桩编码：</label>
                            <div class="col-sm-10">
                                <input id="code" name="code" type="text" class="form-control" value="{$pile.code}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">桩名称：</label>
                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control" value="{$pile.name}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">所属充电站：</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="stationid">
                                    <option value="0">请选择</option>
                                    <volist name="stationList" id="vo">
                                        <option value="{$vo.stationid}" <eq name="pile.stationid" value="$vo.stationid">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">交直流：</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="acdc">
                                    <volist name="pileAcdcList" id="vo">
                                        <option value="{$vo.value}" <eq name="pile.acdc" value="$vo.value">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电枪个数：</label>
                            <div class="col-sm-10">
                                <input id="guncount" name="guncount" type="text" class="form-control" value="{$pile.guncount}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">电费费率(元每度)：</label>
                            <div class="col-sm-10">
                                <input id="rate_e" name="rate_e" type="text" class="form-control" value="{$pile.rate_e}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">服务费费率(元每度)：</label>
                            <div class="col-sm-10">
                                <input id="rate_s" name="rate_s" type="text" class="form-control" value="{$pile.rate_s}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">停车费费率(元每度)：</label>
                            <div class="col-sm-10">
                                <input id="rate_p" name="rate_p" type="text" class="form-control" value="{$pile.rate_p}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">供应商：</label>
                            <div class="col-sm-10">
                                <input id="supplier" name="supplier" type="text" class="form-control" value="{$pile.supplier}">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        // initDate();
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
                code:$("#code").val(),
                name:$("#name").val(),
                stationid:$("#stationid").val(),
                acdc:$("#acdc").val(),
                guncount:$("#guncount").val(),
                rate_e:$("#rate_e").val(),
                rate_s:$("#rate_s").val(),
                rate_p:$("#rate_p").val(),
                supplier:$("#supplier").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-2);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

    function initDate() {
        $('#onlinetime').val(laydate.now(0, 'YYYY-MM-DD hh:mm:ss'));
    }
</script>


</body>

</html>
