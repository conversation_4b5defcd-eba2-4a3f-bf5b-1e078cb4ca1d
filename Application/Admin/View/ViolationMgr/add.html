<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>添加违规记录</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <input id="roleid" name="roleid" style="display: none;" value="{$roleid}">
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户类型：</label>

                            <div class="col-sm-10">
                                <label>
                                    <input type="radio" checked="" value="0" name="usertype"> <i></i>小程序用户</label>
                                <label style="margin-left: 20px;">
                                    <input type="radio" value="1" name="usertype"> <i></i>卡用户</label>
                            </div>
                        </div>
                        <div id="wxuser_wraper" class="form-group">
                            <label class="col-sm-2 control-label">小程序用户：</label>
                            <div class="col-sm-10">
                                <input id="wxuserid" type="text" style="display: none;">
                                <div id="wxusername" style="display: inline-block;"></div>
                                <button id="select_wxuserBtn" class="btn btn-primary" style="display: inline-block;" type="button">选择</button>
                            </div>
                        </div>
                        <div id="carduser_wraper" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">卡用户：</label>
                            <div class="col-sm-10">
                                <input id="carduserid" type="text" style="display: none;">
                                <div id="cardusername" style="display: inline-block;"></div>
                                <button id="select_carduserBtn" class="btn btn-primary" style="display: inline-block;" type="button">选择</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">违规描述：</label>
                            <div class="col-sm-10">
                                <input id="remark" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">计分：</label>
                            <div class="col-sm-10">
                                <input id="point" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal inmodal fade" id="userlistModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 1000px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">选择小程序用户</h4>
            </div>
            <div class="modal-body">
                <table id="tb_userlist">
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button id="userlistModal_commit" type="button" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
</div>
<div class="modal inmodal fade" id="carduserlistModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 1000px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">选择卡用户</h4>
            </div>
            <div class="modal-body">
                <table id="tb_carduserlist">
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button id="carduserlistModal_commit" type="button" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>
</div>
<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<!--<script src="__PUBLIC__/js/sha256.js"></script>-->
<script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                account:$("#account").val(),
                name:$("#name").val(),
                violationpwd:sha256(violationpwd),
                violationpwd1:violationpwd,
                roleid:$("#roleid").val(),
            };
            $.ajax({
                url:'api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $("input[type=radio][name='usertype']").change(function() {
            if (this.value == 1) {
                $('#wxuser_wraper').css('display','none');
                $('#carduser_wraper').css('display','block');
            }
            else{
                $('#wxuser_wraper').css('display','block');
                $('#carduser_wraper').css('display','none');
            }
        });
        $('#select_wxuserBtn').on('click',function () {
            $('#userlistModal').modal();
        });
        $('#select_carduserBtn').on('click',function () {
            $('#carduserlistModal').modal();
        });

        var oTable1 = new TableInit1();
        oTable1.Init();
        var oTable2 = new TableInit2();
        oTable2.Init();
    });

    var TableInit1 = function () {
        var oTableInit = new Object();
        oTableInit.Init = function () {
            $('#tb_userlist').bootstrapTable({
                url: '__MODULE__/RegisterMgr/api_getlist',         //请求后台的URL（*）
                method: 'post',                      //请求方式（*）
                toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                striped: true,                      //是否显示行间隔色
                cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true,                   //是否显示分页（*）
                sortable: false,                     //是否启用排序
                sortOrder: "asc",                   //排序方式
                queryParams: oTableInit.queryParams,//传递参数（*）
                sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                pageNumber:1,                       //初始化加载第一页，默认第一页
                pageSize: 10,                       //每页的记录行数（*）
                pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                strictSearch: true,
                showColumns: true,                  //是否显示所有的列
                showRefresh: true,                  //是否显示刷新按钮
                minimumCountColumns: 2,             //最少允许的列数
                clickToSelect: false,                //是否启用点击选中行
                //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                cardView: false,                    //是否显示详细视图
                detailView: false,                   //是否显示父子表
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: '用户ID'
                },{
                    field: 'account',
                    title: '账号'
                }, {
                    field: 'cardnum',
                    title: '卡号'
                }, {
                    field: 'propertyname',
                    title: '物业'
                }, {
                    field: 'operatorname',
                    title: '运营商'
                },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter1 //自定义方法，添加操作按钮
                    }],
                onLoadSuccess:function(){
                    var modifyBtns = $("button[name='modifyBtn']");
                    for(var i=0;i<modifyBtns.length;i++){
                        modifyBtns[i].addEventListener("click",function(){
                            // window.location.href = "__CONTROLLER__/update?id="+this.previousSibling.innerText;
                        });
                    }
                }
            });
        };

        //得到查询的参数
        oTableInit.queryParams = function (params) {
            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                limit: params.limit,
                offset: params.offset,
                operatorid :$('#search_operatorid').val(),
                propertyid :$('#search_propertyid').val(),
                account :$('#search_account').val(),
            };
            return temp;
        };
        return oTableInit;
    };

    function operateFormatter1(value, row, index) {
        var html = '';
        html +='<div style="width:100px;text-align:center;">';
        html += '<div style="display:none;">'+row['id']+'</div>';
        html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">选择</button>';
        html +='</div>';
        return html;
    }

    var TableInit2 = function () {
        var oTableInit = new Object();
        oTableInit.Init = function () {
            $('#tb_carduserlist').bootstrapTable({
                url: '__MODULE__/CardMgr/api_getlist',         //请求后台的URL（*）
                method: 'post',                      //请求方式（*）
                toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                striped: true,                      //是否显示行间隔色
                cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true,                   //是否显示分页（*）
                sortable: false,                     //是否启用排序
                sortOrder: "asc",                   //排序方式
                queryParams: oTableInit.queryParams,//传递参数（*）
                sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                pageNumber:1,                       //初始化加载第一页，默认第一页
                pageSize: 10,                       //每页的记录行数（*）
                pageList: [10, 20, 50,100],        //可供选择的每页的行数（*）
                search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                strictSearch: true,
                showColumns: true,                  //是否显示所有的列
                showRefresh: true,                  //是否显示刷新按钮
                minimumCountColumns: 2,             //最少允许的列数
                clickToSelect: false,                //是否启用点击选中行
                //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                cardView: false,                    //是否显示详细视图
                detailView: false,                   //是否显示父子表
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: 'ID'
                }, {
                    field: 'cardnumber',
                    title: '卡号'
                }, {
                    field: 'name',
                    title: '名称'
                }, {
                    field: 'operatorname',
                    title: '所属运营商'
                }, {
                    field: 'propertyname',
                    title: '所属物业'
                },
                {
                    field: 'operate',
                    title: '操作',
                    width:'100px',
                    formatter: operateFormatter2 //自定义方法，添加操作按钮
                }],
                onLoadSuccess:function(){
                    var modifyBtns = $("button[name='modifyBtn']");
                    for(var i=0;i<modifyBtns.length;i++){
                        modifyBtns[i].addEventListener("click",function(){
                            // window.location.href = "__CONTROLLER__/update?id="+this.previousSibling.innerText;
                        });
                    }
                }
            });
        };

        //得到查询的参数
        oTableInit.queryParams = function (params) {
            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                limit: params.limit,
                offset: params.offset,
                operatorid :$('#search_operatorid').val(),
                propertyid :$('#search_propertyid').val(),
                cardnumber :$('#search_cardnumber').val(),
            };
            return temp;
        };
        return oTableInit;
    };

    function operateFormatter2(value, row, index) {
        var html = '';
        html +='<div style="width:160px;text-align:center;">';
        html += '<div style="display:none;">'+row['id']+'</div>';
        html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">选择</button>';
        html +='</div>';
        return html;
    }

</script>


</body>

</html>
