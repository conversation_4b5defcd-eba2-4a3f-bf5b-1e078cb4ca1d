<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>添加轮播图</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">文件：</label>
                            <div class="col-sm-10">
                                <a id="filelink" target="_blank" href="" style="display: none">下载</a>
                                <input id="mainpic" style="display: none;" value="" >
                                <input id="md5" style="display: none;" value="" >
                                <button id="btn_upload" class="btn btn-primary" type="button">上传</button>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_uploadfile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">上传文件</h4>
            </div>
            <input id="pic" name="pic" type="file" class="form-control" style="margin: 20px 20px;width: 80%;">
            <div class="modal-footer" style="text-align: center">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button id="btn_uploadfile" type="button" class="btn btn-primary">提交</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>

<div class="modal fade" id="myModal">
    <div class="modal-dialog modal-sm" style="text-align: center;margin:205px auto">

        <img alt="" src="__PUBLIC__/img/wite.gif" style="width:70px;"/>
        <p id="uploadprogress" style="padding-top:10px;font-size:14px;color:#FF9600"></p>
    </div>
</div>
<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#btn_upload").on("click",function(){
            $('#pic').val("");
            $('#modal_uploadfile').modal({
                keyboard: false
            });
        });
        $("#btn_uploadfile").on("click",function(){
            UpladFile();
        });
        $("#saveBtn").on("click",function(){
            var req = {
                type:$("#type").val(),
                url:$("#mainpic").val(),
                md5:$("#md5").val(),
                starttime:$("#starttime").val(),
                endtime:$("#endtime").val(),
            };
            $.ajax({
                url:'__CONTROLLER__/api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        var xhr;
        function UpladFile() {
            $('#myModal').modal({backdrop:'static',keyboard:false});

            var fileObj = document.getElementById("pic").files[0]; // js 获取文件对象
            var url =  "__APP__/Api/Index/uploadFile"; // 接收上传文件的后台地址

            var form = new FormData(); // FormData 对象
            form.append("picture", fileObj); // 文件对象

            xhr = new XMLHttpRequest();  // XMLHttpRequest 对象
            xhr.open("post", url, true); //post方式，url为服务器请求地址，true 该参数规定请求是否异步处理。
            xhr.onload = uploadComplete; //请求完成
            xhr.onerror =  uploadFailed; //请求失败

            // xhr.upload.onprogress = progressFunction;//【上传进度调用方法实现】
            // xhr.upload.onloadstart = function(){
            //     ot = new Date().getTime();   //设置上传开始时间
            //     oloaded = 0;//设置上传开始时，以上传的文件大小为0
            // };

            xhr.send(form); //开始上传，发送form数据
        }
        //上传成功响应
        function uploadComplete(evt) {
            $('#myModal').modal('hide');
            //服务断接收完文件返回的结果
            console.log(evt.target.responseText);
            var data = JSON.parse(evt.target.responseText);
            if(data.retCode=="0") {
                console.log("上传成功！");
                $("#mainpic").val(data.pictureUrl);
                $("#md5").val(data.md5);
                $("#filelink").css("display","inline");
                $("#filelink").attr("href",data.pictureUrl);
                $('#modal_uploadfile').modal('hide');
            }else{
                console.log("上传失败！");
                toastr.error("上传失败！");
            }

        }
        //上传失败
        function uploadFailed(evt) {
            $('#myModal').modal('hide');
            console.log("上传失败！");
            toastr.error("上传失败！");
        }
        //取消上传
        function cancleUploadFile(){
            xhr.abort();
        }
    });
</script>


</body>

</html>
