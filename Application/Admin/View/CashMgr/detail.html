<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>提现记录详情</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">提现记录ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" readonly="readonly" class="form-control" value="{$cash.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">申请用户账号：</label>

                            <div class="col-sm-10">
                                <input id="createaccount" name="createaccount" type="text"  readonly="readonly" class="form-control" value="{$cash.createaccount}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">提现金额：</label>

                            <div class="col-sm-10">
                                <input id="money" name="money" type="text"  readonly="readonly" class="form-control" value="{$cash.money}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态：</label>

                            <div class="col-sm-10">
                                <input id="statusname" name="statusname" type="text"  readonly="readonly" class="form-control" value="{$cash.statusname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">姓名：</label>

                            <div class="col-sm-10">
                                <input id="name" name="name" type="text"  readonly="readonly" class="form-control" value="{$cash.name}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">支行：</label>

                            <div class="col-sm-10">
                                <input id="bank" name="bank" type="text"  readonly="readonly" class="form-control" value="{$cash.bank}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">银行卡号：</label>

                            <div class="col-sm-10">
                                <input id="bankcardnum" name="bankcardnum" type="text"  readonly="readonly" class="form-control" value="{$cash.bankcardnum}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">创建时间：</label>

                            <div class="col-sm-10">
                                <input id="createtime" name="createtime" type="text"  readonly="readonly" class="form-control" value="{$cash.createtime}">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <eq name="cash.status" value="0">
                            <div class="form-group">
                                <div class="col-sm-4 col-sm-offset-2">
                                    <button id="saveBtn" class="btn btn-primary" type="button">确认已转账</button>
                                    <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                                </div>
                            </div>
                        </eq>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
            };
            $.ajax({
                url:'api_confirm',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

</script>


</body>

</html>
