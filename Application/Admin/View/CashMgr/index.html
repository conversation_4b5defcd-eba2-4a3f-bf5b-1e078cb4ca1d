<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>提现记录列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                     role="group">
                                    <button id="testBtn" type="button"
                                            class="btn btn-outline btn-default" style="display: none;"
                                            onclick="script:window.location.href = '__CONTROLLER__/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                </div>
                                <table id="tb_bootstraptblist">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="__PUBLIC__/js/demo/bootstrap-table-demo.js"></script>
    <script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_bootstraptblist').bootstrapTable({
                    url: 'CashMgr/api_getlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'id',
                        title: '提现记录ID'
                    }, {
                        field: 'createaccount',
                        title: '申请用户账号'
                    }, {
                        field: 'money',
                        title: '提现金额'
                    }, {
                        field: 'point',
                        title: '扣除积分'
                    }, {
                        field: 'statusname',
                        title: '状态'
                    }, {
                        field: 'cashtypename',
                        title: '提现方式'
                    }, {
                        field: 'zhifubao',
                        title: '支付宝账号'
                    }, {
                        field: 'name',
                        title: '收款姓名'
                    }, {
                        field: 'bank',
                        title: '所属支行'
                    }, {
                        field: 'bankcardnum',
                        title: '银行卡号'
                    }, {
                        field: 'createtime',
                        title: '创建时间'
                    }, {
                        field: 'updatetime',
                        title: '转账时间'
                    },{
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                // window.location.href = "CashMgr/detail?id="+this.nextSibling.innerText;
                                var money=this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                var r=confirm("确认转账"+money+"元?");
                                if (r!=true){
                                    return;
                                }
                                confirmmoney(id);
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "CashMgr/update?userid="+this.previousSibling.innerText;
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                };
                return temp;
            };
            return oTableInit;
        };

        function confirmmoney(id) {
            var req = {
                id:id,
            };
            $.ajax({
                url:'__CONTROLLER__/api_confirm',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    toastr.success("转账成功！");
                    $('#tb_bootstraptblist').bootstrapTable('refresh');
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        }
        function operateFormatter(value, row, index) {
            if(row['status']!=0){
                return;
            }
            var html = '';
            html +='<div style="width:100px;text-align:center;">';
            html += '<div style="display:none;">'+row['money']+'</div>';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" >确认转账</button>';
            html += '<div style="display:none;">'+row['id']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;display: none;">修改</button>';
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].id);
                    }
                    var deleteReq = {
                        idlist:devs
                    }

                    $.ajax({
                        url:'CashMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(deleteReq),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_bootstraptblist').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });
            };

            return oInit;
        };
    </script>
</body>

</html>
