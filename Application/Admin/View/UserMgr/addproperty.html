<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>添加物业用户</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <input id="roleid" name="roleid" style="display: none;" value="{$roleid}">
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账号：</label>

                            <div class="col-sm-9">
                                <input id="account" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">名称：</label>

                            <div class="col-sm-9">
                                <input id="name" type="text" class="form-control">
                            </div>
                        </div>

                        <eq name="canmodoperator" value="1">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">运营商：</label>
                            <div class="col-sm-9">
                                <select id="operatoruserid" class="form-control m-b" >
                                    <option value="0">请选择运营商</option>
                                    <volist name="operatorlist" id="vo">
                                        <option value="{$vo.userid}">{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        </eq>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动开关机时间段：</label>
                            <div class="col-sm-9">
                                <input id="autopoweronoff" type="text" class="form-control" value="{$user.autopoweronoff}" placeholder="示例：5:00-10:00,16:00-23:00">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">自动开关垃圾箱投口时间段：</label>
                            <div class="col-sm-9">
                                <input id="autodooropenclose" type="text" class="form-control" value="{$user.autodooropenclose}" placeholder="示例：7:00-9:00,17:00-20:00">
                            </div>
                        </div>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动调节屏幕亮度时间点：</label>
                            <div class="col-sm-9">
                                <input id="autoadjustlight" type="text" class="form-control" value="{$user.autoadjustlight}" placeholder="示例：10:00-90,18:00-60">
                            </div>
                        </div>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-3 control-label">自动调节音量时间点：</label>
                            <div class="col-sm-9">
                                <input id="autoadjustvolume" type="text" class="form-control" value="{$user.autoadjustvolume}" placeholder="示例：10:00-90,18:00-60">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">登录密码：</label>

                            <div class="col-sm-9">
                                <input id="userpwd" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">密码确认：</label>

                            <div class="col-sm-9">
                                <input id="userpwd1" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<include file="OrgTree/orgtree" />
<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<!--<script src="__PUBLIC__/js/sha256.js"></script>-->
<script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var userpwd=$("#userpwd").val();
            var userpwd1=$("#userpwd1").val();
            if(''==userpwd){
                toastr.error("请输入密码！ ");
                return;
            }
            if(userpwd!=userpwd1){
                toastr.error("确认密码不一致！ ");
                return;
            }
            // var selected_orgId = $('#selected_orgId').val();
            // if(!selected_orgId){
            //     toastr.error("请选择所属组织结构！");
            //     return;
            // }

            var req = {
                account:$("#account").val(),
                name:$("#name").val(),
                userpwd:sha256(userpwd),
                userpwd1:userpwd,
                roleid:$("#roleid").val(),
                operatoruserid:$("#operatoruserid").val(),
                autopoweronoff:$("#autopoweronoff").val(),
                autodooropenclose:$("#autodooropenclose").val(),
                autoadjustlight:$("#autoadjustlight").val(),
                autoadjustvolume:$("#autoadjustvolume").val(),
            };
            $.ajax({
                url:'api_add_property',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.retMsg);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

</script>


</body>

</html>
