<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>{$pagetitle}</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <style>
        .fl{
            float:left;
        }
        .fr{
            float:right;
        }
        .clearfix:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
        }
        .clearfix_my:after{
            display:block;
            visibility:hidden;
            clear:both;
            height:0;
            content:".";
        }
        /* Border styles */
        .mytable thead, .mytable tr {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: rgb(235, 242, 224);
        }
        .mytable {
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: rgb(235, 242, 224);
            width: 100%;
        }

        /* Padding and font style */
        .mytable td, .mytable th {
            padding: 5px 10px;
            font-size: 12px;
            font-family: Verdana;
            color: rgb(149, 170, 109);
        }

        /* Alternating background colors */
        .mytable tr:nth-child(even) {
            background: rgb(230, 238, 214)
        }
        .mytable tr:nth-child(odd) {
            background: #FFF
        }
        .mysubwindow{
            position: absolute;top: 0;left: 0;
            border: solid 1px #aaaaaa;
        }
        .mywindow-selected{
            background-color: rgba(255, 99, 71,0.3);
        }
        .mywindow-seted{
            border: solid 1px red;
            z-index: 11;
        }
        .myinput2{
            width: 50px;
        }
    </style>

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>{$pagetitle}</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button id="saveBtn" type="button" style="display: none" class="btn btn-primary">确定</button>
                        <button type="button" class="btn btn-white"
                                onclick="javascript:window.history.go(-1);">返回</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>
                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" class="form-control" value="{$modelvo.id}" readonly="readonly">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">实例名称：</label>
                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control" value="{$modelvo.name}" readonly="readonly">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">模板名称：</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="layoutid" disabled="disabled">
                                    <option value="0">选择模板</option>
                                    <volist name="layoutlist" id="vo">
                                        <option value="{$vo.id}" <eq name="modelvo.layoutid" value="$vo.id">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group" style="height: 1920px;padding-left: 20px;">
                            <div class="clearfix" style="text-align:left;">
                                <!--
                                <button id="setMainWindow" type="button" class="btn btn-success">设置主窗口</button>
                                <button id="addSubWindow" type="button" class="btn btn-info">添加子窗口</button>-->
                                <div style="columns: #666666;display: inline-block;margin-left: 20px;">提示：绿色为屏幕边框，蓝色为主窗口，灰色为子窗口，双击查看子窗口</div>
                            </div>
                            <div id="content_wraper" style="position: relative;">
                                {$modelvo.content}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="setPlayListModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 700px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">播放列表</h4>
            </div>
            <div class="modal-body">
                <div><span>窗口ID：</span><input id="setsub_window_id" style="border: none;width: 200px;background-color: transparent;" readonly="readonly"></div>
                <div class="clearfix" style="margin: 10px 0;display: none;">
                    <button id="btn_addFile" type="button" class="btn btn-white fl"  style="">插入文件</button>

                    <button id="setPlayListModal_commit" type="button" class="btn btn-primary fr" style="margin: 0 4px;">确定</button>
                    <button id="setPlayListModal_cancel" type="button" class="btn btn-white fr"  style="margin: 0 4px;">取消</button>
                    <button id="setPlayListModal_clear" type="button" class="btn btn-danger fr" style="margin: 0 4px;">清空</button>
                </div>
                <form class="form-horizontal" >
                    <table id="" class="mytable">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>文件名</th>
                            <th>类型</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>次数或时间</th>
                            <!--<th>移动</th>-->
                        </tr>
                        </thead>
                        <tbody id="playlist">
                        </tbody>
                    </table>
                </form>
            </div>
            <div class="modal-footer">

            </div>
        </div>
    </div>
</div>

<div class="modal inmodal fade" id="adsfileListModal" tabindex="-1" role="dialog"  aria-hidden="true">
    <div class="modal-dialog" style="width: 700px;height: 600px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 10px 15px;">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 id="" class="modal-title">选择广告文件</h4>
            </div>
            <div class="modal-body">
                <table id="tb_adsfilelist">
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button id="adsfileListModal_commit" type="button" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    var playlistmap={$modelvo.playlistmap};
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$('#id').val(),
                name:$("#name").val(),
                layoutid:$("#layoutid").val(),
                content:$("#content_wraper").html(),
                playlistmap:playlistmap
            };
            $.ajax({
                url:'__CONTROLLER__/api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $('#layoutid').on('change',function () {
            var id=$(this).val();
            var req={
                id:id
            }
            postjson('__CONTROLLER__/api_getlayout',req,function (data) {
                var content=data.content;
                $('#content_wraper').html(content);
                playlistmap={};
                initLayout();
            })
        })

        $('#setPlayListModal_cancel').on('click',function () {
            $('#setPlayListModal').modal('hide');
        })

        $('#setPlayListModal_commit').on('click',function () {
            var setsub_window_id = $('#setsub_window_id').val();
            if(!setsub_window_id){
                $('#setPlayListModal').modal('hide');
                return;
            }
            playlistmap[setsub_window_id]=new Array();
            var playlist = playlistmap[setsub_window_id];
            var trs = $('#playlist').children("tr");
            if(!trs||trs.length==0){
                playlistmap[setsub_window_id] = [];
                $('#setPlayListModal').modal('hide');
                return;
            }
            for(var i=0;i<trs.length;i++){
                var tds = $(trs[i]).children("td");
                if(!tds||tds.length<7){
                    continue;
                }
                var elem={};
                elem.id=$(tds[0]).text();
                elem.filename=$(tds[1]).text();
                elem.typename=$(tds[2]).text();
                elem.starttime=$(tds[3]).text();
                elem.endtime=$(tds[4]).text();
                elem.interval=$(tds[5]).children("input")[0].value;
                playlist.push(elem);
            }

            if(playlist.length>0){
                $('#'+setsub_window_id).addClass('mywindow-seted');
            }else{
                $('#'+setsub_window_id).removeClass('mywindow-seted');
            }

            $('#setPlayListModal').modal('hide');
        })

        $('#setPlayListModal_clear').on('click',function () {

        })
        $('#setPlayListModal').on('hide.bs.modal',function () {
            var mysubwindows = document.getElementsByName('mywindow');
            for(var j=0;j<mysubwindows.length;j++){
                $(mysubwindows[j]).removeClass('mywindow-selected');
            }
        })
        $('#btn_addFile').on('click',function () {
            $('#adsfileListModal').modal();
            $('#tb_adsfilelist').bootstrapTable('refresh');
        })

        $('#adsfileListModal_commit').on('click',function () {
            var selectIds = $('#tb_adsfilelist').bootstrapTable('getSelections');
            if(!selectIds||selectIds.length==0){
                return;
            }

            var setsub_window_id = $('#setsub_window_id').val();
            if(!setsub_window_id){
                return;
            }
            if(!playlistmap[setsub_window_id]){
                playlistmap[setsub_window_id]=new Array();
            }
            var playlist = playlistmap[setsub_window_id];

            var html = "";
            for(var i=0;i<selectIds.length;i++){
                if(playlist.length>0){
                    var contained=0;
                    for(var j=0;j<playlist.length;j++){
                        if(playlist[j].id==selectIds[i].id){
                            contained=1;
                            break;
                        }
                    }
                    if(contained==1){
                        continue;
                    }
                }
                html=html+"<tr><td>"+selectIds[i].id+"</td><td>"+selectIds[i].filename+"</td><td>"+selectIds[i].typename+"</td><td>"+selectIds[i].starttime+"</td><td>"+selectIds[i].endtime+"</td><td><input class='myinput2' value='1'/></td><td style='display:none'><span name='order-up' class='fa fa-arrow-down'></span>&nbsp;&nbsp;<span name='order-down' class='fa fa-arrow-up'></span></td></tr>"
                // playlist.push(selectIds[i]);
            }
            $('#playlist').append(html);

            // var btnorderups = document.getElementsByName('order-up');
            // var btnorderdowns = document.getElementsByName('order-down');
            // if(btnorderups&&btnorderups.length>0){
            //     for(var i=0;i<btnorderups.length;i++){
            //         $(btnorderups[i]).off('click').on('click',function () {
            //
            //         })
            //     }
            // }
            $('#adsfileListModal').modal('hide');
        })
        $('#setPlayListModal_clear').on('click',function () {
            var setsub_window_id = $('#setsub_window_id').val();
            if(!setsub_window_id){
                return;
            }
            playlistmap[setsub_window_id]=new Array();
            $('#playlist').html('');
        })
        var oTable = new AdsTableInit();
        oTable.Init();

        initLayout();
    });

    function initLayout() {
        $("div[name='mywindow']").each(function () {
            $(this).on('dblclick',function () {
                var mysubwindows = document.getElementsByName('mywindow');
                for(var j=0;j<mysubwindows.length;j++){
                    $(mysubwindows[j]).removeClass('mywindow-selected');
                }
                $(this).addClass('mywindow-selected');
                $('#setsub_window_id').val(this.id);
                initPlayListModal();
                $('#setPlayListModal').modal();
            })
        });
    }

    function initPlayListModal() {
        var setsub_window_id = $('#setsub_window_id').val();
        if(!setsub_window_id){
            $('#playlist').html("");
            return;
        }
        if(!playlistmap[setsub_window_id]){
            playlistmap[setsub_window_id]=new Array();
            $('#playlist').html("");
            return;
        }
        var playlist = playlistmap[setsub_window_id];

        var html = "";
        if(playlist.length>0){
            for(var j=0;j<playlist.length;j++){
                html=html+"<tr><td>"+playlist[j].id+"</td><td>"+playlist[j].filename+"</td><td>"+playlist[j].typename+"</td><td>"+playlist[j].starttime+"</td><td>"+playlist[j].endtime+"</td><td><input class='myinput2' value='"+playlist[j].interval+"'/></td><td style='display:none'><span name='order-up' class='fa fa-arrow-down'></span>&nbsp;&nbsp;<span name='order-down' class='fa fa-arrow-up'></span></td></tr>"
            }
        }
        $('#playlist').html(html);
    }

    var AdsTableInit = function () {
        //var tableheight = document.body.clientHeight;
        var oTableInit = new Object();
        //初始化Table
        oTableInit.Init = function () {
            $('#tb_adsfilelist').bootstrapTable({
                url: '__MODULE__/AdsMgr/api_getlist',         //请求后台的URL（*）
                method: 'post',                      //请求方式（*）
                toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                striped: true,                      //是否显示行间隔色
                cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true,                   //是否显示分页（*）
                sortable: false,                     //是否启用排序
                sortOrder: "asc",                   //排序方式
                queryParams: oTableInit.queryParams,//传递参数（*）
                sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                pageNumber:1,                       //初始化加载第一页，默认第一页
                pageSize: 10,                       //每页的记录行数（*）
                pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                strictSearch: true,
                showColumns: true,                  //是否显示所有的列
                showRefresh: true,                  //是否显示刷新按钮
                minimumCountColumns: 2,             //最少允许的列数
                clickToSelect: false,                //是否启用点击选中行
                //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                cardView: false,                    //是否显示详细视图
                detailView: false,                   //是否显示父子表
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: 'ID'
                }, {
                    field: 'filename',
                    title: '文件名'
                }, {
                    field: 'typename',
                    title: '类型'
                }, {
                    field: 'starttime',
                    title: '开始时间'
                }, {
                    field: 'endtime',
                    title: '结束时间'
                }
                    // ,
                    //     {
                    //     field: 'operatorname',
                    //     title: '运营商'
                    // },
                    //     {
                    //     field: 'starttime',
                    //     title: '起始时间'
                    // }, {
                    //     field: 'endtime',
                    //     title: '结束时间'
                    // },
                    // {
                    //     field: 'createtime',
                    //     title: '创建时间'
                    // }
                    // ,{
                    //     field: 'operate',
                    //     title: '操作',
                    //     width:'100px',
                    //     formatter: operateFormatter //自定义方法，添加操作按钮
                    // }
                ],
                onLoadSuccess:function(){
                    var modifyBtns = $("button[name='modifyBtn']");
                    for(var i=0;i<modifyBtns.length;i++){
                        modifyBtns[i].addEventListener("click",function(){
                            window.location.href = "__MODULE__/AdsFiletaskMgr/index?id="+this.previousSibling.innerText;
                        });
                    }
                }
            });
        };

        //得到查询的参数
        oTableInit.queryParams = function (params) {
            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                limit: params.limit,
                offset: params.offset,
                // name: params.search,
            };
            return temp;
        };
        return oTableInit;
    };
</script>


</body>

</html>
