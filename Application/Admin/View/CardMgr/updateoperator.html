<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改卡用户</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" readonly="readonly" class="form-control" value="{$card.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">卡号：</label>
                            <div class="col-sm-10">
                                <input id="cardnumber" name="cardnumber" type="text" class="form-control" value="{$card.cardnumber}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称：</label>
                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control" value="{$card.name}">
                            </div>
                        </div>
                        <input id="operatorid" value="{$Think.session.userid}" style="display: none"/>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">身份证名称：</label>
                            <div class="col-sm-10">
                                <input id="idcardname" name="idcardname" type="text" class="form-control" value="{$card.idcardname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">身份号：</label>
                            <div class="col-sm-10">
                                <input id="idcardnum" name="idcardnum" type="text" class="form-control" value="{$card.idcardnum}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">手机号：</label>
                            <div class="col-sm-10">
                                <input id="phonenum" name="phonenum" type="text" class="form-control" value="{$card.phonenum}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">物业：</label>
                            <div class="col-sm-10">
                                <select id="propertyid" class="form-control m-b" >
                                    <option value="0">请选择物业</option>
                                    <volist name="propertylist" id="vo">
                                        <option value="{$vo.userid}" <eq name="card.propertyid" value="$vo.userid">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
                cardnumber:$("#cardnumber").val(),
                name:$("#name").val(),
                idcardname:$("#idcardname").val(),
                idcardnum:$("#idcardnum").val(),
                phonenum:$("#phonenum").val(),
                operatorid:$("#operatorid").val(),
                propertyid:$("#propertyid").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

    });


</script>


</body>

</html>
