<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>厂商处理</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">工单ID：</label>

                            <div class="col-sm-10">
                                <input id="woid" name="woid" type="text" readonly="readonly" class="form-control" value="{$solve.woid}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">电站：</label>

                            <div class="col-sm-10">
                                <input id="stationname" name="stationname" type="text" readonly="readonly" class="form-control" value="{$solve.stationname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">关联原工单ID：</label>

                            <div class="col-sm-10">
                                <input id="rel_woid" name="rel_woid" type="text" readonly="readonly" class="form-control" value="{$solve.rel_woid}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="status" disabled="disabled">
                                    <volist name="statusList" id="vo">
                                        <option value="{$vo.value}" <eq name="solve.status" value="$vo.value">selected</eq> >{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">处理类型：</label>

                            <div class="col-sm-10">
                                <input id="solvetype" name="solvetype" type="text" readonly="readonly" class="form-control" value="{$solve.solvetypename}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">故障级别：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="level" disabled="disabled">
                                    <volist name="levelList" id="vo">
                                        <option value="{$vo.value}" <eq name="solve.level" value="$vo.value">selected</eq> >{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">故障原因：</label>

                            <div class="col-sm-10">
                                <input id="reason" name="reason" type="text" class="form-control" value="{$solve.reason}" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">处理人员：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="solveuserid" disabled="disabled">
                                    <volist name="userList" id="vo">
                                        <option value="{$vo.userid}" <eq name="solve.solveuserid" value="$vo.userid">selected</eq> >{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">巡检结果：</label>

                            <div class="col-sm-10">
                                <button id="gotoResult" class="btn btn-primary" type="button">查看巡检结果</button>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <eq name="solve.solvetype" value="0">
                                    <button id="btn_closewo" class="btn btn-success" type="button">已维修，关闭工单</button>
                                </eq>
                                <eq name="solve.solvetype" value="1">
                                    <eq name="solve.servlogstatus" value="-1">
                                        <button id="btn_addservlog" class="btn btn-success" type="button">提交技术服务单</button>
                                    </eq>
                                    <eq name="solve.servlogstatus" value="0">
                                        <button id="btn_modservlog" class="btn btn-success" type="button">技术服务单</button>
                                        <button id="btn_sgs_confirmservlog" class="btn btn-success" type="button">确认</button>
                                        <button id="btn_sgs_cancelservlog" class="btn btn-success" type="button">退回</button>
                                    </eq>
                                    <eq name="solve.servlogstatus" value="1">
                                        <button id="btn_modservlog" class="btn btn-success" type="button">技术服务单</button>
                                        <button id="btn_weixiuwancheng" class="btn btn-success" type="button">维修完成</button>
                                    </eq>
                                    <eq name="solve.servlogstatus" value="2">
                                        <button id="btn_modservlog" class="btn btn-success" type="button">技术服务单</button>
                                        <eq name="roleid" value="5">
                                        <button id="btn_yunweidanweiqueren" class="btn btn-success" type="button">运维单位确认</button>
                                        </eq>
                                    </eq>
                                    <!--<button id="btn_personnelcosts" class="btn btn-info" type="button" >已维修，提交人工费用清单</button>-->
                                </eq>

                                <!--<button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>-->
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#btn_closewo").on("click",function(){
            var req = {
                woid:$("#woid").val(),
                status:3,
                level:$("#level").val(),
                reason:$("#reason").val(),
                solveuserid:$("#solveuserid").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        $("#btn_sgs_cancelservlog").on("click",function(){
            getjson("__CONTROLLER__/sgs_servlog?woid="+$("#woid").val()+"&type=N",function (data) {
                history.back(-1);
            })
        });
        $("#btn_sgs_confirmservlog").on("click",function(){
            getjson("__CONTROLLER__/sgs_servlog?woid="+$("#woid").val()+"&type=Y",function (data) {
                history.back(-1);
            })
        });
        $("#btn_weixiuwancheng").on("click",function(){
            getjson("__CONTROLLER__/weixiuwancheng?woid="+$("#woid").val()+"&type=Y",function (data) {
                history.back(-1);
            })
        });
        $("#btn_yunweidanweiqueren").on("click",function(){
            getjson("__CONTROLLER__/yunweidanweiqueren?woid="+$("#woid").val()+"&type=Y",function (data) {
                history.back(-1);
            })
        });
        $('#gotoResult').on("click",function(){
            window.location.href = "__MODULE__/WoResultMgr/index?woid="+$("#rel_woid").val();
        });
        $('#btn_addservlog').on("click",function(){
            window.location.href = "__CONTROLLER__/addservlog?woid="+$("#woid").val();
        });
        $('#btn_modservlog').on("click",function(){
            window.location.href = "__CONTROLLER__/modservlog?woid="+$("#woid").val();
        });
    });

</script>


</body>

</html>
