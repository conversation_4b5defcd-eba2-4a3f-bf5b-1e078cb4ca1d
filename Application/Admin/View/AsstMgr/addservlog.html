<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>提交技术服务单</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">工单ID：</label>

                            <div class="col-sm-10">
                                <input id="woid" name="woid" type="text" readonly="readonly" class="form-control" value="{$solve.woid}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">实际服务开始时间：</label>

                            <div class="col-sm-10">
                                <input id="starttime" class="form-control layer-date" placeholder="YYYY-MM-DD hh:mm:ss" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">实际服务结束时间：</label>

                            <div class="col-sm-10">
                                <input id="endtime" class="form-control layer-date" placeholder="YYYY-MM-DD hh:mm:ss" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">实际服务人天：</label>

                            <div class="col-sm-10">
                                <input id="manday" name="manday" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">现场服务情况：</label>

                            <div class="col-sm-10">
                                <textarea id="remark" name="remark" type="text" class="form-control" value="{$solve.reason}" rows="5" placeholder="已完成的工作、未完成的工作"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否更换配件：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="replaceflag">
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>

                            </div>
                        </div>

                        <div id="div_parts" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">配件：</label>

                            <div id="wraper_parts" class="col-sm-10">

                            </div>
                        </div>

                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="btn_commit" class="btn btn-success" type="button">提交</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="parts_tmpl" style="display: none">
    <div class="parts_elem">
        <select class="form-control" name="partsid" style="display: inline-block;width: 20%;margin-bottom: 0;">
            <option value="-1">请选择配件</option>
            <volist name="partsList" id="vo">
                <option value="{$vo.id}">{$vo.name}</option>
            </volist>
        </select>
        <input name="model" type="text" class="form-control" style="display: inline-block;width: 10%;" placeholder="型号" readonly>
        <input name="unit" type="text" class="form-control" style="display: inline-block;width: 10%;" placeholder="单位" readonly>
        <input name="unitprice_discount" type="text" class="form-control" style="display: inline-block;width: 10%;" placeholder="单价" readonly>
        <input name="partscount" type="text" class="form-control" style="display: inline-block;width: 10%;" placeholder="请输入个数">
        <button name="btn_del" class="btn btn-success" type="button" style="display: none">删除此行</button>
        <button name="btn_add" class="btn btn-danger" type="button">添加一行</button>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        initDate();
        $("#btn_commit").on("click",function(){
            var partsidObjs=$('#wraper_parts').find("[name=partsid]");
            var parts=new Array();
            partsidObjs.each(function () {
                var partsid=$(this).val();
                var partscount=$(this).parent().find("[name=partscount]").val();
                var partsElem={
                    partsid:partsid,
                    partscount:partscount
                }
                parts.push(partsElem);
            });
            if(parts.length!=0){
                for(var i=0;i<parts.length;i++){
                    if(!parts[i]||!parts[i].partsid||!parts[i].partscount){
                        toastr.error("请选择配件并输入配件数量");
                        return;
                    }
                }
            }
            var req = {
                woid:$("#woid").val(),
                starttime:$("#starttime").val(),
                endtime:$("#endtime").val(),
                manday:$("#manday").val(),
                remark:$("#remark").val(),
                replaceflag:$("#replaceflag").val(),
                parts:parts
            };
            console.log(req);
            $.ajax({
                url:'api_addservlog',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    window.location.href = "__CONTROLLER__/index";
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $('#replaceflag').on("change",function () {
            if($(this).val()==1){
                $('#div_parts').css("display","block");
                addParts(true);
            }else{
                $('#wraper_parts').empty();
                $('#div_parts').css("display","none");
            }
        })
    });
    function initDate() {
        $('#starttime').val(laydate.now(0, 'YYYY-MM-DD hh:mm:ss'));
        $('#endtime').val(laydate.now(7, 'YYYY-MM-DD hh:mm:ss'));
    }
    function addParts(isFirst) {
        var tmpl=document.getElementById("parts_tmpl").outerHTML;
        var tmplObj=$(tmpl);
        if(!isFirst){
            tmplObj.find("[name=btn_del]").css("display","inline-block");
            tmplObj.find("[name=btn_add]").css("display","none");
        }
        tmpl=tmplObj.html();
        $('#wraper_parts').append(tmpl);
        $('[name=btn_del]').off("click").on("click",function () {
            $(this).parents(".parts_elem").remove();
        })
        $('[name=btn_add]').off("click").on("click",function(){
            addParts(false);
        });
        $('[name=partsid]').off("change").on("change",function(){
            var partsid=$(this);
            getjson1("__CONTROLLER__/api_getParts?id="+$(this).val(),function (data) {
                if(data){
                    partsid.parent().find("[name=model]").val(data.model);
                    partsid.parent().find("[name=unit]").val(data.unit);
                    partsid.parent().find("[name=unitprice_discount]").val(data.unitprice_discount);
                }else{
                    partsid.parent().find("[name=model]").val('');
                    partsid.parent().find("[name=unit]").val('');
                    partsid.parent().find("[name=unitprice_discount]").val('');
                }
            },function () {
                partsid.parent().find("[name=model]").val('');
                partsid.parent().find("[name=unit]").val('');
                partsid.parent().find("[name=unitprice_discount]").val('');
            })
        });
    }
</script>


</body>

</html>
