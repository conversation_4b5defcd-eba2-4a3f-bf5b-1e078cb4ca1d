<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">

    <title>积富回收管理后台</title>

    <meta name="keywords" content="管理后台">
    <meta name="description" content="管理后台">

    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->

    <link rel="shortcut icon" href="__PUBLIC__/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
	<link rel="stylesheet" href="__PUBLIC__/css/main.css">
</head>

<body class="fixed-sidebar full-height-layout gray-bg" style="overflow:hidden">
    <div id="wrapper">
        <!--左侧导航开始-->
        <nav class="navbar-default navbar-static-side" role="navigation">
            <div class="nav-close"><i class="fa fa-times-circle"></i>
            </div>
            <div class="sidebar-collapse">
                <ul class="nav" id="side-menu">

                    <li class="nav-header">
                        <div class="dropdown profile-element">
                            <span><img alt="image" class="img-circle" style="height: 40px;" src="__PUBLIC__/img/default_touxiang.png" /></span>
                            <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                                <span class="clear">
                               <span class="block m-t-xs"><strong class="font-bold">{$Think.session.account}</strong></span>
                                <span class="text-muted text-xs block">{$Think.session.rolename}<b class="caret"></b></span>
                                </span>
                            </a>
                            <ul class="dropdown-menu animated fadeInRight m-t-xs">
                                <li><a class="J_menuItem" href="profile.html" style="display: none">个人资料</a>
                                </li>
                                <li><a class="J_menuItem" href="contacts.html" style="display: none">联系我们</a>
                                </li>
                                <li class="divider" style="display: none"></li>
                                <li><a href="__CONTROLLER__/logout">安全退出</a>
                                </li>
                            </ul>
                        </div>
                        <div class="logo-element"><span class="fa fa-arrow-right"></span>
                        </div>
                    </li>
                    <li>
                        <a class="J_menuItem" href="__MODULE__/Homepage">
                            <!--<i class="fa fa-home"></i>-->
                            <span class="nav-label">首页</span>
                        </a>
                    </li>

                    <volist name="menuTrees1" id="vo">
                        <li>
                            <empty name="vo.url">
                                <a class="J_menuItem">
                                    <!--<i class="${menuLevel1.iconclass}"></i>-->
                                    <span class="nav-label">{$vo.menutitle}</span>
                                    <notempty name="vo.subnodes">
                                        <span class="fa arrow"></span>
                                    </notempty>
                                </a>
                                <else />
                                <a class="J_menuItem" href="__APP__/{$vo.url}">
                                    <!--<i class="${menuLevel1.iconclass}"></i>-->
                                    <span class="nav-label">{$vo.menutitle}</span>
                                    <notempty name="vo.subnodes">
                                        <span class="fa arrow"></span>
                                    </notempty>
                                </a>
                            </empty>
                            <ul class="nav nav-second-level">
                                <volist name="vo.subnodes" id="vo2">
                                    <li>
                                        <empty name="vo2.url">
                                            <a class="J_menuItem">
                                                <!--<i class="${menuLevel2.iconclass}"></i>-->
                                                <span class="nav-label">{$vo2.menutitle}</span>
                                                <notempty name="vo2.subnodes">
                                                    <span class="fa arrow"></span>
                                                </notempty>
                                            </a>
                                            <else />
                                            <a class="J_menuItem" href="__APP__/{$vo2.url}">
                                                <!--<i class="${menuLevel2.iconclass}"></i>-->
                                                <span class="nav-label">{$vo2.menutitle}</span>
                                                <notempty name="vo2.subnodes">
                                                    <span class="fa arrow"></span>
                                                </notempty>
                                            </a>
                                        </empty>
                                        <ul class="nav nav-third-level">
                                            <volist name="vo2.subnodes" id="vo3">
                                                <li>
                                                    <empty name="vo3.url">
                                                        <a class="J_menuItem">
                                                            <!--<i class="${menuLevel2.iconclass}"></i>-->
                                                            <span class="nav-label">{$vo3.menutitle}</span>
                                                            <!--<notempty name="vo3.subnodes">-->
                                                                <!--<span class="fa arrow"></span>-->
                                                            <!--</notempty>-->
                                                        </a>
                                                        <else />
                                                        <a class="J_menuItem" href="__APP__/{$vo3.url}">
                                                            <!--<i class="${menuLevel2.iconclass}"></i>-->
                                                            <span class="nav-label">{$vo3.menutitle}</span>
                                                            <notempty name="vo3.subnodes">
                                                                <span class="fa arrow"></span>
                                                            </notempty>
                                                        </a>
                                                    </empty>
                                                </li>
                                            </volist>
                                        </ul>
                                    </li>
                                </volist>
                            </ul>
                        </li>
                    </volist>
                </ul>
            </div>
        </nav>
        <!--左侧导航结束-->
        <!--右侧部分开始-->
        <div id="page-wrapper" class="gray-bg dashbard-1">
            <div class="row border-bottom">
                <div class="navbar-header" style="display: none"><a class="navbar-minimalize minimalize-styl-2 btn btn-primary " href="#"><i class="fa fa-bars"></i> </a>
                    <form role="search" class="navbar-form-custom" method="post" action="search_results.html">
                        <div class="form-group">
                            <input type="text" placeholder="请输入您需要查找的内容 …" class="form-control" name="top-search" id="top-search">
                        </div>
                    </form>
                </div>
            </div>
            <div class="row content-tabs">
                <button class="roll-nav roll-left J_tabLeft"><i class="fa fa-backward"></i>
                </button>
                <nav class="page-tabs J_menuTabs">
                    <div class="page-tabs-content">
                        <a href="javascript:;" class="active J_menuTab" data-id="__MODULE__/Homepage">首页</a>
                    </div>
                </nav>
                <button class="roll-nav roll-right J_tabRight" style="display: none"><i class="fa fa-forward"></i>
                </button>
                <div class="btn-group roll-nav roll-right" style="display: none">
                    <button class="dropdown J_tabClose" data-toggle="dropdown">关闭操作<span class="caret"></span>

                    </button>
                    <ul role="menu" class="dropdown-menu dropdown-menu-right">
                        <li class="J_tabShowActive"><a>定位当前选项卡</a>
                        </li>
                        <li class="divider"></li>
                        <li class="J_tabCloseAll"><a>关闭全部选项卡</a>
                        </li>
                        <li class="J_tabCloseOther"><a>关闭其他选项卡</a>
                        </li>
                    </ul>
                </div>
                <eq name="displaydashboard" value="1">
                    <div class="btn-group roll-nav roll-right" style="">
                        <button onclick="script:window.location.href = '__MODULE__/Dashboard/dashboard'" class="J_tabClose">数据监控</button>
                    </div>
                </eq>
                <a href="__CONTROLLER__/logout" class="roll-nav roll-right J_tabExit"><i class="fa fa fa-sign-out"></i> 退出</a>
            </div>
            <div class="row J_mainContent" id="content-main">
                <iframe class="J_iframe" name="iframe0" width="100%" height="100%" src="__MODULE__/Homepage" frameborder="0" data-id="__MODULE__/Homepage" seamless></iframe>
            </div>
            <div class="footer">
                <div class="pull-right" style="">积富回收管理后台</div>
            </div>
        </div>
        <!--右侧部分结束-->
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="__PUBLIC__/js/plugins/metisMenu/jquery.metisMenu.js"></script>
    <script src="__PUBLIC__/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
    <script src="__PUBLIC__/js/plugins/layer/layer.min.js"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/hplus.js?v=4.1.0"></script>
    <script type="text/javascript" src="__PUBLIC__/js/contabs.js"></script>

    <!-- 第三方插件 -->
    <script src="__PUBLIC__/js/plugins/pace/pace.min.js"></script>

</body>

</html>
