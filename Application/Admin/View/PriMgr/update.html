<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改用户</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户ID：</label>

                            <div class="col-sm-10">
                                <input id="roleid" name="roleid" type="text" readonly="readonly" class="form-control" value="{$user.roleid}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户账号：</label>

                            <div class="col-sm-10">
                                <input id="account" name="account" type="text" class="form-control" value="{$user.account}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户名称：</label>

                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control" value="{$user.name}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">登录密码：</label>

                            <div class="col-sm-10">
                                <input id="userpwd" name="userpwd" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">密码确认：</label>

                            <div class="col-sm-10">
                                <input id="userpwd1" name="userpwd1" type="password" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">手机号码：</label>

                            <div class="col-sm-10">
                                <input id="phonenumber" phonenumber="name" type="text" class="form-control" value="{$user.phonenumber}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户角色：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="roleid">
                                    <volist name="rolelist" id="vo">
                                        <option value="{$vo.roleid}" <eq name="user.roleid" value="$vo.roleid">selected</eq>>{$vo.rolename}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div id="div_orgid" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">所属组织结构：</label>

                            <div class="col-sm-10">
                                <div>
                                    <div id="selected_org" style="display: inline-block"></div>
                                    <input id="selected_orgId" value="{$user.orgid}" style="display: none">
                                    <button id="btn_setorg" type="button" class="btn btn-primary">选择</button>
                                </div>
                            </div>
                        </div>
                        <div id="div_manufacturerid" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">所属生产商：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="manufacturerid">
                                    <volist name="manufacturerist" id="vo">
                                        <option value="{$vo.manufacturerid}" <eq name="user.manufacturerid" value="$vo.manufacturerid">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<include file="OrgTree/orgtree" />

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        initSelect($('#roleid'));
        $("#saveBtn").on("click",function(){
            var userpwd=$("#userpwd").val();
            var userpwd1=$("#userpwd1").val();
            if(userpwd!=userpwd1){
                toastr.error("确认密码不一致！ ");
                return;
            }
            if(userpwd.length==0){
                userpwd='';
            }else{
                userpwd=sha256(userpwd);
            }
            var selected_orgId = $('#selected_orgId').val();
            if(!selected_orgId){
                toastr.error("请选择所属组织结构！");
                return;
            }

            var req = {
                roleid:$("#roleid").val(),
                account:$("#account").val(),
                name:$("#name").val(),
                userpwd:userpwd,
                phonenumber:$("#phonenumber").val(),
                roleid:$("#roleid").val(),
                selected_orgId:$("#selected_orgId").val(),
                manufacturerid:$("#manufacturerid").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        $('#roleid').on('change',function () {
            initSelect($(this));
        });
        $('#btn_setorg').on('click',function () {
            $('#orgTreeModal').modal({keyboard:false})
            initOrgzTree();
        })
        $('#btnOrgSave').on('click',function () {
            var select = $('#orgTreeview').treeview('getSelected');
            if(select.length!=1){
                toastr.error("请先选择一个节点！");
                return;
            }
            var id=select[0].dataid;
            var name=select[0].text;
            // console.log(getAllParent(select[0]))
            $('#orgTreeModal').modal('hide');

            // console.log("__MODULE__/OrgzMgr/api_getOrgPath?id="+id)
            var req={
                id:id
            }
            postjson("__MODULE__/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
            $('#selected_orgId').val(id);
        })

        var id=$('#selected_orgId').val();
        if(id){
            var req={
                id:id
            }
            postjson("__MODULE__/OrgzMgr/api_getOrgPath",req,function (data) {
                $('#selected_org').html(data)
            })
        }
    });

    function initSelect($roleid) {
        if($roleid.val()=='1'||$roleid.val()=='2'||$roleid.val()=='7'){
            $('#div_orgid').css("display",'none');
            $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='3'){
            $('#div_orgid').css("display",'none');
            $('#div_manufacturerid').css("display",'block');
        }else if($roleid.val()=='4'){
            $('#div_orgid').css("display",'none');
            $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='5'){
            $('#div_orgid').css("display",'block');
            $('#div_manufacturerid').css("display",'none');
        }else if($roleid.val()=='6'){
            $('#div_orgid').css("display",'block');
            $('#div_manufacturerid').css("display",'none');
        }else{
            $('#div_orgid').css("display",'none');
            $('#div_manufacturerid').css("display",'none');
        }
    }

</script>


</body>

</html>
