<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>添加{$wkotypename}自动任务</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">任务名称：</label>

                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">任务开始时间：</label>

                            <div class="col-sm-10">
                                <input id="starttime" class="form-control layer-date" placeholder="YYYY-MM-DD hh:mm:ss" onclick="laydate({istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">任务间隔时间（天）：</label>

                            <div class="col-sm-10">
                                <input id="taskinterval" name="taskinterval" type="text" class="form-control" value="7">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">生成工单开始时间（天）：</label>

                            <div class="col-sm-10">
                                <input id="woistartdelay" name="woistartdelay" type="text" class="form-control" value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">生成工单时长（天）：</label>

                            <div class="col-sm-10">
                                <input id="woitimelength" name="woitimelength" type="text" class="form-control" value="7">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">站点目标范围：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="targetlevel">
                                    <volist name="targetlevels" id="vo">
                                        <option value="{$vo.value}">{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div id="div_targetcity" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">目标城市：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="targetcity">
                                    <volist name="cityList" id="vo">
                                        <option value="{$vo.cityid}">{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div id="div_targetmaintenance" class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">目标运维单位：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="targetmaintenance">
                                    <volist name="maintenanceList" id="vo">
                                        <option value="{$vo.maintenanceid}">{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <!--<div class="form-group">-->
                            <!--<label class="col-sm-2 control-label">目标站点：</label>-->

                            <!--<div class="col-sm-10">-->
                                <!--<input id="targetstation" name="targetstation" type="text" class="form-control">-->
                            <!--</div>-->
                        <!--</div>-->
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        initDate();
        $("#saveBtn").on("click",function(){
            var name=$("#name").val();
            var starttime=$("#starttime").val();
            var taskinterval=$("#taskinterval").val();
            var woistartdelay=$("#woistartdelay").val();
            var woitimelength=$("#woitimelength").val();
            var targetlevel=$("#targetlevel").val();
            var targetcity=$("#targetcity").val();
            var targetmaintenance=$("#targetmaintenance").val();

            var req = {
                name:name,
                starttime:starttime,
                taskinterval:taskinterval,
                woistartdelay:woistartdelay,
                woitimelength:woitimelength,
                targetlevel:targetlevel,
                targetcity:targetcity,
                targetmaintenance:targetmaintenance,
                wkotype:{$wkotype},
            };
            $.ajax({
                url:'api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
        $('#targetlevel').on("change",function () {
            if($(this).val()=='1'){
                $('#div_targetcity').css("display",'none');
                $('#div_targetmaintenance').css("display",'none');
            }else if($(this).val()=='2'){
                $('#div_targetcity').css("display",'block');
                $('#div_targetmaintenance').css("display",'none');
            }else if($(this).val()=='3'){
                $('#div_targetcity').css("display",'none');
                $('#div_targetmaintenance').css("display",'block');
            }
        })
    });
    function initDate() {
        $('#starttime').val(laydate.now(0, 'YYYY-MM-DD hh:mm:ss'));
    }

</script>


</body>

</html>
