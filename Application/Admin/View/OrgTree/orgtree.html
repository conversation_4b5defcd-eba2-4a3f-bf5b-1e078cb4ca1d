    <div class="modal inmodal fade" id="orgTreeModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">选择所属组织</h4>
                </div>
                <div class="modal-body">
                    <div id="orgTreeview" class="test"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">取消</button>
                    <button id="btnOrgSave" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function initOrgzTree() {
            postjson("__MODULE__/OrgzMgr/api_getOrgs","",function (data) {
                // console.log(JSON.stringify(data));
                $('#orgTreeview').treeview({
                    data: data,
                    showCheckbox: false,
                    onNodeSelected: function (event, node) {
                    }
                });
            });
        }

        function getAllParent(node) {
            if(!node){
                return;
            }
            var parentNode = $('#orgTreeview').treeview('getParent', node);
            // console.log(parentNode)
            if(parentNode&&parentNode.dataid){
                var parentNode1 = getAllParent(parentNode);
                // console.log(parentNode1)
                if(parentNode1){
                    return parentNode1+" > "+node.text;
                }else{
                    return parentNode.text+" > "+node.text;
                }
            }else{
                return node.text;
            }
        }
    </script>
