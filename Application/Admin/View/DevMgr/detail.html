<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>设备详情</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备ID：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$dev.id}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备编码：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$dev.code}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备名称：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$dev.name}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">地址：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$dev.address}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">所属回收员：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$collectusername}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">终端系统版本：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$devversion}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">APK软件版本：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$apkversion}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备上报设备号：</label>

                            <div class="col-sm-10">
                                <div class="form-control">{$devcode}</div>
                            </div>
                        </div>
                        <!--<div class="form-group">-->
                            <!--<label class="col-sm-2 control-label">地理位置：</label>-->

                            <!--<div class="col-sm-10">-->
                                <!--<input name="jw" type="hidden" id="jw" value=""/>-->
                                <!--<input name="lat" type="hidden" id="lat" value="{$lat}"/>-->
                                <!--<input name="lng" type="hidden" id="lng" value="{$lng}"/>-->
                                <!--<iframe src="__URL__/findposition/lat/{$lat}/lng/{$lng}" style="border:0px;width:100%;height:600px"></iframe>-->
                            <!--</div>-->
                        <!--</div>-->
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="startBtn" class="btn btn-primary" type="button">开机</button>
                                <button id="stopBtn" class="btn btn-danger" type="button">关机</button>
                                <!--<button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>-->
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
                code:$("#code").val(),
                name:$("#name").val(),
                address:$("#address").val(),
                collectuserid:$("#collectuserid").val(),
                // lat:$("#lat").val(),
                // lng:$("#lng").val(),
            };
            $.ajax({
                url:'__CONTROLLER__/api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

</script>


</body>

</html>
