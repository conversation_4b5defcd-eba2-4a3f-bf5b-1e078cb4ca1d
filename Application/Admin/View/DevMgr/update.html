<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>修改设备</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" readonly="readonly" class="form-control" value="{$dev.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备编码：</label>

                            <div class="col-sm-10">
                                <input id="code" name="code" type="text" readonly="readonly" class="form-control" value="{$dev.code}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">设备名称：</label>

                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" class="form-control" value="{$dev.name}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">地址：</label>

                            <div class="col-sm-10">
                                <input id="address" name="address" type="text" class="form-control" value="{$dev.address}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">客服电话：</label>

                            <div class="col-sm-10">
                                <input id="customerphone" name="customerphone" type="text" class="form-control" value="{$dev.customerphone}">
                            </div>
                        </div>
                        <div class="form-group" style="display: none">
                            <label class="col-sm-2 control-label">所属回收员：</label>

                            <div class="col-sm-10">
                                <select class="form-control m-b" id="collectuserid">
                                    <option value="0">请选择</option>
                                    <volist name="collectuserlist" id="vo">
                                        <option value="{$vo.id}" <eq name="dev.collectuserid" value="$vo.id">selected</eq>>{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <eq name="display_operator" value="1">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">运营商：</label>
                                <div class="col-sm-10">
                                    <select id="operatorid" class="form-control m-b">
                                        <option value="0">请选择运营商</option>
                                        <volist name="operatorlist" id="vo">
                                            <option value="{$vo.userid}" <eq name="dev.operatorid" value="$vo.userid">selected</eq>>{$vo.name}</option>
                                        </volist>
                                    </select>
                                </div>
                            </div>
                        </eq>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">社区：</label>

                            <div class="col-sm-10">
                                <input id="communityname" name="communityname" type="text" class="form-control" value="{$dev.communityname}">
                            </div>
                        </div>

                        <eq name="display_property" value="1">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">物业：</label>
                                <div class="col-sm-10">
                                    <input id="propertyid1" name="propertyid1" type="text" class="form-control" style="display: none" value="{$dev.propertyid}">
                                    <select id="propertyid" class="form-control m-b">
                                        <eq name="display_operator" value="0">
                                            <option value="0">请选择物业</option>
                                            <volist name="propertylist" id="vo">
                                                <option value="{$vo.userid}" <eq name="dev.propertyid" value="$vo.userid">selected</eq>>{$vo.name}</option>
                                            </volist>
                                        </eq>
                                    </select>
                                </div>
                            </div>
                        </eq>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">省份</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="areaid1">
                                    <option value="0">选择省份</option>
                                    <volist name="level1arealist" id="vo">
                                        <option value="{$vo.areaid}" <eq name="dev.areaid1" value="$vo.areaid">selected</eq>>{$vo.areaname}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">城市</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="areaid2">
                                    <option value="0">选择城市</option>
                                    <volist name="level2arealist" id="vo">
                                        <option value="{$vo.areaid}" <eq name="dev.areaid2" value="$vo.areaid">selected</eq>>{$vo.areaname}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">区县</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="areaid3">
                                    <option value="0">选择区县</option>
                                    <volist name="level3arealist" id="vo">
                                        <option value="{$vo.areaid}" <eq name="dev.areaid3" value="$vo.areaid">selected</eq>>{$vo.areaname}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">街道</label>
                            <div class="col-sm-10">
                                <select class="form-control m-b" id="areaid4">
                                    <option value="0">选择街道</option>
                                    <volist name="level4arealist" id="vo">
                                        <option value="{$vo.areaid}" <eq name="dev.areaid4" value="$vo.areaid">selected</eq>>{$vo.areaname}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">辐射户数：</label>
                            <div class="col-sm-10">
                                <input id="households" name="households" type="text" class="form-control" value="{$dev.households}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">终端系统版本：</label>

                            <div class="col-sm-10">
                                <input id="devversion" name="devversion" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">APK软件版本：</label>

                            <div class="col-sm-10">
                                <input id="apkversion" name="apkversion" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">上报设备号：</label>

                            <div class="col-sm-10">
                                <input id="devcode" name="devcode" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">TCP心跳间隔(秒)：</label>

                            <div class="col-sm-10">
                                <input id="heartbeatinterval" name="heartbeatinterval" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">TCP服务器地址：</label>

                            <div class="col-sm-10">
                                <input id="tcpip" name="tcpip" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">TCP服务器端口：</label>

                            <div class="col-sm-10">
                                <input id="tcpport" name="tcpport" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">HTTP服务器地址：</label>

                            <div class="col-sm-10">
                                <input id="httpip" name="httpip" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">HTTP服务器端口：</label>

                            <div class="col-sm-10">
                                <input id="httpport" name="httpport" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">可用时间段：</label>

                            <div class="col-sm-10">
                                <input id="autodooropenclose" name="autodooropenclose" type="text" readonly="readonly" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">欢迎语：</label>
                            <div class="col-sm-10">
                                <input id="welcomemsg" name="welcomemsg" type="text" readonly="readonly" class="form-control" value="{$dev.welcomemsg}">
                            </div>
                        </div>
                        <!--<div class="form-group">-->
                            <!--<label class="col-sm-2 control-label">远程操作：</label>-->

                            <!--<div class="col-sm-10">-->
                                <!--<button id="startBtn" class="btn btn-primary" type="button">开机</button>-->
                                <!--<button id="stopBtn" class="btn btn-danger" type="button">关机</button>-->
                            <!--</div>-->
                        <!--</div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">地理位置：</label>

                            <div class="col-sm-10">
                                <input name="jw" type="hidden" id="jw" value=""/>
                                <input name="lat" type="hidden" id="lat" value="{$lat}"/>
                                <input name="lng" type="hidden" id="lng" value="{$lng}"/>
                                <iframe src="__URL__/findposition/lat/{$lat}/lng/{$lng}" style="border:0px;width:100%;height:600px"></iframe>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
                code:$("#code").val(),
                name:$("#name").val(),
                communityname:$("#communityname").val(),
                address:$("#address").val(),
                customerphone:$("#customerphone").val(),
                collectuserid:$("#collectuserid").val(),
                operatorid:$("#operatorid").val(),
                propertyid:$("#propertyid").val(),
                lat:$("#lat").val(),
                lng:$("#lng").val(),
                areaid1:$("#areaid1").val(),
                areaid2:$("#areaid2").val(),
                areaid3:$("#areaid3").val(),
                areaid4:$("#areaid4").val(),
                households:$('#households').val(),
                // welcomemsg:$("#welcomemsg").val(),
            };
            $.ajax({
                url:'__CONTROLLER__/api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $('#operatorid').on('change',function () {
            refreshProperty();
        })

        $('#areaid1').on('change',function () {
            var req={
                areaid:$('#areaid1').val(),
            }
            postjson("__CONTROLLER__/getSubArea",req,function (data) {
                if(!data||data.length==0){
                    $('#areaid2').html("");
                    return;
                }
                var html="<option value='0'>选择城市</option>"
                for(var i=0;i<data.length;i++){
                    html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                }

                $('#areaid2').html(html);
                $('#areaid3').html('');
                $('#areaid4').html('');
            })
        })

        $('#areaid2').on('change',function () {
            var req={
                areaid:$('#areaid2').val(),
            }
            postjson("__CONTROLLER__/getSubArea",req,function (data) {
                if(!data||data.length==0){
                    $('#areaid3').html("");
                    return;
                }
                var html="<option value='0'>选择区县</option>"
                for(var i=0;i<data.length;i++){
                    html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                }

                $('#areaid3').html(html);
                $('#areaid4').html('');
            })
        })

        $('#areaid3').on('change',function () {
            var req={
                areaid:$('#areaid3').val(),
            }
            postjson("__CONTROLLER__/getSubArea",req,function (data) {
                if(!data||data.length==0){
                    $('#areaid4').html("");
                    return;
                }
                var html="<option value='0'>选择街道</option>"
                for(var i=0;i<data.length;i++){
                    html+="<option value='"+data[i].areaid+"'>"+data[i].areaname+"</option>"
                }

                $('#areaid4').html(html);
            })
        })

        getDevParam();
        refreshProperty();
    });

    function getDevParam() {
        var req = {
            id:$("#id").val(),
        };
        $.ajax({
            url:'__CONTROLLER__/api_getdevparam',
            type:'POST',
            async:true,
            data:JSON.stringify(req),
            timeout:5000,
            dataType:'json',
            success:function(data,textStatus,jqXHR){
                console.log(data)
                if("0"!=data.retCode){
                    // toastr.error("获取设备参数失败！ "+data.data);
                    var text1="从设备获取失败"
                    $('#devversion').val(text1);
                    $('#apkversion').val(text1);
                    $('#devcode').val(text1);
                    $('#heartbeatinterval').val(text1);
                    $('#tcpip').val(text1);
                    $('#tcpport').val(text1);
                    $('#httpip').val(text1);
                    $('#httpport').val(text1);
                    return;
                }
                var res=data.data;
                $('#devversion').val(res.devversion);
                $('#apkversion').val(res.apkversion);
                $('#devcode').val(res.devcode);
                $('#heartbeatinterval').val(res.heartbeatinterval);
                $('#tcpip').val(res.tcpip);
                $('#tcpport').val(res.tcpport);
                $('#httpip').val(res.httpip);
                $('#httpport').val(res.httpport);
                $('#autodooropenclose').val(res.autodooropenclose);
            },
            error:function(xhr,textStatus){
                toastr.error("服务器异常！");
            },
        });
    }

    function refreshProperty() {
        var operatorid = $('#operatorid').val();
        if(operatorid){
            getjson('api_getPropertys?operatorid='+operatorid,function (data) {
                // console.log(data)
                if(!data||data.length==0){
                    $('#propertyid').html("");
                    return;
                }
                var html="<option value='0'>请选择物业</option>"
                var propertyid1=$('#propertyid1').val();
                for(var i=0;i<data.length;i++){
                    if(propertyid1==data[i].userid){
                        html+="<option value='"+data[i].userid+"' selected>"+data[i].name+"</option>"
                    }else{
                        html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                    }

                }

                $('#propertyid').html(html);
            })
        }
    }

</script>


</body>

</html>
