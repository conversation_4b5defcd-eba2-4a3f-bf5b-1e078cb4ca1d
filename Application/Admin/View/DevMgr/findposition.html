<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>获取地理位置</title>

<style type="text/css">
*{margin:0;padding:0;list-style-type:none;}
a,img{border:0;}
body{font:12px/180% Arial, Helvetica, sans-serif, "新宋体";}
.demo{width:100%;height: 400px;}
#l-map{height:590px;width:99%;float:left;border:1px solid #bcbcbc;}
#r-result{height:0px;width:230px;float:right;}
</style>

<script src="https://code.jquery.com/jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=HrZArUYwUrobURinXg7rTAkVdrgahb2h&s=1"></script>
<script type="text/javascript" src="https://developer.baidu.com/map/jsdemo/demo/convertor.js"></script>
		

</head>
<body>

<div class="demo">
	<div id="l-map"></div>
	<div id="r-result"></div>
</div>
	<script type="text/javascript">
		var map = new BMap.Map("l-map");
		// map.centerAndZoom("泰州", 8);
		map.enableScrollWheelZoom();    //启用滚轮放大缩小，默认禁用
		map.enableContinuousZoom();    //启用地图惯性拖拽，默认禁用

		map.addControl(new BMap.NavigationControl());  //添加默认缩放平移控件
		map.addControl(new BMap.OverviewMapControl()); //添加默认缩略地图控件
		//map.addControl(new BMap.OverviewMapControl({ isOpen: true, anchor: BMAP_ANCHOR_BOTTOM_RIGHT }));   //右下角，打开

		var localSearch = new BMap.LocalSearch(map);
		localSearch.enableAutoViewport(); //允许自动调节窗体大小

        var lng="{$lng}";
        if(lng.length>0){
            var point = new BMap.Point("{$lng}","{$lat}");
            map.centerAndZoom(point, 16);
            var markercurrent = new BMap.Marker(point);  // 创建标注，为要查询的地方对应的经纬度
            map.addOverlay(markercurrent);
        }else{
            map.centerAndZoom("郑州", 6);
        }

		map.addEventListener("click",function(e){
			map.clearOverlays();//清空原来的标注
			var marker = new BMap.Marker(new BMap.Point(e.point.lng, e.point.lat));  // 创建标注，为要查询的地方对应的经纬度
			map.addOverlay(marker);
			parent.document.getElementById("jw").value=e.point.lng + "," + e.point.lat;
			parent.document.getElementById("lat").value=e.point.lat;
			parent.document.getElementById("lng").value=e.point.lng;
		});
	</script>

	

</body>
</html>