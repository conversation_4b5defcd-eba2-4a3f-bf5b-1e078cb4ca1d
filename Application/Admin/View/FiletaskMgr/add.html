<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>新增APK升级任务-选择目标设备</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-default"
                                onclick="javascript:window.history.go(-1);">取消</button>
                        <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                    </div>
                </div>
                <input id="apppkgid" value="{$apppkgid}" style="display: none"/>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="ibox-content">
                            <div class="example-wrap">
                                <div class="example">
                                    <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                         role="group">
                                        <input id="search_devcode" class="searchInput" style="margin-left: 3px;width: 130px;" placeholder="设备编码">
                                        <input id="search_devname" class="searchInput" style="margin-left: 3px;width: 130px;" placeholder="设备名称">
                                        <eq name="search_operatorid_visible" value="1">
                                            <select id="search_operatorid" class="searchInput" style="margin-left: 3px;width: 130px;">
                                                <option value="0">选择运营商</option>
                                                <volist name="operatorlist" id="vo">
                                                    <option value="{$vo.userid}">{$vo.name}</option>
                                                </volist>
                                            </select>
                                        </eq>

                                        <eq name="search_propertyid_visible" value="1">
                                            <select id="search_propertyid" class="searchInput" style="margin-left: 3px;width: 130px;">
                                                <option value="0">选择物业</option>
                                                <volist name="propertylist" id="vo">
                                                    <option value="{$vo.userid}">{$vo.name}</option>
                                                </volist>
                                            </select>
                                        </eq>
                                        <button id="btn_search" type="button"
                                                class="btn btn-primary" style="margin-left: 3px;">搜索</button>
                                    </div>
                                    <table id="tb_bootstraptblist">
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/plugins/layer/laydate/laydate.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<!-- Bootstrap table -->
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
<script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
<script src="__PUBLIC__/js/common.js"></script>

<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        //1.初始化Table
        var oTable = new TableInit();
        oTable.Init();

        $("#saveBtn").on("click",function(){
            var selectIds = $('#tb_bootstraptblist').bootstrapTable('getSelections');
            var ids = new Array();
            for(var i=0;i<selectIds.length;i++){
                ids.push(selectIds[i].id);
            }
            var req = {
                idlist:ids,
                apppkgid:$('#apppkgid').val()
            }
            $.ajax({
                url:'__CONTROLLER__/api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $('#search_operatorid').on('change',function () {
            var operatorid = $('#search_operatorid').val();
            if(operatorid){
                getjson('__CONTROLLER__/api_getPropertys?operatorid='+operatorid,function (data) {
                    // console.log(data)
                    if(!data||data.length==0){
                        $('#search_propertyid').html("");
                        return;
                    }
                    var html="<option value='0'>请选择物业</option>"
                    for(var i=0;i<data.length;i++){
                        html+="<option value='"+data[i].userid+"'>"+data[i].name+"</option>"
                    }

                    $('#search_propertyid').html(html);
                })
            }
        })

        $('#btn_search').on("click",function () {
            var opt = {
                query:{
                    offset: 0,
                }
            };
            $('#tb_bootstraptblist').bootstrapTable('refresh',opt);
        })
    });

    var TableInit = function () {
        //var tableheight = document.body.clientHeight;
        var oTableInit = new Object();
        //初始化Table
        oTableInit.Init = function () {
            $('#tb_bootstraptblist').bootstrapTable({
                url: '__CONTROLLER__/api_getdevlist',         //请求后台的URL（*）
                method: 'post',                      //请求方式（*）
                toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                striped: true,                      //是否显示行间隔色
                cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                pagination: true,                   //是否显示分页（*）
                sortable: false,                     //是否启用排序
                sortOrder: "asc",                   //排序方式
                queryParams: oTableInit.queryParams,//传递参数（*）
                sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                pageNumber:1,                       //初始化加载第一页，默认第一页
                pageSize: 200,                       //每页的记录行数（*）
                pageList: [100, 200, 500, 1000],        //可供选择的每页的行数（*）
                search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                strictSearch: true,
                showColumns: false,                  //是否显示所有的列
                showRefresh: false,                  //是否显示刷新按钮
                minimumCountColumns: 2,             //最少允许的列数
                clickToSelect: false,                //是否启用点击选中行
                //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                cardView: false,                    //是否显示详细视图
                detailView: false,                   //是否显示父子表
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: '设备ID'
                }, {
                    field: 'code',
                    title: '设备编码'
                }, {
                    field: 'name',
                    title: '设备名称'
                }, {
                    field: 'address',
                    title: '地址'
                }, {
                    field: 'onlinestatusname',
                    title: '是否在线',
                    formatter: onlinestatusFormatter
                }
                ],
                onLoadSuccess:function(){
                }
            });
        };

        //得到查询的参数
        oTableInit.queryParams = function (params) {
            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                limit: params.limit,
                offset: params.offset,
                devcode :$('#search_devcode').val(),
                devname :$('#search_devname').val(),
                operatorid :$('#search_operatorid').val(),
                propertyid :$('#search_propertyid').val(),
            };
            return temp;
        };
        return oTableInit;
    };

    function onlinestatusFormatter(value, row, index) {
        var html = '';
        if(row['onlinestatus']==1){
            html += '<div style="width:40px;text-align:center;color: green">在线</div>';
        }else{
            html += '<div style="width:40px;text-align:center;color:gray">离线</div>';
        }
        return html;
    }
</script>


</body>

</html>
