<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title" style="">
                        <h5>{$register.name} 用户积分</h5>
                        <div class="pull-right" style="text-align:right;margin:-9px 0;">
                            <button type="button" class="btn btn-success"
                                    onclick="javascript:window.history.go(-1);">返回列表</button>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <input id="userid" style="display: none" value="{$register.id}">
                                <table id="tb_list">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="modPointModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">修改</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <input id="modPointModal_id" value="" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">积分：</label>
                            <div class="col-sm-10">
                                <input id="modPointModal_point" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">角色：</label>
                            <div class="col-sm-10">
                                <select id="modPointModal_roleid" class="form-control m-b" >
                                    <volist name="rolelist" id="vo">
                                        <option value="{$vo.value}">{$vo.name}</option>
                                    </volist>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="modPointModal_commit" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="costPointModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 700px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 id="" class="modal-title">消费积分</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" >
                        <input id="costPointModal_id" value="" style="display: none" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">本次消费积分：</label>
                            <div class="col-sm-10">
                                <input id="costPointModal_point" type="text" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>
                            <div class="col-sm-10">
                                <textarea rows="5" id="costPointModal_remark" type="text" class="form-control"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                    <button id="costPointModal_commit" type="button" class="btn btn-primary">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="__PUBLIC__/js/demo/bootstrap-table-demo.js"></script>
    <script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
    <script src="__PUBLIC__/js/common.js"></script>


    <script>
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            //1.初始化Table
            var oTable = new TableInit();
            oTable.Init();

            //2.初始化Button的点击事件
            var oButtonInit = new ButtonInit();
            oButtonInit.Init();

        });

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_list').bootstrapTable({
                    url: '__CONTROLLER__/api_getpointlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    // toolbar: '#tb_listEventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: false,                  //是否显示所有的列
                    showRefresh: false,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "id",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'point',
                        title: '小程序积分'
                    }, {
                        field: 'propertyname',
                        title: '物业'
                    },{
                        field: 'operatorname',
                        title: '运营商'
                    }, {
                        field: 'cardnum',
                        title: '卡号'
                    }, {
                        field: 'cardpoint',
                        title: '卡积分'
                    }, {
                        field: 'rolename',
                        title: '角色'
                    }, {
                        field: 'cardmasterflagname',
                        title: '是否户主'
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                // window.location.href = "devicedetail.do?id="+this.nextSibling.innerText;
                                var r=confirm("确认解绑该用户当前物业?")
                                if (r!=true){
                                    return;
                                }
                                var id = this.nextSibling.innerText
                                $.ajax({
                                    url:'__APP__/Api/Index/quitCurrentProperty?id='+id,
                                    type:'GET',
                                    async:true,
                                    // data:JSON.stringify(req),
                                    timeout:5000,
                                    dataType:'json',
                                    beforeSend:function(xhr){
                                    },
                                    success:function(data,textStatus,jqXHR){
                                        if("0"!=data.retCode){
                                            toastr.error("解绑失败！");
                                            return;
                                        }

                                        toastr.success("解绑成功！");
                                        $('#tb_list').bootstrapTable('refresh');
                                        return;
                                    },
                                    error:function(xhr,textStatus){
                                        toastr.error("服务器异常！");
                                    },
                                });
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                // window.location.href = "__CONTROLLER__/updatepoint?id="+this.previousSibling.innerText;
                                var tmp=this.nextSibling.innerText;
                                var tmps=tmp.split(tmp);
                                var tmps=tmp.split(",");
                                $('#modPointModal_point').val(tmps[0]);
                                $('#modPointModal_roleid').val(tmps[1]);
                                $('#modPointModal_id').val(tmps[2]);
                                $('#modPointModal').modal();
                            });
                        }

                        var costBtn = $("button[name='costBtn']");
                        for(var i=0;i<costBtn.length;i++){
                            costBtn[i].addEventListener("click",function(){
                                // window.location.href = "DevMgr/update?id="+this.previousSibling.innerText;
                                var id=this.nextSibling.innerText;
                                $('#costPointModal_id').val(id);
                                $('#costPointModal').modal({keyboard:false})
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    // devname: params.search,
                    userid :$('#userid').val(),
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:100px;text-align:center;">';
            // html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="">解绑</button>';
            if({$Think.session.roleid}!=6)
            {
                html += '<div style="display:none;">' + row['id'] + '</div>';
                html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">修改</button>';
                html += '<div style="display:none;">' + row['point'] + ',' + row['roleid'] + ',' + row['id'] + '</div>';
            }else{
                html += '<button name="costBtn" type="button" class="btn btn-info btn-sm" href="#" style="">消费积分</button>';
                html += '<div style="display:none;">'+row['id']+'</div>';
            }
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var r=confirm("确认删除?")
                    if (r!=true){
                        return;
                    }
                    var selectDevs = $('#tb_list').bootstrapTable('getSelections');
                    var users = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        users.push(selectDevs[i].id);
                    }
                    var req = {
                        idlist:users
                    }

                    $.ajax({
                        url:'__CONTROLLER__/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });

                $('#btn_search').on("click",function () {
                    var opt = {
                        query:{
                            offset: 0,
                        }
                    };
                    $('#tb_list').bootstrapTable('refresh',opt);
                })
                $('#modPointModal_commit').on('click',function () {
                    var req = {
                        id:$('#modPointModal_id').val(),
                        point:$('#modPointModal_point').val(),
                        roleid:$('#modPointModal_roleid').val(),
                    }

                    $.ajax({
                        url:'__CONTROLLER__/api_modpoint',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(req),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("操作失败！");
                                return;
                            }

                            toastr.success("操作成功！");
                            $('#modPointModal').modal('hide');
                            $('#tb_list').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                })

                $('#costPointModal_commit').on('click',function () {
                    var req={
                        id:$('#costPointModal_id').val(),
                        point:$('#costPointModal_point').val(),
                        remark:$('#costPointModal_remark').val(),
                    }
                    postjson('__CONTROLLER__/costPoint',req,function (data) {
                        $('#costPointModal').modal('hide');
                        $('#tb_list').bootstrapTable('refresh');
                    })
                })
            };

            return oInit;
        };

    </script>
</body>

</html>
