<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title>管理后台示例</title>

    <link rel="shortcut icon" href="favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">

    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/treeview/bootstrap-treeview.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInUp">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox-content">
                    <eq name="privilege" value="1">
                        <div style="margin: 0 0 10px 0;text-align: right;">
                            <button id="btn_add" type="button" class="btn btn-primary">新增子节点</button>
                            <button id="btn_mod" type="button" class="btn btn-success">修改节点</button>
                            <button id="btn_del" type="button" class="btn btn-danger">删除节点</button>
                        </div>
                    </eq>
                    <div id="treeview1" class="test"></div>
                </div>
            </div>
            <div class="col-sm-6" style="display: none">
                <div class="ibox">
                    <div class="ibox-title" style=""><h5>用户列表</h5></div>
                    <div class="ibox-content">
                        <div class="example-wrap">
                            <div class="example">
                                <div class="btn-group hidden-xs" id="tb_EventsToolbar"
                                     role="group">
                                    <button id="testBtn" type="button" style="display: none"
                                            class="btn btn-outline btn-default"
                                            onclick="script:window.location.href = '__CONTROLLER__/add'">
                                        <i class="glyphicon glyphicon-plus" aria-hidden="true"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline btn-default"
                                            style="display: none;">
                                        <i class="glyphicon glyphicon-heart" aria-hidden="true"></i>
                                    </button>
                                    <button id="delete" type="button" style="display: none"
                                            class="btn btn-outline btn-default">
                                        <i class="glyphicon glyphicon-trash" aria-hidden="true"></i>
                                    </button>
                                </div>
                                <table id="tb_bootstraptblist">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="addModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">新增子节点</h4>
                </div>
                <div class="modal-body">
                    <form id="addForm" class="form-horizontal" >
                        <input class="form-control" id="add_parentId" value="" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称：</label>
                            <div class="col-sm-10">
                                <input class="form-control" id="add_name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">序号：</label>
                            <div class="col-sm-10">
                                <input class="form-control" id="add_ordernum" value="0">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">取消</button>
                    <button id="btnAdd" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal fade" id="modModal" tabindex="-1" role="dialog"  aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 600px;">
            <div class="modal-content">
                <div class="modal-header" style="padding: 10px 15px;">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title">修改节点</h4>
                </div>
                <div class="modal-body">
                    <form id="modForm" class="form-horizontal" >
                        <input class="form-control" id="mod_id" value="" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称：</label>
                            <div class="col-sm-10">
                                <input class="form-control" id="mod_name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">序号：</label>
                            <div class="col-sm-10">
                                <input class="form-control" id="mod_ordernum" value="0">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">取消</button>
                    <button id="btnMod" type="button" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
    <!-- Bootstrap table -->
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
    <script src="__PUBLIC__/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- Peity -->
    <script src="__PUBLIC__/js/demo/bootstrap-table-demo.js"></script>
    <script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
    <script src="__PUBLIC__/js/plugins/treeview/bootstrap-treeview.js"></script>
    <script src="__PUBLIC__/js/common.js"></script>


    <script>
        var level=-1;
        var levelid=-1;
        $(document).ready(function(){
            toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
            initOrgzTree();
            //1.初始化Table
            // var oTable = new TableInit();
            // oTable.Init();

            //2.初始化Button的点击事件
            // var oButtonInit = new ButtonInit();
            // oButtonInit.Init();
            $('#btn_add').on('click',function () {
                var select = $('#treeview1').treeview('getSelected');
                if(select.length!=1){
                    toastr.error("请先选择一个父节点！");
                    return;
                }
                var parentId=select[0].dataid;
                console.log(parentId)
                $('#addForm')[0].reset();
                $('#addModal').modal({keyboard:false})
                $('#add_parentId').val(parentId)
            })
            $('#btnAdd').on("click",function () {
                var req={
                    parentId:$('#add_parentId').val(),
                    name:$('#add_name').val(),
                    ordernum:$('#add_ordernum').val(),
                }
                postjson("OrgzMgr/api_addOrg",req,function (data) {
                    $('#addModal').modal('hide');
                    initOrgzTree();
                });
            })
            $('#btn_mod').on('click',function () {
                var select = $('#treeview1').treeview('getSelected');
                if(select.length!=1){
                    toastr.error("请先选择一个节点！");
                    return;
                }
                var id=select[0].dataid;
                var name=select[0].text;
                var ordernum=select[0].ordernum;
                console.log(select[0])
                $('#modForm')[0].reset();
                $('#modModal').modal({keyboard:false})
                $('#mod_id').val(id)
                $('#mod_name').val(name)
                $('#mod_ordernum').val(ordernum)
            })
            $('#btnMod').on("click",function () {
                var req={
                    id:$('#mod_id').val(),
                    name:$('#mod_name').val(),
                    ordernum:$('#mod_ordernum').val(),
                }
                postjson("OrgzMgr/api_modOrg",req,function (data) {
                    $('#modModal').modal('hide');
                    initOrgzTree();
                });
            })

            $('#btn_del').on("click",function () {
                var select = $('#treeview1').treeview('getSelected');
                if(select.length!=1){
                    toastr.error("请选择要删除的节点！");
                    return;
                }
                var id=select[0].dataid;
                var req={
                    id:id
                }
                postjson("OrgzMgr/api_delOrg",req,function (data) {
                    initOrgzTree();
                });
            })
        });

        function initOrgzTree() {
            postjson("OrgzMgr/api_getOrgs","",function (data) {
                var citys=data;
                // console.log(JSON.stringify(data));
                $('#treeview1').treeview({
                    data: data,
                    showCheckbox: false,
                    onNodeSelected: function (event, node) {
                        // level=node.level;
                        // levelid=node.dataid;
                        // $('#tb_bootstraptblist').bootstrapTable('refresh');
                    }
                });
            });
        }

        var TableInit = function () {
            //var tableheight = document.body.clientHeight;
            var oTableInit = new Object();
            //初始化Table
            oTableInit.Init = function () {
                $('#tb_bootstraptblist').bootstrapTable({
                    url: 'OrgzMgr/api_getUserlist',         //请求后台的URL（*）
                    method: 'post',                      //请求方式（*）
                    toolbar: '#tb_EventsToolbar',                //工具按钮用哪个容器
                    striped: true,                      //是否显示行间隔色
                    cache: false,                       //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
                    pagination: true,                   //是否显示分页（*）
                    sortable: false,                     //是否启用排序
                    sortOrder: "asc",                   //排序方式
                    queryParams: oTableInit.queryParams,//传递参数（*）
                    sidePagination: "server",           //分页方式：client客户端分页，server服务端分页（*）
                    pageNumber:1,                       //初始化加载第一页，默认第一页
                    pageSize: 10,                       //每页的记录行数（*）
                    pageList: [10, 20, 50, 100],        //可供选择的每页的行数（*）
                    search: false,                       //是否显示表格搜索，此搜索是客户端搜索，不会进服务端，所以，个人感觉意义不大
                    strictSearch: true,
                    showColumns: true,                  //是否显示所有的列
                    showRefresh: true,                  //是否显示刷新按钮
                    minimumCountColumns: 2,             //最少允许的列数
                    clickToSelect: false,                //是否启用点击选中行
                    //height: tableheight,                        //行高，如果没有设置height属性，表格自动根据记录条数觉得表格高度
                    uniqueId: "userid",                     //每一行的唯一标识，一般为主键列
                    showToggle:false,                    //是否显示详细视图和列表视图的切换按钮
                    cardView: false,                    //是否显示详细视图
                    detailView: false,                   //是否显示父子表
                    columns: [{
                        checkbox: true
                    }, {
                        field: 'userid',
                        title: '用户ID'
                    }, {
                        field: 'account',
                        title: '账号'
                    }, {
                        field: 'name',
                        title: '名称'
                    }, {
                        field: 'phonenumber',
                        title: '手机号码'
                    }, {
                        field: 'rolename',
                        title: '角色'
                    }, {
                        field: 'createtime',
                        title: '注册时间'
                    },
                    {
                        field: 'operate',
                        title: '操作',
                        width:'100px',
                        formatter: operateFormatter //自定义方法，添加操作按钮
                    }],
                    onLoadSuccess:function(){
                        var detailBtns = $("button[name='detailBtn']");
                        for(var i=0;i<detailBtns.length;i++){
                            detailBtns[i].addEventListener("click",function(){
                                window.location.href = "devicedetail.do?devid="+this.nextSibling.innerText;
                            });
                        }

                        var modifyBtns = $("button[name='modifyBtn']");
                        for(var i=0;i<modifyBtns.length;i++){
                            modifyBtns[i].addEventListener("click",function(){
                                window.location.href = "UserMgr/update?userid="+this.previousSibling.innerText;
                            });
                        }
                    }
                });
            };

            //得到查询的参数
            oTableInit.queryParams = function (params) {
                var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                    limit: params.limit,
                    offset: params.offset,
                    level: level,
                    levelid: levelid,
                };
                return temp;
            };
            return oTableInit;
        };

        function operateFormatter(value, row, index) {
            var html = '';
            html +='<div style="width:100px;text-align:center;">';
            html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
            html += '<div style="display:none;">'+row['userid']+'</div>';
            html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;display:none;">修改</button>';
            html +='</div>';
            return html;
        }


        var ButtonInit = function () {
            var oInit = new Object();
            var postdata = {};

            oInit.Init = function () {
                //初始化页面上面的按钮事件
                $("#delete").on("click",function(){
                    var selectDevs = $('#tb_bootstraptblist').bootstrapTable('getSelections');
                    var devs = new Array();
                    for(var i=0;i<selectDevs.length;i++){
                        devs.push(selectDevs[i].devid);
                    }
                    var deleteReq = {
                        devidlist:devs
                    }

                    $.ajax({
                        url:'DevMgr/api_del',
                        type:'POST',
                        async:true,
                        data:JSON.stringify(deleteReq),
                        timeout:5000,
                        dataType:'json',
                        beforeSend:function(xhr){
                        },
                        success:function(data,textStatus,jqXHR){
                            if("0"!=data.retCode){
                                toastr.error("删除失败！");
                                return;
                            }

                            toastr.success("删除成功！");
                            $('#tb_bootstraptblist').bootstrapTable('refresh');
                            return;
                        },
                        error:function(xhr,textStatus){
                            toastr.error("服务器异常！");
                        },
                    });
                });
            };

            return oInit;
        };
    </script>
</body>

</html>
