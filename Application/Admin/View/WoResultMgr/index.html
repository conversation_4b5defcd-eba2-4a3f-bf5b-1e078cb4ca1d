<!DOCTYPE HTML>
<html lang="zh-cn">
 
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="width=device-width,initial-scale=1.0" name="viewport">
  <meta content="yes" name="apple-mobile-web-app-capable">
  <meta content="black" name="apple-mobile-web-app-status-bar-style">
  <meta content="telephone=no" name="format-detection">
  <meta content="email=no" name="format-detection">
  <title>系统管理</title>
  <link href="__PUBLIC__/css/plugins/bootstrap_3_3_7/bootstrap.min.css" rel="stylesheet">
  <link href="__PUBLIC__/css/plugins/bootstrap-table_1_11_1/bootstrap-table.min.css" rel="stylesheet">
  <link rel="stylesheet" href="__PUBLIC__/css/plugins/jquery-treegrid/0.2.0/jquery.treegrid.min.css">
    <style>
        .issuepic{
            width: 100%;
            margin-bottom: 5px;
        }
    </style>
</head>
 
<body>
  <div class="container" style="padding: 20px;">
      <div style="text-align: right"><button type="button" class="btn btn-info" onclick="javascript:window.history.go(-1);">返回列表</button></div>
      <h4>工单ID: {$wo.woid}</h4>
      <input id="woid" style="display: none" value="{$wo.woid}">
    <table id="table"></table>
  </div>
  <div class="modal fade" id="picModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
      <div class="modal-dialog">
          <div class="modal-content">
              <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                  <h4 class="modal-title" id="myModalLabel">查看照片</h4>
              </div>
              <div id="piclist"></div>
              <div class="modal-footer" style="text-align: center">
                  <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                  <!--<button id="addRoleSave" type="button" class="btn btn-primary">提交</button>-->
              </div>
          </div><!-- /.modal-content -->
      </div><!-- /.modal -->
  </div>
</body>
<script src="__PUBLIC__/js/plugins/jquery/3.1.1/jquery.min.js"></script>
<script src="__PUBLIC__/js/bootstrap3.3.7.min.js"></script>
<!-- <script src="https://cdn.bootcss.com/bootstrap-table/1.11.1/bootstrap-table.min.js"></script> -->
<script src="__PUBLIC__/js/plugins/bootstrap-table/1.12.1/bootstrap-table.min.js"></script>
<!--<script src="http://issues.wenzhixin.net.cn/bootstrap-table/assets/bootstrap-table/src/bootstrap-table.js"></script>  -->
<script src="__PUBLIC__/js/plugins/bootstrap-table/1.12.0/extensions/treegrid/bootstrap-table-treegrid.js"></script>
<script src="__PUBLIC__/js/plugins/jquery-treegrid/0.2.0/jquery.treegrid.min.js"></script>
<script src="__PUBLIC__/js/common.js"></script>
<script type="text/javascript">
    // var data1 = JSON.parse('[{"id":1,"pid":0,"status":1,"name":"系统管理","permissionValue":"open:system:get"},{"id":2,"pid":0,"status":1,"name":"字典管理","permissionValue":"open:dict:get"},{"id":20,"pid":1,"status":1,"name":"新增系统","permissionValue":"open:system:add"},{"id":200,"pid":20,"status":1,"name":"新增系统","permissionValue":"open:system:add"},{"id":201,"pid":20,"status":1,"name":"新增系统","permissionValue":"open:system:add"},{"id":2001,"pid":200,"status":1,"name":"新增系统","permissionValue":"open:system:add"},{"id":2002,"pid":200,"status":1,"name":"新增系统","permissionValue":"open:system:add"},{"id":21,"pid":1,"status":1,"name":"编辑系统","permissionValue":"open:system:edit"},{"id":22,"pid":1,"status":1,"name":"删除系统","permissionValue":"open:system:delete"},{"id":33,"pid":2,"status":1,"name":"系统环境","permissionValue":"open:env:get"},{"id":333,"pid":33,"status":1,"name":"新增环境","permissionValue":"open:env:add"},{"id":3333,"pid":33,"status":1,"name":"编辑环境","permissionValue":"open:env:edit"},{"id":233332,"pid":33,"status":0,"name":"删除环境","permissionValue":"open:env:delete"}]')

    // console.log(data1)
    var $table = $('#table');
    var woid;
  $(function() {
      woid =$("#woid").val();
      var req={
          woid:$("#woid").val(),
      }
    postjson("getItemTree",req,function (data) {
        // data=JSON.stringify(data);
        // data = JSON.parse('');
        // console.log(data)
        $table.bootstrapTable({
            data:data,
            idField: 'id',
            dataType:'jsonp',
            columns: [
                {
                    field: 'check',
                    checkbox: true,
                    formatter: function (value, row, index) {
                        if (row.check == true) {
                            console.log(row.serverName);
                            return {
                                checked: true//设置选中
                            };
                        }
                    }
                },
                {
                    field: 'name',
                    title: '巡检项'
                },
                // {field: 'id', title: '编号', sortable: true, align: 'center'},
                // {field: 'pid', title: '所属上级'},
                {
                    field: 'result',
                    title: '结果',
                    sortable: true,
                    align: 'center',
                    formatter: 'resultFormatter'
                },
                {
                    field: 'issuedesc',
                    title: '问题描述'
                },
                // {
                //     field: 'issuepic',
                //     title: '现场照片'
                // }
                {
                    field: 'operate',
                    title: '操作',
                    width:'100px',
                    formatter: picFormatter //自定义方法，添加操作按钮
                }
            ],
            // bootstrap-table-tree-column.js 插件配置
            // treeShowField: 'name',
            // parentIdField: 'pid'
            // bootstrap-table-tree-column.js 插件配置
            // bootstrap-table-treegrid.js 插件配置
            treeShowField: 'name',
            parentIdField: 'parentid',
            onResetView: function(data) {
                console.log('load');
                // jquery.treegrid.js
                $table.treegrid({
                    // initialState: 'collapsed',
                    treeColumn: 1,
                    // expanderExpandedClass: 'glyphicon glyphicon-minus',
                    // expanderCollapsedClass: 'glyphicon glyphicon-plus',
                    onChange: function() {
                        $table.bootstrapTable('resetWidth');
                    }
                });

                var modifyBtns = $("button[name='modifyBtn']");
                for(var i=0;i<modifyBtns.length;i++){
                    modifyBtns[i].addEventListener("click",function(){
                        // window.location.href = "WoXSListMgr/update?woid="+this.previousSibling.innerText;
                        console.log(this.previousSibling.innerText)

                        getjson("__CONTROLLER__/getPicList?woid="+woid+"&itemid="+this.previousSibling.innerText,function (data) {
                            console.log(data)
                            if(data&&data.length>0){
                                var html="";
                                data.forEach(function (currentValue, index, arr) {
                                    if(currentValue.length>0){
                                        html+="<img src='"+currentValue+"' class='issuepic'/>";
                                    }
                                });
                                $('#piclist').html(html);
                                $('#picModal').modal({
                                    keyboard: false
                                });
                            }
                        })
                    });
                }
            },
            onCheck:function(row){
                var datas = $table.bootstrapTable('getData');
                // 勾选子类
                selectChilds(datas,row,"id","parentid",true);

                // 勾选父类
                selectParentChecked(datas,row,"id","parentid")

                // 刷新数据
                $table.bootstrapTable('load', datas);
            },

            onUncheck:function(row){
                var datas = $table.bootstrapTable('getData');
                selectChilds(datas,row,"id","parentid",false);
                $table.bootstrapTable('load', datas);
            },
            // bootstrap-table-treetreegrid.js 插件配置
            onLoadSuccess:function(row){
                // var detailBtns = $("button[name='detailBtn']");
                // for(var i=0;i<detailBtns.length;i++){
                //     detailBtns[i].addEventListener("click",function(){
                //         window.location.href = "devicedetail.do?woid="+this.nextSibling.innerText;
                //     });
                // }
                console.log("onLoadSuccess")

            },
        });
    });

  });
  // 格式化类型
  function typeFormatter(value, row, index) {
    if (value === 'menu') {
      return '菜单';
    }
    if (value === 'button') {
      return '按钮';
    }
    if (value === 'api') {
      return '接口';
    }
    return '-';
  }
  // 格式化状态
  function resultFormatter(value, row, index) {
    if (value === 1) {
      return '<span class="label label-danger">故障</span>';
    } else if (value === 0) {
      return '<span class="label label-success">完好</span>';
    }else{
        return '';
    }
  }

    function picFormatter(value, row, index) {
      if(!row['issuepic']||row['issuepic'].length==0){
          return;
      }
        var html = '';
        html +='<div style="width:100px;text-align:center;">';
        html += '<button name="detailBtn" type="button" class="btn btn-info btn-sm" href="#" style="display: none">详情</button>';
        html += '<div style="display:none;">'+row['id']+'</div>';
        html += '<button name="modifyBtn" type="button" class="btn btn-success btn-sm" href="#" style="margin-left:3px;">查看照片</button>';
        html +='</div>';
        return html;
    }
        /**
         * 选中父项时，同时选中子项
         * @param datas 所有的数据
         * @param row 当前数据
         * @param id id 字段名
         * @param pid 父id字段名
         */
        function selectChilds(datas,row,id,pid,checked) {
            for(var i in datas){
                if(datas[i][pid] == row[id]){
                    datas[i].check=checked;
                    selectChilds(datas,datas[i],id,pid,checked);
                };
            }
        }
 
        function selectParentChecked(datas,row,id,pid){
             for(var i in datas){
                if(datas[i][id] == row[pid]){
                    datas[i].check=true;
                    selectParentChecked(datas,datas[i],id,pid);
                };
            } 
        }
    </script>
 
</html>
