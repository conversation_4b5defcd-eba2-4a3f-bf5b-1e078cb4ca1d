<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>查看消息</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" class="form-control" readonly="readonly" value="{$msg.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">标题：</label>

                            <div class="col-sm-10">
                                <input id="title" name="title" type="text" class="form-control" readonly="readonly" value="{$msg.title}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">内容：</label>

                            <div class="col-sm-10">
                                <textarea id="content" name="content" type="text" class="form-control" style="height: 200px;" readonly="readonly" >{$msg.content}</textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">类型：</label>

                            <div class="col-sm-10">
                                <input id="typename" name="typename" type="text" class="form-control" readonly="readonly" value="{$msg.typename}"></input>
                            </div>
                        </div>
                        <eq name="msg.type" value="2">
                            <div id="div_userid" class="form-group" >
                                <label class="col-sm-2 control-label">接收用户：</label>

                                <div class="col-sm-10">
                                    <input id="username" name="username" type="text" class="form-control" readonly="readonly" value="{$msg.username}"></input>
                                </div>
                            </div>
                        </eq>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button" style="display: none">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);" style="display: none">返回</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                title:$("#title").val(),
                content:$("#content").val(),
                type:$("#type").val(),
                userid:$("#userid").val(),
            };
            $.ajax({
                url:'api_add',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });

        $('#type').on("change",function () {
            if($(this).val()==1){
                $('#div_userid').css("display","none");
            }else{
                $('#div_userid').css("display","block");
            }
        })
    });

</script>


</body>

</html>
