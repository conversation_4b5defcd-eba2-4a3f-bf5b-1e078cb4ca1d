<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">


    <title>基本表单</title>
    <meta name="keywords" content="南京九则软件科技有限公司">
    <meta name="description" content="南京九则软件科技有限公司">

    <link rel="shortcut icon" href="img/favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.css?v=4.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.css?v=4.1.0" rel="stylesheet">
    <link href="__PUBLIC__/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>充电记录详情</h5>
                    <div class="pull-right" style="text-align:right;margin:-9px 0;">
                        <button type="button" class="btn btn-success"
                                onclick="javascript:window.history.go(-1);">返回列表</button>
                    </div>
                </div>
                <div class="ibox-content">
                    <form class="form-horizontal" >
                        <div class="form-group">
                            <label class="col-sm-2 control-label">ID：</label>

                            <div class="col-sm-10">
                                <input id="id" name="id" type="text" readonly="readonly" class="form-control" value="{$chargelog.id}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户名称：</label>

                            <div class="col-sm-10">
                                <input id="name" name="name" type="text" readonly class="form-control" value="{$chargelog.name}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户手机号：</label>

                            <div class="col-sm-10">
                                <input id="account" name="account" type="text" readonly class="form-control" value="{$chargelog.account}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">卡号：</label>

                            <div class="col-sm-10">
                                <input id="cardnum" name="cardnum" type="text" readonly class="form-control" value="{$chargelog.cardnum}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电站：</label>

                            <div class="col-sm-10">
                                <input id="stationname" name="stationname" type="text" readonly class="form-control" value="{$chargelog.stationname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">桩编号：</label>

                            <div class="col-sm-10">
                                <input id="pilecode" name="pilecode" type="text" readonly class="form-control" value="{$chargelog.pilecode}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">枪号：</label>

                            <div class="col-sm-10">
                                <input id="portnum" name="portnum" type="text" readonly class="form-control" value="{$chargelog.portnum}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">状态：</label>

                            <div class="col-sm-10">
                                <input id="statusname" name="statusname" type="text" readonly class="form-control" value="{$chargelog.statusname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">开始时间：</label>

                            <div class="col-sm-10">
                                <input id="starttime" name="starttime" type="text" readonly class="form-control" value="{$chargelog.starttime}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">结束时间：</label>

                            <div class="col-sm-10">
                                <input id="endtime" name="endtime" type="text" readonly class="form-control" value="{$chargelog.endtime}">
                            </div>
                        </div>




                        <div class="form-group">
                            <label class="col-sm-2 control-label">当前SOC：</label>

                            <div class="col-sm-10">
                                <input id="currentsoc" name="currentsoc" type="text" readonly class="form-control" value="{$chargelog.currentsoc}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">开始SOC：</label>

                            <div class="col-sm-10">
                                <input id="startsoc" name="startsoc" type="text" readonly class="form-control" value="{$chargelog.startsoc}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">结束SOC：</label>

                            <div class="col-sm-10">
                                <input id="endsoc" name="endsoc" type="text" readonly class="form-control" value="{$chargelog.endsoc}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">预支付费用：</label>

                            <div class="col-sm-10">
                                <input id="prepayment" name="prepayment" type="text" readonly class="form-control" value="{$chargelog.prepayment}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">当前充电费用：</label>

                            <div class="col-sm-10">
                                <input id="currentcost" name="currentcost" type="text" readonly class="form-control" value="{$chargelog.currentcost}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">费用：</label>

                            <div class="col-sm-10">
                                <input id="cost" name="cost" type="text" readonly class="form-control" value="{$chargelog.cost}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">返还费用：</label>

                            <div class="col-sm-10">
                                <input id="refund" name="refund" type="text" readonly class="form-control" value="{$chargelog.refund}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">返还费用状态：</label>

                            <div class="col-sm-10">
                                <input id="refundstatusname" name="refundstatusname" type="text" readonly class="form-control" value="{$chargelog.refundstatusname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">剩余充电时间：</label>

                            <div class="col-sm-10">
                                <input id="remaintime" name="remaintime" type="text" readonly class="form-control" value="{$chargelog.remaintime}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电时长：</label>

                            <div class="col-sm-10">
                                <input id="chargetime" name="chargetime" type="text" readonly class="form-control" value="{$chargelog.chargetime}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">累计充电电量：</label>

                            <div class="col-sm-10">
                                <input id="currentelecquantity" name="currentelecquantity" type="text" readonly class="form-control" value="{$chargelog.currentelecquantity}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电电量：</label>

                            <div class="col-sm-10">
                                <input id="electquanlity" name="electquanlity" type="text" readonly class="form-control" value="{$chargelog.electquanlity}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电前电表读数：</label>

                            <div class="col-sm-10">
                                <input id="meterreadingbefore" name="meterreadingbefore" type="text" readonly class="form-control" value="{$chargelog.meterreadingbefore}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">当前电表读数：</label>

                            <div class="col-sm-10">
                                <input id="meterreadingcurrent" name="meterreadingcurrent" type="text" readonly class="form-control" value="{$chargelog.meterreadingcurrent}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">启动方式：</label>

                            <div class="col-sm-10">
                                <input id="startchargemode" name="startchargemode" type="text" readonly class="form-control" value="{$chargelog.startchargemodename}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电策略：</label>

                            <div class="col-sm-10">
                                <input id="chargestrategy" name="chargestrategy" type="text" readonly class="form-control" value="{$chargelog.chargestrategyname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电策略参数：</label>

                            <div class="col-sm-10">
                                <input id="chargestrategyparam" name="chargestrategyparam" type="text" readonly class="form-control" value="{$chargelog.chargestrategyparam}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电前卡余额：</label>

                            <div class="col-sm-10">
                                <input id="balancebefore" name="balancebefore" type="text" readonly class="form-control" value="{$chargelog.balancebefore}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电功率：</label>

                            <div class="col-sm-10">
                                <input id="chargepower" name="chargepower" type="text" readonly class="form-control" value="{$chargelog.chargepower}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电枪类型：</label>

                            <div class="col-sm-10">
                                <input id="acdc" name="acdc" type="text" readonly class="form-control" value="{$chargelog.acdcname}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电结束原因：</label>

                            <div class="col-sm-10">
                                <input id="endresult" name="endresult" type="text" readonly class="form-control" value="{$chargelog.endresult}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">充电流水号：</label>

                            <div class="col-sm-10">
                                <input id="chargeseq" name="chargeseq" type="text" readonly class="form-control" value="{$chargelog.chargeseq}">
                            </div>
                        </div>
                        <div class="hr-line-dashed"></div>
                        <div class="form-group" style="display: none">
                            <div class="col-sm-4 col-sm-offset-2">
                                <button id="saveBtn" class="btn btn-primary" type="button">确定</button>
                                <button class="btn btn-white" type="button" onclick="javascript:history.back(-1);">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.4"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.3.6"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.js?v=1.0.0"></script>
<!-- iCheck -->
<script src="__PUBLIC__/js/plugins/iCheck/icheck.min.js"></script>
<script src="__PUBLIC__/js/sha256.min.js"></script>
<script src="__PUBLIC__/js/plugins/toastr/toastr.min.js"></script>
<script src="__PUBLIC__/js/md5.js"></script>
<script>
    $(document).ready(function () {
        toastr.options = {"positionClass": "toast-top-center","showDuration": "100","timeOut": "1000"};
        $("#saveBtn").on("click",function(){
            var req = {
                id:$("#id").val(),
                account:$("#account").val(),
                name:$("#name").val(),
                openid:$("#openid").val(),
                cardnum:$("#cardnum").val(),
            };
            $.ajax({
                url:'api_update',
                type:'POST',
                async:true,
                data:JSON.stringify(req),
                timeout:5000,
                dataType:'json',
                success:function(data,textStatus,jqXHR){
                    if("0"!=data.retCode){
                        toastr.error("操作失败！ "+data.data);
                        return;
                    }
                    history.back(-1);
                    return;
                },
                error:function(xhr,textStatus){
                    toastr.error("服务器异常！");
                },
            });
        });
    });

</script>


</body>

</html>
