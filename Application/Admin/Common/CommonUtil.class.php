<?php
namespace Admin\Common;
class CommonUtil {
    const server_url = "http://127.0.0.1:18052/jifurecycle/api/recycle/";

    public static function getMenuids($roleid){
        $rolemenu=M('rolemenu');
        $result=$rolemenu->where("roleid=%d",array($roleid))->field('menuid')->select();
        if(empty($result)){
            return null;
        }
        $menuids=array();
        for($i=0;$i<count($result);$i++){
            $menuids[$i]=$result[$i]['menuid'];
        }
        return $menuids;
    }

    public static function setParamNotify($propertyid){
        $req = array();
        $req['propertyid'] = $propertyid;

        $url = self::server_url."setParamNotify.do";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl,CURLOPT_POST,true);
        curl_setopt($curl,CURLOPT_POSTFIELDS,json_encode($req));
        $res = curl_exec($curl);
        \Think\Log::record("setParamNotify res=".$res,"INFO");
        $res = json_decode($res,true);
        curl_close($curl);
    }

    public static function getUserAreaLevel($user){
        if(empty($user)){
            return 0;
        }
        $areaid1=$user['areaid1'];
        $areaid2=$user['areaid2'];
        $areaid3=$user['areaid3'];
        $areaid4=$user['areaid4'];
        if(!empty($areaid4)){
            return 4;
        }
        if(!empty($areaid3)){
            return 3;
        }
        if(!empty($areaid2)){
            return 2;
        }
        if(!empty($areaid1)){
            return 1;
        }
        return 0;
    }
}