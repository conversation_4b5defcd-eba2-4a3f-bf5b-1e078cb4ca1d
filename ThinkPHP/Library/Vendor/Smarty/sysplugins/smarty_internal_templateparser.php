<?php
/**
* Smarty Internal Plugin Templateparser
*
* This is the template parser.
* It is generated from the internal.templateparser.y file
* @package Smarty
* @subpackage Compiler
* <AUTHOR> Tews
*/

class TP_yyToken implements ArrayAccess
{
    public $string = '';
    public $metadata = array();

    function __construct($s, $m = array())
    {
        if ($s instanceof TP_yyToken) {
            $this->string = $s->string;
            $this->metadata = $s->metadata;
        } else {
            $this->string = (string) $s;
            if ($m instanceof TP_yyToken) {
                $this->metadata = $m->metadata;
            } elseif (is_array($m)) {
                $this->metadata = $m;
            }
        }
    }

    function __toString()
    {
        return $this->_string;
    }

    function offsetExists($offset)
    {
        return isset($this->metadata[$offset]);
    }

    function offsetGet($offset)
    {
        return $this->metadata[$offset];
    }

    function offsetSet($offset, $value)
    {
        if ($offset === null) {
            if (isset($value[0])) {
                $x = ($value instanceof TP_yyToken) ?
                    $value->metadata : $value;
                $this->metadata = array_merge($this->metadata, $x);
                return;
            }
            $offset = count($this->metadata);
        }
        if ($value === null) {
            return;
        }
        if ($value instanceof TP_yyToken) {
            if ($value->metadata) {
                $this->metadata[$offset] = $value->metadata;
            }
        } elseif ($value) {
            $this->metadata[$offset] = $value;
        }
    }

    function offsetUnset($offset)
    {
        unset($this->metadata[$offset]);
    }
}

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
};


#line 12 "smarty_internal_templateparser.y"
class Smarty_Internal_Templateparser#line 79 "smarty_internal_templateparser.php"
{
#line 14 "smarty_internal_templateparser.y"

    const Err1 = "Security error: Call to private object member not allowed";
    const Err2 = "Security error: Call to dynamic object member not allowed";
    const Err3 = "PHP in template not allowed. Use SmartyBC to enable it";
    // states whether the parse was successful or not
    public $successful = true;
    public $retvalue = 0;
    private $lex;
    private $internalError = false;

    function __construct($lex, $compiler) {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->smarty = $this->compiler->smarty;
        $this->template = $this->compiler->template;
        $this->compiler->has_variable_string = false;
        $this->compiler->prefix_code = array();
        $this->prefix_number = 0;
        $this->block_nesting_level = 0;
        if ($this->security = isset($this->smarty->security_policy)) {
            $this->php_handling = $this->smarty->security_policy->php_handling;
        } else {
            $this->php_handling = $this->smarty->php_handling;
        }
        $this->is_xml = false;
        $this->asp_tags = (ini_get('asp_tags') != '0');
        $this->current_buffer = $this->root_buffer = new _smarty_template_buffer($this);
    }

    public static function escape_start_tag($tag_text) {
        $tag = preg_replace('/\A<\?(.*)\z/', '<<?php ?>?\1', $tag_text, -1 , $count); //Escape tag
        return $tag;
    }

    public static function escape_end_tag($tag_text) {
        return '?<?php ?>>';
    }

    public function compileVariable($variable) {
        if (strpos($variable,'(') == 0) {
            // not a variable variable
            $var = trim($variable,'\'');
            $this->compiler->tag_nocache=$this->compiler->tag_nocache|$this->template->getVariable($var, null, true, false)->nocache;
            $this->template->properties['variables'][$var] = $this->compiler->tag_nocache|$this->compiler->nocache;
        }
//       return '(isset($_smarty_tpl->tpl_vars['. $variable .'])?$_smarty_tpl->tpl_vars['. $variable .']->value:$_smarty_tpl->getVariable('. $variable .')->value)';
        return '$_smarty_tpl->tpl_vars['. $variable .']->value';
    }
#line 131 "smarty_internal_templateparser.php"

    const TP_VERT                           =  1;
    const TP_COLON                          =  2;
    const TP_COMMENT                        =  3;
    const TP_PHPSTARTTAG                    =  4;
    const TP_PHPENDTAG                      =  5;
    const TP_ASPSTARTTAG                    =  6;
    const TP_ASPENDTAG                      =  7;
    const TP_FAKEPHPSTARTTAG                =  8;
    const TP_XMLTAG                         =  9;
    const TP_OTHER                          = 10;
    const TP_LINEBREAK                      = 11;
    const TP_LITERALSTART                   = 12;
    const TP_LITERALEND                     = 13;
    const TP_LITERAL                        = 14;
    const TP_LDEL                           = 15;
    const TP_RDEL                           = 16;
    const TP_DOLLAR                         = 17;
    const TP_ID                             = 18;
    const TP_EQUAL                          = 19;
    const TP_PTR                            = 20;
    const TP_LDELIF                         = 21;
    const TP_LDELFOR                        = 22;
    const TP_SEMICOLON                      = 23;
    const TP_INCDEC                         = 24;
    const TP_TO                             = 25;
    const TP_STEP                           = 26;
    const TP_LDELFOREACH                    = 27;
    const TP_SPACE                          = 28;
    const TP_AS                             = 29;
    const TP_APTR                           = 30;
    const TP_LDELSETFILTER                  = 31;
    const TP_SMARTYBLOCKCHILD               = 32;
    const TP_LDELSLASH                      = 33;
    const TP_INTEGER                        = 34;
    const TP_COMMA                          = 35;
    const TP_OPENP                          = 36;
    const TP_CLOSEP                         = 37;
    const TP_MATH                           = 38;
    const TP_UNIMATH                        = 39;
    const TP_ANDSYM                         = 40;
    const TP_ISIN                           = 41;
    const TP_ISDIVBY                        = 42;
    const TP_ISNOTDIVBY                     = 43;
    const TP_ISEVEN                         = 44;
    const TP_ISNOTEVEN                      = 45;
    const TP_ISEVENBY                       = 46;
    const TP_ISNOTEVENBY                    = 47;
    const TP_ISODD                          = 48;
    const TP_ISNOTODD                       = 49;
    const TP_ISODDBY                        = 50;
    const TP_ISNOTODDBY                     = 51;
    const TP_INSTANCEOF                     = 52;
    const TP_QMARK                          = 53;
    const TP_NOT                            = 54;
    const TP_TYPECAST                       = 55;
    const TP_HEX                            = 56;
    const TP_DOT                            = 57;
    const TP_SINGLEQUOTESTRING              = 58;
    const TP_DOUBLECOLON                    = 59;
    const TP_AT                             = 60;
    const TP_HATCH                          = 61;
    const TP_OPENB                          = 62;
    const TP_CLOSEB                         = 63;
    const TP_EQUALS                         = 64;
    const TP_NOTEQUALS                      = 65;
    const TP_GREATERTHAN                    = 66;
    const TP_LESSTHAN                       = 67;
    const TP_GREATEREQUAL                   = 68;
    const TP_LESSEQUAL                      = 69;
    const TP_IDENTITY                       = 70;
    const TP_NONEIDENTITY                   = 71;
    const TP_MOD                            = 72;
    const TP_LAND                           = 73;
    const TP_LOR                            = 74;
    const TP_LXOR                           = 75;
    const TP_QUOTE                          = 76;
    const TP_BACKTICK                       = 77;
    const TP_DOLLARID                       = 78;
    const YY_NO_ACTION = 590;
    const YY_ACCEPT_ACTION = 589;
    const YY_ERROR_ACTION = 588;

    const YY_SZ_ACTTAB = 2393;
static public $yy_action = array(
 /*     0 */   211,  316,  317,  319,  318,  315,  314,  310,  309,  311,
 /*    10 */   312,  313,  320,  189,  304,  161,   38,  589,   95,  265,
 /*    20 */   317,  319,    7,  106,  289,   37,   26,   30,  146,  283,
 /*    30 */    10,  285,  250,  286,  238,  280,  287,   49,   48,   46,
 /*    40 */    45,   20,   29,  365,  366,   28,   32,  373,  374,   15,
 /*    50 */    11,  328,  323,  322,  324,  327,  231,  211,    4,  189,
 /*    60 */   329,  332,  211,  382,  383,  384,  385,  381,  380,  376,
 /*    70 */   375,  377,  281,  378,  379,  211,  360,  450,  180,  251,
 /*    80 */   117,  144,  196,   74,  135,  260,   17,  451,   26,   30,
 /*    90 */   307,  283,  299,  361,   35,  158,  225,  368,  362,  451,
 /*   100 */   343,   30,   30,  226,   44,  203,  285,    4,   47,  203,
 /*   110 */   218,  259,   49,   48,   46,   45,   20,   29,  365,  366,
 /*   120 */    28,   32,  373,  374,   15,   11,  351,  108,  176,  334,
 /*   130 */   144,  193,  337,   23,  129,  134,  371,  289,  382,  383,
 /*   140 */   384,  385,  381,  380,  376,  375,  377,  281,  378,  379,
 /*   150 */   211,  360,  372,   26,  203,  142,  283,   31,   68,  122,
 /*   160 */   242,   26,  109,  353,  283,  346,  454,  299,  361,   25,
 /*   170 */   185,  225,  368,  362,   30,  343,  239,   30,  454,  289,
 /*   180 */     4,   41,   26,  143,  165,  283,    4,   49,   48,   46,
 /*   190 */    45,   20,   29,  365,  366,   28,   32,  373,  374,   15,
 /*   200 */    11,  101,  160,  144,   26,  208,   34,  283,   31,  144,
 /*   210 */     8,  289,    4,  382,  383,  384,  385,  381,  380,  376,
 /*   220 */   375,  377,  281,  378,  379,  211,  360,  227,  203,  357,
 /*   230 */   142,  197,   19,   73,  135,  144,  211,  302,    9,  158,
 /*   240 */   340,   26,  299,  361,  283,  158,  225,  368,  362,  228,
 /*   250 */   343,  294,   30,    6,  331,  235,  330,  221,  195,  337,
 /*   260 */   126,  240,   49,   48,   46,   45,   20,   29,  365,  366,
 /*   270 */    28,   32,  373,  374,   15,   11,  211,   16,  129,  244,
 /*   280 */   249,  219,  208,  192,  337,  302,  228,    8,  382,  383,
 /*   290 */   384,  385,  381,  380,  376,  375,  377,  281,  378,  379,
 /*   300 */   163,  211,  107,  188,  105,   40,   40,  266,  277,  289,
 /*   310 */   241,  232,  289,   49,   48,   46,   45,   20,   29,  365,
 /*   320 */   366,   28,   32,  373,  374,   15,   11,  211,  168,  203,
 /*   330 */    40,    2,  278,  167,  175,  244,  242,  289,  350,  382,
 /*   340 */   383,  384,  385,  381,  380,  376,  375,  377,  281,  378,
 /*   350 */   379,  191,   47,  184,  204,  234,  169,  198,  287,  386,
 /*   360 */   203,  203,  289,  124,   49,   48,   46,   45,   20,   29,
 /*   370 */   365,  366,   28,   32,  373,  374,   15,   11,  211,  204,
 /*   380 */   182,   26,   26,   26,  283,  212,  224,  118,  131,  289,
 /*   390 */   382,  383,  384,  385,  381,  380,  376,  375,  377,  281,
 /*   400 */   378,  379,  370,  172,  244,  270,  204,  130,  211,  164,
 /*   410 */    26,  287,  289,  229,  178,   49,   48,   46,   45,   20,
 /*   420 */    29,  365,  366,   28,   32,  373,  374,   15,   11,  204,
 /*   430 */   364,  298,    5,   26,  100,   30,  247,  148,  148,   99,
 /*   440 */   159,  382,  383,  384,  385,  381,  380,  376,  375,  377,
 /*   450 */   281,  378,  379,  211,  354,  370,  360,  174,   26,  369,
 /*   460 */   142,  283,  360,   73,  135,  158,  157,  123,   24,  155,
 /*   470 */   135,   30,  299,  361,  211,  190,  225,  368,  362,  272,
 /*   480 */   343,  252,  225,  368,  362,  343,  343,  222,  223,  306,
 /*   490 */    49,   48,   46,   45,   20,   29,  365,  366,   28,   32,
 /*   500 */   373,  374,   15,   11,  129,   43,  236,    9,  269,  258,
 /*   510 */   199,  133,   33,   14,  202,  103,  382,  383,  384,  385,
 /*   520 */   381,  380,  376,  375,  377,  281,  378,  379,  211,  360,
 /*   530 */   370,  170,  262,  142,  360,   36,   73,  135,  151,  141,
 /*   540 */   289,  245,  135,  276,  211,  299,  361,  211,   44,  225,
 /*   550 */   368,  362,  287,  343,  225,  368,  362,  119,  343,  295,
 /*   560 */   216,  267,  282,  296,  211,   49,   48,   46,   45,   20,
 /*   570 */    29,  365,  366,   28,   32,  373,  374,   15,   11,  284,
 /*   580 */   181,  223,  333,  138,  302,  236,  297,    6,  127,  289,
 /*   590 */   116,  382,  383,  384,  385,  381,  380,  376,  375,  377,
 /*   600 */   281,  378,  379,  211,  360,  370,  177,   94,  142,  303,
 /*   610 */   292,   54,  122,  139,  162,  289,  150,  261,  264,  293,
 /*   620 */   299,  361,   30,  289,  225,  368,  362,  287,  343,   30,
 /*   630 */   287,   30,  132,  300,  308,  287,  158,  211,   30,  334,
 /*   640 */    49,   48,   46,   45,   20,   29,  365,  366,   28,   32,
 /*   650 */   373,  374,   15,   11,  211,  204,  166,   12,  275,  287,
 /*   660 */   273,  248,  342,   98,   97,  113,  382,  383,  384,  385,
 /*   670 */   381,  380,  376,  375,  377,  281,  378,  379,  370,  370,
 /*   680 */   370,  110,   18,  321,  324,  324,  324,  324,  324,  324,
 /*   690 */   246,   49,   48,   46,   45,   20,   29,  365,  366,   28,
 /*   700 */    32,  373,  374,   15,   11,  211,  324,  324,  324,  324,
 /*   710 */   324,  324,  324,  324,  112,  136,  104,  382,  383,  384,
 /*   720 */   385,  381,  380,  376,  375,  377,  281,  378,  379,  370,
 /*   730 */   370,  370,  324,  324,  324,  324,  324,  324,  324,  324,
 /*   740 */   324,  256,   49,   48,   46,   45,   20,   29,  365,  366,
 /*   750 */    28,   32,  373,  374,   15,   11,  211,  324,  324,  324,
 /*   760 */   324,  324,  324,  324,  324,  324,  324,  324,  382,  383,
 /*   770 */   384,  385,  381,  380,  376,  375,  377,  281,  378,  379,
 /*   780 */   351,  324,  324,   30,  324,  324,  324,  324,  324,  324,
 /*   790 */   324,  324,  324,   49,   48,   46,   45,   20,   29,  365,
 /*   800 */   366,   28,   32,  373,  374,   15,   11,  211,  324,  324,
 /*   810 */   324,  324,  324,  324,  324,  324,  324,  355,  324,  382,
 /*   820 */   383,  384,  385,  381,  380,  376,  375,  377,  281,  378,
 /*   830 */   379,  324,  324,  324,  324,  324,  324,  324,  324,  324,
 /*   840 */   324,  324,  324,  324,   49,   48,   46,   45,   20,   29,
 /*   850 */   365,  366,   28,   32,  373,  374,   15,   11,  324,  324,
 /*   860 */   324,  324,  324,  324,  324,  324,  324,  324,  324,  257,
 /*   870 */   382,  383,  384,  385,  381,  380,  376,  375,  377,  281,
 /*   880 */   378,  379,  211,  324,  324,  324,  194,  360,  211,  288,
 /*   890 */   255,  145,  324,  352,  336,  135,  324,  200,   42,   22,
 /*   900 */    27,   30,   30,  341,    7,  106,   30,  225,  368,  362,
 /*   910 */   146,  343,  324,  203,  250,  286,  238,  324,  211,   49,
 /*   920 */    48,   46,   45,   20,   29,  365,  366,   28,   32,  373,
 /*   930 */   374,   15,   11,  305,  324,  324,  324,  324,  324,   47,
 /*   940 */   324,  324,  324,  324,  324,  382,  383,  384,  385,  381,
 /*   950 */   380,  376,  375,  377,  281,  378,  379,  211,  324,  359,
 /*   960 */    39,  349,  360,  326,  348,  291,  156,  324,  352,  344,
 /*   970 */   135,  324,  201,   42,  324,   30,   30,   30,  324,    7,
 /*   980 */   106,   30,  225,  368,  362,  146,  343,  324,  324,  250,
 /*   990 */   286,  238,  324,  324,   49,   48,   46,   45,   20,   29,
 /*  1000 */   365,  366,   28,   32,  373,  374,   15,   11,  211,  324,
 /*  1010 */   324,  324,  324,  324,  324,  324,  324,  324,  324,  324,
 /*  1020 */   382,  383,  384,  385,  381,  380,  376,  375,  377,  281,
 /*  1030 */   378,  379,  324,  324,  358,   39,  349,  324,  324,  324,
 /*  1040 */   324,  324,  324,  324,  324,   49,   48,   46,   45,   20,
 /*  1050 */    29,  365,  366,   28,   32,  373,  374,   15,   11,  324,
 /*  1060 */   324,  324,  324,  324,  324,  324,  324,  324,  324,  324,
 /*  1070 */   324,  382,  383,  384,  385,  381,  380,  376,  375,  377,
 /*  1080 */   281,  378,  379,  324,   49,   48,   46,   45,   20,   29,
 /*  1090 */   365,  366,   28,   32,  373,  374,   15,   11,  324,  324,
 /*  1100 */   324,  324,  324,  324,  324,  324,  324,  324,  324,  324,
 /*  1110 */   382,  383,  384,  385,  381,  380,  376,  375,  377,  281,
 /*  1120 */   378,  379,  324,  324,  324,  324,   38,  324,  140,  207,
 /*  1130 */   324,  360,    7,  106,  290,  147,  324,  356,  146,  135,
 /*  1140 */   324,  324,  250,  286,  238,  230,   30,   13,  367,   30,
 /*  1150 */    51,  225,  368,  362,  324,  343,  360,  324,  324,  324,
 /*  1160 */   152,  324,  324,  324,  135,   50,   52,  301,  237,  363,
 /*  1170 */   324,  360,  105,    1,  254,  154,  225,  368,  362,  135,
 /*  1180 */   343,  324,   38,  324,  140,  214,  324,   96,    7,  106,
 /*  1190 */   279,  225,  368,  362,  146,  343,  347,  345,  250,  286,
 /*  1200 */   238,  230,   30,   13,  274,  324,   51,  338,   30,   30,
 /*  1210 */   360,  324,  324,  324,  121,  324,   30,   53,  135,   30,
 /*  1220 */   211,   50,   52,  301,  237,  363,  299,  361,  105,    1,
 /*  1230 */   225,  368,  362,  211,  343,  453,  324,  268,   38,  324,
 /*  1240 */   128,  214,  324,   96,    7,  106,  253,  453,  339,   30,
 /*  1250 */   146,  335,  233,  324,  250,  286,  238,  230,   30,    3,
 /*  1260 */    30,  324,   51,   30,  271,  324,  360,  324,    4,  324,
 /*  1270 */   142,   47,  324,   84,  135,  324,   30,   50,   52,  301,
 /*  1280 */   237,  363,  299,  361,  105,    1,  225,  368,  362,  324,
 /*  1290 */   343,  144,  324,  324,   38,  324,  125,   92,  324,   96,
 /*  1300 */     7,  106,  324,  324,  324,  324,  146,  324,  324,  324,
 /*  1310 */   250,  286,  238,  230,  324,   13,  324,  324,   51,  324,
 /*  1320 */   324,  324,  360,  324,  324,  324,  142,  324,  324,   89,
 /*  1330 */   135,  324,  211,   50,   52,  301,  237,  363,  299,  361,
 /*  1340 */   105,    1,  225,  368,  362,  324,  343,  456,  324,  324,
 /*  1350 */    38,  324,  126,  214,  324,   96,    7,  106,  324,  456,
 /*  1360 */   243,  324,  146,  324,  324,  324,  250,  286,  238,  230,
 /*  1370 */   324,   21,  324,  324,   51,  324,  324,  324,  360,  324,
 /*  1380 */   324,  324,  142,   47,  324,   87,  135,  324,  211,   50,
 /*  1390 */    52,  301,  237,  363,  299,  361,  105,    1,  225,  368,
 /*  1400 */   362,  324,  343,  456,  324,  324,   38,  324,  140,  210,
 /*  1410 */   324,   96,    7,  106,  324,  456,  324,  324,  146,  324,
 /*  1420 */   324,  324,  250,  286,  238,  230,  324,   13,  324,  324,
 /*  1430 */    51,  324,  324,  324,  360,  324,  324,  324,  142,   47,
 /*  1440 */   324,   63,  135,  324,  211,   50,   52,  301,  237,  363,
 /*  1450 */   299,  361,  105,    1,  225,  368,  362,  324,  343,  325,
 /*  1460 */   324,  324,   38,  324,  137,  214,  324,   96,    7,  106,
 /*  1470 */   324,   30,  324,  324,  146,  324,  324,  324,  250,  286,
 /*  1480 */   238,  230,  324,   13,  324,  324,   51,  324,  324,  324,
 /*  1490 */   360,  324,  324,  324,  142,   47,  324,   80,  135,  324,
 /*  1500 */   324,   50,   52,  301,  237,  363,  299,  361,  105,    1,
 /*  1510 */   225,  368,  362,  324,  343,  324,  324,  324,   38,  324,
 /*  1520 */   140,  206,  324,   96,    7,  106,  324,  324,  324,  324,
 /*  1530 */   146,  324,  324,  324,  250,  286,  238,  220,  324,   13,
 /*  1540 */   324,  324,   51,  324,  324,  324,  360,  324,  324,  324,
 /*  1550 */   114,  324,  324,   75,  135,  324,  324,   50,   52,  301,
 /*  1560 */   237,  363,  299,  361,  105,    1,  225,  368,  362,  324,
 /*  1570 */   343,  324,  324,  324,   38,  324,  140,  205,  324,   96,
 /*  1580 */     7,  106,  324,  324,  324,  324,  146,  324,  324,  324,
 /*  1590 */   250,  286,  238,  230,  324,   13,  324,  324,   51,  324,
 /*  1600 */   324,  324,  360,  324,  324,  324,  142,  324,  324,   77,
 /*  1610 */   135,  324,  324,   50,   52,  301,  237,  363,  299,  361,
 /*  1620 */   105,    1,  225,  368,  362,  324,  343,  324,  324,  324,
 /*  1630 */    38,  324,  140,  209,  324,   96,    7,  106,  324,  324,
 /*  1640 */   324,  324,  146,  324,  324,  324,  250,  286,  238,  230,
 /*  1650 */   324,   13,  324,  324,   51,  324,  324,  324,  360,  324,
 /*  1660 */   324,  324,  142,  324,  324,   85,  135,  324,  324,   50,
 /*  1670 */    52,  301,  237,  363,  299,  361,  105,    1,  225,  368,
 /*  1680 */   362,  324,  343,  324,  324,  324,   38,  324,  126,  213,
 /*  1690 */   324,   96,    7,  106,  324,  324,  324,  324,  146,  324,
 /*  1700 */   324,  324,  250,  286,  238,  230,  324,   21,  324,  324,
 /*  1710 */    51,  324,  324,  324,  360,  324,  324,  324,  142,  324,
 /*  1720 */   324,   71,  135,  324,  324,   50,   52,  301,  237,  363,
 /*  1730 */   299,  361,  105,  324,  225,  368,  362,  324,  343,  324,
 /*  1740 */   324,  324,   38,  324,  126,  214,  324,   96,    7,  106,
 /*  1750 */   324,  324,  324,  324,  146,  324,  324,  324,  250,  286,
 /*  1760 */   238,  230,  324,   21,  102,  186,   51,  324,  324,  324,
 /*  1770 */   324,  324,  324,  324,  289,  324,  324,   22,   27,  324,
 /*  1780 */   499,   50,   52,  301,  237,  363,  324,  499,  105,  499,
 /*  1790 */   499,  203,  499,  499,  324,  324,  324,  324,  324,  499,
 /*  1800 */     4,  499,  324,   96,  324,  324,  324,  324,  324,  324,
 /*  1810 */   324,  324,  324,  360,  324,  324,  499,  117,  324,  324,
 /*  1820 */    74,  135,  324,  144,  324,  324,  324,  499,  324,  299,
 /*  1830 */   361,  324,  324,  225,  368,  362,  324,  343,  360,  324,
 /*  1840 */   324,  499,  142,  324,  324,   66,  135,  324,  263,  324,
 /*  1850 */   324,  324,  324,  324,  299,  361,  324,  324,  225,  368,
 /*  1860 */   362,  324,  343,  324,  360,  324,  324,  324,  142,  324,
 /*  1870 */   324,   79,  135,  324,  360,  324,  324,  324,  149,  324,
 /*  1880 */   299,  361,  135,  360,  225,  368,  362,  142,  343,  324,
 /*  1890 */    81,  135,  324,  324,  225,  368,  362,  324,  343,  299,
 /*  1900 */   361,  324,  324,  225,  368,  362,  324,  343,  324,  324,
 /*  1910 */   324,  360,  324,  324,  324,  115,  324,  324,   83,  135,
 /*  1920 */   324,  324,  360,  324,  324,  324,  142,  299,  361,   72,
 /*  1930 */   135,  225,  368,  362,  324,  343,  324,  324,  299,  361,
 /*  1940 */   324,  324,  225,  368,  362,  324,  343,  324,  360,  324,
 /*  1950 */   324,  324,  142,  324,  324,   70,  135,  324,  360,  324,
 /*  1960 */   324,  324,  153,  324,  299,  361,  135,  360,  225,  368,
 /*  1970 */   362,  142,  343,  324,   68,  135,  324,  324,  225,  368,
 /*  1980 */   362,  324,  343,  299,  361,  324,  324,  225,  368,  362,
 /*  1990 */   324,  343,  324,  324,  324,  360,  324,  324,  324,  142,
 /*  2000 */   324,  324,   90,  135,  324,  324,  360,  324,  324,  324,
 /*  2010 */   142,  299,  361,   86,  135,  225,  368,  362,  324,  343,
 /*  2020 */   324,  324,  299,  361,  324,  324,  225,  368,  362,  324,
 /*  2030 */   343,  324,  360,  194,  183,  324,  142,  324,  324,   91,
 /*  2040 */   135,  324,  324,  289,  324,  324,   22,   27,  299,  361,
 /*  2050 */   324,  360,  225,  368,  362,  142,  343,  324,   61,  135,
 /*  2060 */   203,  324,  324,  324,  194,  171,  324,  299,  361,  324,
 /*  2070 */   324,  225,  368,  362,  289,  343,  324,   22,   27,  360,
 /*  2080 */   324,  324,  324,  142,  324,  324,   88,  135,  324,  324,
 /*  2090 */   360,  203,  324,  324,  142,  299,  361,   69,  135,  225,
 /*  2100 */   368,  362,  324,  343,  324,  324,  299,  361,  324,  324,
 /*  2110 */   225,  368,  362,  324,  343,  324,  360,  194,  179,  324,
 /*  2120 */   142,  324,  324,   76,  135,  324,  324,  289,  324,  324,
 /*  2130 */    22,   27,  299,  361,  324,  360,  225,  368,  362,  142,
 /*  2140 */   343,  324,   65,  135,  203,  324,  324,  324,  194,  187,
 /*  2150 */   324,  299,  361,  324,  324,  225,  368,  362,  289,  343,
 /*  2160 */   324,   22,   27,  360,  324,  324,  324,  111,  324,  324,
 /*  2170 */    64,  135,  324,  324,  360,  203,  324,  324,  142,  299,
 /*  2180 */   361,   62,  135,  225,  368,  362,  324,  343,  324,  324,
 /*  2190 */   299,  361,  324,  324,  225,  368,  362,  324,  343,  324,
 /*  2200 */   360,  194,  173,  324,  142,  324,  324,   82,  135,  324,
 /*  2210 */   324,  289,  324,  324,   22,   27,  299,  361,  324,  360,
 /*  2220 */   225,  368,  362,  142,  343,  324,   60,  135,  203,  324,
 /*  2230 */   324,  324,  324,  324,  324,  299,  361,  324,  324,  225,
 /*  2240 */   368,  362,  324,  343,  324,  324,  324,  360,  324,  324,
 /*  2250 */   324,   93,  324,  324,   57,  120,  324,  324,  360,  324,
 /*  2260 */   324,  324,  142,  299,  361,   58,  135,  225,  368,  362,
 /*  2270 */   324,  343,  324,  324,  299,  361,  324,  324,  225,  368,
 /*  2280 */   362,  324,  343,  324,  360,  324,  324,  324,  142,  324,
 /*  2290 */   324,   59,  135,  324,  324,  324,  324,  324,  324,  324,
 /*  2300 */   299,  361,  324,  360,  225,  368,  362,   93,  343,  324,
 /*  2310 */    55,  120,  324,  324,  324,  324,  324,  324,  324,  299,
 /*  2320 */   361,  324,  324,  215,  368,  362,  324,  343,  324,  324,
 /*  2330 */   324,  360,  324,  324,  324,  142,  324,  324,   56,  135,
 /*  2340 */   324,  324,  360,  324,  324,  324,  142,  299,  361,   78,
 /*  2350 */   135,  225,  368,  362,  324,  343,  324,  324,  299,  361,
 /*  2360 */   324,  324,  225,  368,  362,  324,  343,  324,  360,  324,
 /*  2370 */   324,  324,  142,  324,  324,   67,  135,  324,  324,  324,
 /*  2380 */   324,  324,  324,  324,  299,  361,  324,  324,  217,  368,
 /*  2390 */   362,  324,  343,
    );
    static public $yy_lookahead = array(
 /*     0 */     1,   82,   83,   84,    3,    4,    5,    6,    7,    8,
 /*    10 */     9,   10,   11,   12,   18,   89,   15,   80,   81,   82,
 /*    20 */    83,   84,   21,   22,   98,   26,   15,   28,   27,   18,
 /*    30 */    19,  116,   31,   32,   33,   24,  110,   38,   39,   40,
 /*    40 */    41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
 /*    50 */    51,    4,    5,    6,    7,    8,   60,    1,   36,   12,
 /*    60 */    13,   14,    1,   64,   65,   66,   67,   68,   69,   70,
 /*    70 */    71,   72,   73,   74,   75,    1,   83,   16,   88,   57,
 /*    80 */    87,   59,   88,   90,   91,   63,   30,   16,   15,   28,
 /*    90 */    16,   18,   99,  100,   19,   20,  103,  104,  105,   28,
 /*   100 */   107,   28,   28,   30,    2,  115,  116,   36,   52,  115,
 /*   110 */   117,  118,   38,   39,   40,   41,   42,   43,   44,   45,
 /*   120 */    46,   47,   48,   49,   50,   51,   83,   88,   89,  109,
 /*   130 */    59,  111,  112,   15,   59,   17,   18,   98,   64,   65,
 /*   140 */    66,   67,   68,   69,   70,   71,   72,   73,   74,   75,
 /*   150 */     1,   83,   34,   15,  115,   87,   18,   19,   90,   91,
 /*   160 */    92,   15,  119,  120,   18,   16,   16,   99,  100,   19,
 /*   170 */    89,  103,  104,  105,   28,  107,   30,   28,   28,   98,
 /*   180 */    36,   15,   15,   17,   18,   18,   36,   38,   39,   40,
 /*   190 */    41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
 /*   200 */    51,   88,   89,   59,   15,   57,   30,   18,   19,   59,
 /*   210 */    62,   98,   36,   64,   65,   66,   67,   68,   69,   70,
 /*   220 */    71,   72,   73,   74,   75,    1,   83,   60,  115,   16,
 /*   230 */    87,   97,   15,   90,   91,   59,    1,   24,   19,   20,
 /*   240 */    16,   15,   99,  100,   18,   20,  103,  104,  105,   60,
 /*   250 */   107,   16,   28,   36,   84,   20,   86,  114,  111,  112,
 /*   260 */    17,   18,   38,   39,   40,   41,   42,   43,   44,   45,
 /*   270 */    46,   47,   48,   49,   50,   51,    1,    2,   59,   91,
 /*   280 */    92,   93,   57,  111,  112,   24,   60,   62,   64,   65,
 /*   290 */    66,   67,   68,   69,   70,   71,   72,   73,   74,   75,
 /*   300 */    89,    1,   88,   89,   61,   35,   35,   37,   37,   98,
 /*   310 */    17,   18,   98,   38,   39,   40,   41,   42,   43,   44,
 /*   320 */    45,   46,   47,   48,   49,   50,   51,    1,   89,  115,
 /*   330 */    35,   35,   37,   88,   88,   91,   92,   98,   77,   64,
 /*   340 */    65,   66,   67,   68,   69,   70,   71,   72,   73,   74,
 /*   350 */    75,   23,   52,   89,  115,   29,  108,   97,  110,   63,
 /*   360 */   115,  115,   98,   35,   38,   39,   40,   41,   42,   43,
 /*   370 */    44,   45,   46,   47,   48,   49,   50,   51,    1,  115,
 /*   380 */    89,   15,   15,   15,   18,   18,   18,   95,   17,   98,
 /*   390 */    64,   65,   66,   67,   68,   69,   70,   71,   72,   73,
 /*   400 */    74,   75,  110,   89,   91,   92,  115,   36,    1,  108,
 /*   410 */    15,  110,   98,   18,  108,   38,   39,   40,   41,   42,
 /*   420 */    43,   44,   45,   46,   47,   48,   49,   50,   51,  115,
 /*   430 */   106,  106,   36,   15,   97,   28,   18,  113,  113,  108,
 /*   440 */    95,   64,   65,   66,   67,   68,   69,   70,   71,   72,
 /*   450 */    73,   74,   75,    1,   77,  110,   83,  108,   15,   18,
 /*   460 */    87,   18,   83,   90,   91,   20,   87,   17,   19,   91,
 /*   470 */    91,   28,   99,  100,    1,   23,  103,  104,  105,  100,
 /*   480 */   107,  103,  103,  104,  105,  107,  107,  114,    2,   16,
 /*   490 */    38,   39,   40,   41,   42,   43,   44,   45,   46,   47,
 /*   500 */    48,   49,   50,   51,   59,   19,   57,   19,   37,   61,
 /*   510 */    18,   17,   53,    2,   18,   95,   64,   65,   66,   67,
 /*   520 */    68,   69,   70,   71,   72,   73,   74,   75,    1,   83,
 /*   530 */   110,   89,   63,   87,   83,   25,   90,   91,   87,   17,
 /*   540 */    98,   18,   91,   16,    1,   99,  100,    1,    2,  103,
 /*   550 */   104,  105,  110,  107,  103,  104,  105,   18,  107,   16,
 /*   560 */   114,   61,   16,   34,    1,   38,   39,   40,   41,   42,
 /*   570 */    43,   44,   45,   46,   47,   48,   49,   50,   51,   16,
 /*   580 */    89,    2,   18,   17,   24,   57,   34,   36,   17,   98,
 /*   590 */    95,   64,   65,   66,   67,   68,   69,   70,   71,   72,
 /*   600 */    73,   74,   75,    1,   83,  110,   89,   18,   87,   18,
 /*   610 */    16,   90,   91,   92,   89,   98,   96,   16,   16,   16,
 /*   620 */    99,  100,   28,   98,  103,  104,  105,  110,  107,   28,
 /*   630 */   110,   28,   18,   18,   98,  110,   20,    1,   28,  109,
 /*   640 */    38,   39,   40,   41,   42,   43,   44,   45,   46,   47,
 /*   650 */    48,   49,   50,   51,    1,  115,  108,   28,  113,  110,
 /*   660 */    28,   94,  112,   95,   95,   95,   64,   65,   66,   67,
 /*   670 */    68,   69,   70,   71,   72,   73,   74,   75,  110,  110,
 /*   680 */   110,   85,   94,   13,  121,  121,  121,  121,  121,  121,
 /*   690 */    37,   38,   39,   40,   41,   42,   43,   44,   45,   46,
 /*   700 */    47,   48,   49,   50,   51,    1,  121,  121,  121,  121,
 /*   710 */   121,  121,  121,  121,   95,   95,   95,   64,   65,   66,
 /*   720 */    67,   68,   69,   70,   71,   72,   73,   74,   75,  110,
 /*   730 */   110,  110,  121,  121,  121,  121,  121,  121,  121,  121,
 /*   740 */   121,   37,   38,   39,   40,   41,   42,   43,   44,   45,
 /*   750 */    46,   47,   48,   49,   50,   51,    1,  121,  121,  121,
 /*   760 */   121,  121,  121,  121,  121,  121,  121,  121,   64,   65,
 /*   770 */    66,   67,   68,   69,   70,   71,   72,   73,   74,   75,
 /*   780 */    83,  121,  121,   28,  121,  121,  121,  121,  121,  121,
 /*   790 */   121,  121,  121,   38,   39,   40,   41,   42,   43,   44,
 /*   800 */    45,   46,   47,   48,   49,   50,   51,    1,  121,  121,
 /*   810 */   121,  121,  121,  121,  121,  121,  121,  120,  121,   64,
 /*   820 */    65,   66,   67,   68,   69,   70,   71,   72,   73,   74,
 /*   830 */    75,  121,  121,  121,  121,  121,  121,  121,  121,  121,
 /*   840 */   121,  121,  121,  121,   38,   39,   40,   41,   42,   43,
 /*   850 */    44,   45,   46,   47,   48,   49,   50,   51,  121,  121,
 /*   860 */   121,  121,  121,  121,  121,  121,  121,  121,  121,   63,
 /*   870 */    64,   65,   66,   67,   68,   69,   70,   71,   72,   73,
 /*   880 */    74,   75,    1,  121,  121,  121,   88,   83,    1,   16,
 /*   890 */    16,   87,  121,   10,   16,   91,  121,   16,   15,  101,
 /*   900 */   102,   28,   28,   16,   21,   22,   28,  103,  104,  105,
 /*   910 */    27,  107,  121,  115,   31,   32,   33,  121,    1,   38,
 /*   920 */    39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
 /*   930 */    49,   50,   51,   16,  121,  121,  121,  121,  121,   52,
 /*   940 */   121,  121,  121,  121,  121,   64,   65,   66,   67,   68,
 /*   950 */    69,   70,   71,   72,   73,   74,   75,    1,  121,   76,
 /*   960 */    77,   78,   83,   16,   16,   16,   87,  121,   10,   16,
 /*   970 */    91,  121,   16,   15,  121,   28,   28,   28,  121,   21,
 /*   980 */    22,   28,  103,  104,  105,   27,  107,  121,  121,   31,
 /*   990 */    32,   33,  121,  121,   38,   39,   40,   41,   42,   43,
 /*  1000 */    44,   45,   46,   47,   48,   49,   50,   51,    1,  121,
 /*  1010 */   121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
 /*  1020 */    64,   65,   66,   67,   68,   69,   70,   71,   72,   73,
 /*  1030 */    74,   75,  121,  121,   76,   77,   78,  121,  121,  121,
 /*  1040 */   121,  121,  121,  121,  121,   38,   39,   40,   41,   42,
 /*  1050 */    43,   44,   45,   46,   47,   48,   49,   50,   51,  121,
 /*  1060 */   121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
 /*  1070 */   121,   64,   65,   66,   67,   68,   69,   70,   71,   72,
 /*  1080 */    73,   74,   75,  121,   38,   39,   40,   41,   42,   43,
 /*  1090 */    44,   45,   46,   47,   48,   49,   50,   51,  121,  121,
 /*  1100 */   121,  121,  121,  121,  121,  121,  121,  121,  121,  121,
 /*  1110 */    64,   65,   66,   67,   68,   69,   70,   71,   72,   73,
 /*  1120 */    74,   75,  121,  121,  121,  121,   15,  121,   17,   18,
 /*  1130 */   121,   83,   21,   22,   16,   87,  121,   16,   27,   91,
 /*  1140 */   121,  121,   31,   32,   33,   34,   28,   36,  100,   28,
 /*  1150 */    39,  103,  104,  105,  121,  107,   83,  121,  121,  121,
 /*  1160 */    87,  121,  121,  121,   91,   54,   55,   56,   57,   58,
 /*  1170 */   121,   83,   61,   62,   63,   87,  103,  104,  105,   91,
 /*  1180 */   107,  121,   15,  121,   17,   18,  121,   76,   21,   22,
 /*  1190 */    16,  103,  104,  105,   27,  107,   16,   16,   31,   32,
 /*  1200 */    33,   34,   28,   36,   16,  121,   39,   16,   28,   28,
 /*  1210 */    83,  121,  121,  121,   87,  121,   28,   90,   91,   28,
 /*  1220 */     1,   54,   55,   56,   57,   58,   99,  100,   61,   62,
 /*  1230 */   103,  104,  105,    1,  107,   16,  121,   16,   15,  121,
 /*  1240 */    17,   18,  121,   76,   21,   22,   16,   28,   16,   28,
 /*  1250 */    27,   16,   20,  121,   31,   32,   33,   34,   28,   36,
 /*  1260 */    28,  121,   39,   28,   16,  121,   83,  121,   36,  121,
 /*  1270 */    87,   52,  121,   90,   91,  121,   28,   54,   55,   56,
 /*  1280 */    57,   58,   99,  100,   61,   62,  103,  104,  105,  121,
 /*  1290 */   107,   59,  121,  121,   15,  121,   17,   18,  121,   76,
 /*  1300 */    21,   22,  121,  121,  121,  121,   27,  121,  121,  121,
 /*  1310 */    31,   32,   33,   34,  121,   36,  121,  121,   39,  121,
 /*  1320 */   121,  121,   83,  121,  121,  121,   87,  121,  121,   90,
 /*  1330 */    91,  121,    1,   54,   55,   56,   57,   58,   99,  100,
 /*  1340 */    61,   62,  103,  104,  105,  121,  107,   16,  121,  121,
 /*  1350 */    15,  121,   17,   18,  121,   76,   21,   22,  121,   28,
 /*  1360 */    29,  121,   27,  121,  121,  121,   31,   32,   33,   34,
 /*  1370 */   121,   36,  121,  121,   39,  121,  121,  121,   83,  121,
 /*  1380 */   121,  121,   87,   52,  121,   90,   91,  121,    1,   54,
 /*  1390 */    55,   56,   57,   58,   99,  100,   61,   62,  103,  104,
 /*  1400 */   105,  121,  107,   16,  121,  121,   15,  121,   17,   18,
 /*  1410 */   121,   76,   21,   22,  121,   28,  121,  121,   27,  121,
 /*  1420 */   121,  121,   31,   32,   33,   34,  121,   36,  121,  121,
 /*  1430 */    39,  121,  121,  121,   83,  121,  121,  121,   87,   52,
 /*  1440 */   121,   90,   91,  121,    1,   54,   55,   56,   57,   58,
 /*  1450 */    99,  100,   61,   62,  103,  104,  105,  121,  107,   16,
 /*  1460 */   121,  121,   15,  121,   17,   18,  121,   76,   21,   22,
 /*  1470 */   121,   28,  121,  121,   27,  121,  121,  121,   31,   32,
 /*  1480 */    33,   34,  121,   36,  121,  121,   39,  121,  121,  121,
 /*  1490 */    83,  121,  121,  121,   87,   52,  121,   90,   91,  121,
 /*  1500 */   121,   54,   55,   56,   57,   58,   99,  100,   61,   62,
 /*  1510 */   103,  104,  105,  121,  107,  121,  121,  121,   15,  121,
 /*  1520 */    17,   18,  121,   76,   21,   22,  121,  121,  121,  121,
 /*  1530 */    27,  121,  121,  121,   31,   32,   33,   34,  121,   36,
 /*  1540 */   121,  121,   39,  121,  121,  121,   83,  121,  121,  121,
 /*  1550 */    87,  121,  121,   90,   91,  121,  121,   54,   55,   56,
 /*  1560 */    57,   58,   99,  100,   61,   62,  103,  104,  105,  121,
 /*  1570 */   107,  121,  121,  121,   15,  121,   17,   18,  121,   76,
 /*  1580 */    21,   22,  121,  121,  121,  121,   27,  121,  121,  121,
 /*  1590 */    31,   32,   33,   34,  121,   36,  121,  121,   39,  121,
 /*  1600 */   121,  121,   83,  121,  121,  121,   87,  121,  121,   90,
 /*  1610 */    91,  121,  121,   54,   55,   56,   57,   58,   99,  100,
 /*  1620 */    61,   62,  103,  104,  105,  121,  107,  121,  121,  121,
 /*  1630 */    15,  121,   17,   18,  121,   76,   21,   22,  121,  121,
 /*  1640 */   121,  121,   27,  121,  121,  121,   31,   32,   33,   34,
 /*  1650 */   121,   36,  121,  121,   39,  121,  121,  121,   83,  121,
 /*  1660 */   121,  121,   87,  121,  121,   90,   91,  121,  121,   54,
 /*  1670 */    55,   56,   57,   58,   99,  100,   61,   62,  103,  104,
 /*  1680 */   105,  121,  107,  121,  121,  121,   15,  121,   17,   18,
 /*  1690 */   121,   76,   21,   22,  121,  121,  121,  121,   27,  121,
 /*  1700 */   121,  121,   31,   32,   33,   34,  121,   36,  121,  121,
 /*  1710 */    39,  121,  121,  121,   83,  121,  121,  121,   87,  121,
 /*  1720 */   121,   90,   91,  121,  121,   54,   55,   56,   57,   58,
 /*  1730 */    99,  100,   61,  121,  103,  104,  105,  121,  107,  121,
 /*  1740 */   121,  121,   15,  121,   17,   18,  121,   76,   21,   22,
 /*  1750 */   121,  121,  121,  121,   27,  121,  121,  121,   31,   32,
 /*  1760 */    33,   34,  121,   36,   88,   89,   39,  121,  121,  121,
 /*  1770 */   121,  121,  121,  121,   98,  121,  121,  101,  102,  121,
 /*  1780 */    16,   54,   55,   56,   57,   58,  121,   23,   61,   25,
 /*  1790 */    26,  115,   28,   29,  121,  121,  121,  121,  121,   35,
 /*  1800 */    36,   37,  121,   76,  121,  121,  121,  121,  121,  121,
 /*  1810 */   121,  121,  121,   83,  121,  121,   52,   87,  121,  121,
 /*  1820 */    90,   91,  121,   59,  121,  121,  121,   63,  121,   99,
 /*  1830 */   100,  121,  121,  103,  104,  105,  121,  107,   83,  121,
 /*  1840 */   121,   77,   87,  121,  121,   90,   91,  121,  118,  121,
 /*  1850 */   121,  121,  121,  121,   99,  100,  121,  121,  103,  104,
 /*  1860 */   105,  121,  107,  121,   83,  121,  121,  121,   87,  121,
 /*  1870 */   121,   90,   91,  121,   83,  121,  121,  121,   87,  121,
 /*  1880 */    99,  100,   91,   83,  103,  104,  105,   87,  107,  121,
 /*  1890 */    90,   91,  121,  121,  103,  104,  105,  121,  107,   99,
 /*  1900 */   100,  121,  121,  103,  104,  105,  121,  107,  121,  121,
 /*  1910 */   121,   83,  121,  121,  121,   87,  121,  121,   90,   91,
 /*  1920 */   121,  121,   83,  121,  121,  121,   87,   99,  100,   90,
 /*  1930 */    91,  103,  104,  105,  121,  107,  121,  121,   99,  100,
 /*  1940 */   121,  121,  103,  104,  105,  121,  107,  121,   83,  121,
 /*  1950 */   121,  121,   87,  121,  121,   90,   91,  121,   83,  121,
 /*  1960 */   121,  121,   87,  121,   99,  100,   91,   83,  103,  104,
 /*  1970 */   105,   87,  107,  121,   90,   91,  121,  121,  103,  104,
 /*  1980 */   105,  121,  107,   99,  100,  121,  121,  103,  104,  105,
 /*  1990 */   121,  107,  121,  121,  121,   83,  121,  121,  121,   87,
 /*  2000 */   121,  121,   90,   91,  121,  121,   83,  121,  121,  121,
 /*  2010 */    87,   99,  100,   90,   91,  103,  104,  105,  121,  107,
 /*  2020 */   121,  121,   99,  100,  121,  121,  103,  104,  105,  121,
 /*  2030 */   107,  121,   83,   88,   89,  121,   87,  121,  121,   90,
 /*  2040 */    91,  121,  121,   98,  121,  121,  101,  102,   99,  100,
 /*  2050 */   121,   83,  103,  104,  105,   87,  107,  121,   90,   91,
 /*  2060 */   115,  121,  121,  121,   88,   89,  121,   99,  100,  121,
 /*  2070 */   121,  103,  104,  105,   98,  107,  121,  101,  102,   83,
 /*  2080 */   121,  121,  121,   87,  121,  121,   90,   91,  121,  121,
 /*  2090 */    83,  115,  121,  121,   87,   99,  100,   90,   91,  103,
 /*  2100 */   104,  105,  121,  107,  121,  121,   99,  100,  121,  121,
 /*  2110 */   103,  104,  105,  121,  107,  121,   83,   88,   89,  121,
 /*  2120 */    87,  121,  121,   90,   91,  121,  121,   98,  121,  121,
 /*  2130 */   101,  102,   99,  100,  121,   83,  103,  104,  105,   87,
 /*  2140 */   107,  121,   90,   91,  115,  121,  121,  121,   88,   89,
 /*  2150 */   121,   99,  100,  121,  121,  103,  104,  105,   98,  107,
 /*  2160 */   121,  101,  102,   83,  121,  121,  121,   87,  121,  121,
 /*  2170 */    90,   91,  121,  121,   83,  115,  121,  121,   87,   99,
 /*  2180 */   100,   90,   91,  103,  104,  105,  121,  107,  121,  121,
 /*  2190 */    99,  100,  121,  121,  103,  104,  105,  121,  107,  121,
 /*  2200 */    83,   88,   89,  121,   87,  121,  121,   90,   91,  121,
 /*  2210 */   121,   98,  121,  121,  101,  102,   99,  100,  121,   83,
 /*  2220 */   103,  104,  105,   87,  107,  121,   90,   91,  115,  121,
 /*  2230 */   121,  121,  121,  121,  121,   99,  100,  121,  121,  103,
 /*  2240 */   104,  105,  121,  107,  121,  121,  121,   83,  121,  121,
 /*  2250 */   121,   87,  121,  121,   90,   91,  121,  121,   83,  121,
 /*  2260 */   121,  121,   87,   99,  100,   90,   91,  103,  104,  105,
 /*  2270 */   121,  107,  121,  121,   99,  100,  121,  121,  103,  104,
 /*  2280 */   105,  121,  107,  121,   83,  121,  121,  121,   87,  121,
 /*  2290 */   121,   90,   91,  121,  121,  121,  121,  121,  121,  121,
 /*  2300 */    99,  100,  121,   83,  103,  104,  105,   87,  107,  121,
 /*  2310 */    90,   91,  121,  121,  121,  121,  121,  121,  121,   99,
 /*  2320 */   100,  121,  121,  103,  104,  105,  121,  107,  121,  121,
 /*  2330 */   121,   83,  121,  121,  121,   87,  121,  121,   90,   91,
 /*  2340 */   121,  121,   83,  121,  121,  121,   87,   99,  100,   90,
 /*  2350 */    91,  103,  104,  105,  121,  107,  121,  121,   99,  100,
 /*  2360 */   121,  121,  103,  104,  105,  121,  107,  121,   83,  121,
 /*  2370 */   121,  121,   87,  121,  121,   90,   91,  121,  121,  121,
 /*  2380 */   121,  121,  121,  121,   99,  100,  121,  121,  103,  104,
 /*  2390 */   105,  121,  107,
);
    const YY_SHIFT_USE_DFLT = -5;
    const YY_SHIFT_MAX = 252;
    static public $yy_shift_ofst = array(
 /*     0 */     1, 1391, 1391, 1223, 1167, 1167, 1167, 1223, 1111, 1167,
 /*    10 */  1167, 1167, 1503, 1167, 1559, 1167, 1167, 1167, 1167, 1167,
 /*    20 */  1167, 1167, 1167, 1167, 1167, 1615, 1167, 1167, 1167, 1167,
 /*    30 */  1503, 1167, 1167, 1447, 1167, 1167, 1167, 1167, 1279, 1167,
 /*    40 */  1167, 1167, 1279, 1167, 1335, 1335, 1727, 1671, 1727, 1727,
 /*    50 */  1727, 1727, 1727,  224,   74,  149,   -1,  755,  755,  755,
 /*    60 */   956,  881,  806,  527,  326,  704,  275,  377,  653,  602,
 /*    70 */   452, 1007, 1007, 1007, 1007, 1007, 1007, 1007, 1007, 1007,
 /*    80 */  1007, 1007, 1007, 1007, 1007, 1007, 1007, 1007, 1007, 1007,
 /*    90 */  1046, 1046, 1232, 1443,  407,    1,  958,   73,  146,  225,
 /*   100 */   546,   61,   61,  443,  443,  243,  371,  407,  407,  883,
 /*   110 */    47, 1331,   11,  189, 1387, 1219,  226,   56,  138,  235,
 /*   120 */    75,  887,  219,  366,  371,  367,  366,  366,  368,  293,
 /*   130 */   371,  366,  917,  366,  366,  445,  366,  418,  366, 1248,
 /*   140 */   368,  366,  300,  395,  293,  636,  629,  636,  616,  636,
 /*   150 */   610,  636,  636,  636,  636,  616,  636,   -5,  166,  167,
 /*   160 */   601,  603,  873,  594,  148,  217,  148,  473,  947,  148,
 /*   170 */   874,  878,  948, 1235,  148,  543, 1191, 1221,  148, 1230,
 /*   180 */   563, 1188, 1121, 1118,  953,  949, 1181, 1174, 1180,  670,
 /*   190 */   632,  632,  616,  616,  636,  616,  636,  102,  102,  396,
 /*   200 */    -5,   -5,   -5,   -5,   -5, 1764,  150,   22,  118,   71,
 /*   210 */   176,   -4,  486,  144,  144,  213,  270,  261,  296,  328,
 /*   220 */   449,  271,  295,  615,  579,  560,  566,  441,  564,  396,
 /*   230 */   528,  591,  551,  589,  571,  614,  552,  529,  539,  494,
 /*   240 */   448,  492,  471,  450,  488,  469,  459,  511,  522,  510,
 /*   250 */   496,  523,  500,
);
    const YY_REDUCE_USE_DFLT = -86;
    const YY_REDUCE_MAX = 204;
    static public $yy_reduce_ofst = array(
 /*     0 */   -63,   -7, 1730,   68,  446,  373,  143,  521, 2091, 2117,
 /*    10 */  1800, 1407, 2080, 1884, 1912, 1575, 1949, 1923, 1865, 1968,
 /*    20 */  1996, 2052, 2033, 2007, 1839, 1828, 1351, 1295, 1183, 1239,
 /*    30 */  1463, 1519, 1781, 1755, 1631, 2175, 2248, 2201, 2164, 2285,
 /*    40 */  2259, 2136, 2220, 1127,  379, 1048,  451, 1073,  804,  879,
 /*    50 */  1875, 1791, 1088, 1976, 1945, 1676, 2060, 1676, 2113, 2029,
 /*    60 */   798,  798,  798,  798,  798,  798,  798,  798,  798,  798,
 /*    70 */   798,  798,  798,  798,  798,  798,  798,  798,  798,  798,
 /*    80 */   798,  798,  798,  798,  798,  798,  798,  798,  798,  798,
 /*    90 */   798,  798,   39,  113,  214,  -81,   43,  442,  -74,   20,
 /*   100 */   -10,  239,  264,  525,  517,  378,  188,  314,  291,  697,
 /*   110 */   170,   -6,  520,  248,   -6,   -6,  248,   -6,  248,  246,
 /*   120 */   172,   -6,  172,  568,  313,  495,  495,  569,  570,  324,
 /*   130 */   244,  292,  245,  420,  345,  172,  301,  495,  621,  491,
 /*   140 */   495,  619,   -6,  620,  325,   -6,  211,   -6,  147,   -6,
 /*   150 */    81,   -6,   -6,   -6,   -6,  172,   -6,   -6,  545,  549,
 /*   160 */   536,  536,  536,  536,  530,  548,  530,  540,  536,  530,
 /*   170 */   536,  536,  536,  536,  530,  540,  536,  536,  530,  536,
 /*   180 */   540,  536,  536,  536,  536,  536,  536,  536,  536,  596,
 /*   190 */   567,  588,  550,  550,  540,  550,  540,  -85,  -85,  331,
 /*   200 */   306,  349,  337,  260,  134,
);
    static public $yyExpectedTokens = array(
        /* 0 */ array(3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 21, 22, 27, 31, 32, 33, ),
        /* 1 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 2 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 3 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 4 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 5 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 6 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 7 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 8 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 63, 76, ),
        /* 9 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 10 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 11 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 12 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 13 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 14 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 15 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 16 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 17 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 18 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 19 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 20 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 21 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 22 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 23 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 24 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 25 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 26 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 27 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 28 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 29 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 30 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 31 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 32 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 33 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 34 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 35 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 36 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 37 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 38 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 39 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 40 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 41 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 42 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 43 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 44 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 45 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 62, 76, ),
        /* 46 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 47 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 48 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 49 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 50 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 51 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 52 */ array(15, 17, 18, 21, 22, 27, 31, 32, 33, 34, 36, 39, 54, 55, 56, 57, 58, 61, 76, ),
        /* 53 */ array(1, 16, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 54 */ array(1, 16, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 55 */ array(1, 16, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 56 */ array(1, 26, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 57 */ array(1, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 58 */ array(1, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 59 */ array(1, 28, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 60 */ array(1, 16, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 61 */ array(1, 16, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 62 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 63 */ array(1, 16, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 64 */ array(1, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 65 */ array(1, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 66 */ array(1, 2, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 67 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, ),
        /* 68 */ array(1, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 69 */ array(1, 16, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 70 */ array(1, 23, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 71 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 72 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 73 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 74 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 75 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 76 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 77 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 78 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 79 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 80 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 81 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 82 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 83 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 84 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 85 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 86 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 87 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 88 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 89 */ array(1, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 90 */ array(38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 91 */ array(38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, ),
        /* 92 */ array(1, 16, 20, 28, 36, 59, ),
        /* 93 */ array(1, 16, 28, 52, ),
        /* 94 */ array(1, 28, ),
        /* 95 */ array(3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 21, 22, 27, 31, 32, 33, ),
        /* 96 */ array(10, 15, 21, 22, 27, 31, 32, 33, 76, 77, 78, ),
        /* 97 */ array(15, 18, 28, 30, ),
        /* 98 */ array(15, 18, 28, 30, ),
        /* 99 */ array(20, 57, 62, ),
        /* 100 */ array(1, 2, 16, ),
        /* 101 */ array(1, 16, 28, ),
        /* 102 */ array(1, 16, 28, ),
        /* 103 */ array(15, 18, 28, ),
        /* 104 */ array(15, 18, 28, ),
        /* 105 */ array(17, 18, 61, ),
        /* 106 */ array(17, 36, ),
        /* 107 */ array(1, 28, ),
        /* 108 */ array(1, 28, ),
        /* 109 */ array(10, 15, 21, 22, 27, 31, 32, 33, 76, 77, 78, ),
        /* 110 */ array(4, 5, 6, 7, 8, 12, 13, 14, ),
        /* 111 */ array(1, 16, 28, 29, 52, ),
        /* 112 */ array(15, 18, 19, 24, ),
        /* 113 */ array(15, 18, 19, 60, ),
        /* 114 */ array(1, 16, 28, 52, ),
        /* 115 */ array(1, 16, 28, 52, ),
        /* 116 */ array(15, 18, 60, ),
        /* 117 */ array(1, 30, 52, ),
        /* 118 */ array(15, 18, 19, ),
        /* 119 */ array(1, 16, 20, ),
        /* 120 */ array(19, 20, 59, ),
        /* 121 */ array(1, 16, 52, ),
        /* 122 */ array(19, 20, 59, ),
        /* 123 */ array(15, 18, ),
        /* 124 */ array(17, 36, ),
        /* 125 */ array(15, 18, ),
        /* 126 */ array(15, 18, ),
        /* 127 */ array(15, 18, ),
        /* 128 */ array(15, 18, ),
        /* 129 */ array(17, 18, ),
        /* 130 */ array(17, 36, ),
        /* 131 */ array(15, 18, ),
        /* 132 */ array(1, 16, ),
        /* 133 */ array(15, 18, ),
        /* 134 */ array(15, 18, ),
        /* 135 */ array(20, 59, ),
        /* 136 */ array(15, 18, ),
        /* 137 */ array(15, 18, ),
        /* 138 */ array(15, 18, ),
        /* 139 */ array(16, 28, ),
        /* 140 */ array(15, 18, ),
        /* 141 */ array(15, 18, ),
        /* 142 */ array(1, 52, ),
        /* 143 */ array(15, 18, ),
        /* 144 */ array(17, 18, ),
        /* 145 */ array(1, ),
        /* 146 */ array(28, ),
        /* 147 */ array(1, ),
        /* 148 */ array(20, ),
        /* 149 */ array(1, ),
        /* 150 */ array(28, ),
        /* 151 */ array(1, ),
        /* 152 */ array(1, ),
        /* 153 */ array(1, ),
        /* 154 */ array(1, ),
        /* 155 */ array(20, ),
        /* 156 */ array(1, ),
        /* 157 */ array(),
        /* 158 */ array(15, 17, 18, ),
        /* 159 */ array(15, 18, 60, ),
        /* 160 */ array(16, 28, ),
        /* 161 */ array(16, 28, ),
        /* 162 */ array(16, 28, ),
        /* 163 */ array(16, 28, ),
        /* 164 */ array(57, 62, ),
        /* 165 */ array(15, 36, ),
        /* 166 */ array(57, 62, ),
        /* 167 */ array(1, 16, ),
        /* 168 */ array(16, 28, ),
        /* 169 */ array(57, 62, ),
        /* 170 */ array(16, 28, ),
        /* 171 */ array(16, 28, ),
        /* 172 */ array(16, 28, ),
        /* 173 */ array(16, 28, ),
        /* 174 */ array(57, 62, ),
        /* 175 */ array(1, 16, ),
        /* 176 */ array(16, 28, ),
        /* 177 */ array(16, 28, ),
        /* 178 */ array(57, 62, ),
        /* 179 */ array(16, 28, ),
        /* 180 */ array(1, 16, ),
        /* 181 */ array(16, 28, ),
        /* 182 */ array(16, 28, ),
        /* 183 */ array(16, 28, ),
        /* 184 */ array(16, 28, ),
        /* 185 */ array(16, 28, ),
        /* 186 */ array(16, 28, ),
        /* 187 */ array(16, 28, ),
        /* 188 */ array(16, 28, ),
        /* 189 */ array(13, ),
        /* 190 */ array(28, ),
        /* 191 */ array(28, ),
        /* 192 */ array(20, ),
        /* 193 */ array(20, ),
        /* 194 */ array(1, ),
        /* 195 */ array(20, ),
        /* 196 */ array(1, ),
        /* 197 */ array(2, ),
        /* 198 */ array(2, ),
        /* 199 */ array(36, ),
        /* 200 */ array(),
        /* 201 */ array(),
        /* 202 */ array(),
        /* 203 */ array(),
        /* 204 */ array(),
        /* 205 */ array(16, 23, 25, 26, 28, 29, 35, 36, 37, 52, 59, 63, 77, ),
        /* 206 */ array(16, 19, 28, 36, 59, ),
        /* 207 */ array(36, 57, 59, 63, ),
        /* 208 */ array(15, 17, 18, 34, ),
        /* 209 */ array(16, 28, 36, 59, ),
        /* 210 */ array(30, 36, 59, ),
        /* 211 */ array(18, 60, ),
        /* 212 */ array(2, 19, ),
        /* 213 */ array(36, 59, ),
        /* 214 */ array(36, 59, ),
        /* 215 */ array(16, 24, ),
        /* 216 */ array(35, 37, ),
        /* 217 */ array(24, 77, ),
        /* 218 */ array(35, 63, ),
        /* 219 */ array(23, 35, ),
        /* 220 */ array(19, 57, ),
        /* 221 */ array(35, 37, ),
        /* 222 */ array(35, 37, ),
        /* 223 */ array(18, ),
        /* 224 */ array(2, ),
        /* 225 */ array(24, ),
        /* 226 */ array(17, ),
        /* 227 */ array(18, ),
        /* 228 */ array(18, ),
        /* 229 */ array(36, ),
        /* 230 */ array(57, ),
        /* 231 */ array(18, ),
        /* 232 */ array(36, ),
        /* 233 */ array(18, ),
        /* 234 */ array(17, ),
        /* 235 */ array(18, ),
        /* 236 */ array(34, ),
        /* 237 */ array(34, ),
        /* 238 */ array(18, ),
        /* 239 */ array(17, ),
        /* 240 */ array(61, ),
        /* 241 */ array(18, ),
        /* 242 */ array(37, ),
        /* 243 */ array(17, ),
        /* 244 */ array(19, ),
        /* 245 */ array(63, ),
        /* 246 */ array(53, ),
        /* 247 */ array(2, ),
        /* 248 */ array(17, ),
        /* 249 */ array(25, ),
        /* 250 */ array(18, ),
        /* 251 */ array(18, ),
        /* 252 */ array(61, ),
        /* 253 */ array(),
        /* 254 */ array(),
        /* 255 */ array(),
        /* 256 */ array(),
        /* 257 */ array(),
        /* 258 */ array(),
        /* 259 */ array(),
        /* 260 */ array(),
        /* 261 */ array(),
        /* 262 */ array(),
        /* 263 */ array(),
        /* 264 */ array(),
        /* 265 */ array(),
        /* 266 */ array(),
        /* 267 */ array(),
        /* 268 */ array(),
        /* 269 */ array(),
        /* 270 */ array(),
        /* 271 */ array(),
        /* 272 */ array(),
        /* 273 */ array(),
        /* 274 */ array(),
        /* 275 */ array(),
        /* 276 */ array(),
        /* 277 */ array(),
        /* 278 */ array(),
        /* 279 */ array(),
        /* 280 */ array(),
        /* 281 */ array(),
        /* 282 */ array(),
        /* 283 */ array(),
        /* 284 */ array(),
        /* 285 */ array(),
        /* 286 */ array(),
        /* 287 */ array(),
        /* 288 */ array(),
        /* 289 */ array(),
        /* 290 */ array(),
        /* 291 */ array(),
        /* 292 */ array(),
        /* 293 */ array(),
        /* 294 */ array(),
        /* 295 */ array(),
        /* 296 */ array(),
        /* 297 */ array(),
        /* 298 */ array(),
        /* 299 */ array(),
        /* 300 */ array(),
        /* 301 */ array(),
        /* 302 */ array(),
        /* 303 */ array(),
        /* 304 */ array(),
        /* 305 */ array(),
        /* 306 */ array(),
        /* 307 */ array(),
        /* 308 */ array(),
        /* 309 */ array(),
        /* 310 */ array(),
        /* 311 */ array(),
        /* 312 */ array(),
        /* 313 */ array(),
        /* 314 */ array(),
        /* 315 */ array(),
        /* 316 */ array(),
        /* 317 */ array(),
        /* 318 */ array(),
        /* 319 */ array(),
        /* 320 */ array(),
        /* 321 */ array(),
        /* 322 */ array(),
        /* 323 */ array(),
        /* 324 */ array(),
        /* 325 */ array(),
        /* 326 */ array(),
        /* 327 */ array(),
        /* 328 */ array(),
        /* 329 */ array(),
        /* 330 */ array(),
        /* 331 */ array(),
        /* 332 */ array(),
        /* 333 */ array(),
        /* 334 */ array(),
        /* 335 */ array(),
        /* 336 */ array(),
        /* 337 */ array(),
        /* 338 */ array(),
        /* 339 */ array(),
        /* 340 */ array(),
        /* 341 */ array(),
        /* 342 */ array(),
        /* 343 */ array(),
        /* 344 */ array(),
        /* 345 */ array(),
        /* 346 */ array(),
        /* 347 */ array(),
        /* 348 */ array(),
        /* 349 */ array(),
        /* 350 */ array(),
        /* 351 */ array(),
        /* 352 */ array(),
        /* 353 */ array(),
        /* 354 */ array(),
        /* 355 */ array(),
        /* 356 */ array(),
        /* 357 */ array(),
        /* 358 */ array(),
        /* 359 */ array(),
        /* 360 */ array(),
        /* 361 */ array(),
        /* 362 */ array(),
        /* 363 */ array(),
        /* 364 */ array(),
        /* 365 */ array(),
        /* 366 */ array(),
        /* 367 */ array(),
        /* 368 */ array(),
        /* 369 */ array(),
        /* 370 */ array(),
        /* 371 */ array(),
        /* 372 */ array(),
        /* 373 */ array(),
        /* 374 */ array(),
        /* 375 */ array(),
        /* 376 */ array(),
        /* 377 */ array(),
        /* 378 */ array(),
        /* 379 */ array(),
        /* 380 */ array(),
        /* 381 */ array(),
        /* 382 */ array(),
        /* 383 */ array(),
        /* 384 */ array(),
        /* 385 */ array(),
        /* 386 */ array(),
);
    static public $yy_default = array(
 /*     0 */   390,  571,  588,  588,  542,  542,  542,  588,  588,  588,
 /*    10 */   588,  588,  588,  588,  588,  588,  588,  588,  588,  588,
 /*    20 */   588,  588,  588,  588,  588,  588,  588,  588,  588,  588,
 /*    30 */   588,  588,  588,  588,  588,  588,  588,  588,  588,  588,
 /*    40 */   588,  588,  588,  588,  588,  588,  588,  588,  588,  588,
 /*    50 */   588,  588,  588,  588,  588,  588,  450,  450,  450,  450,
 /*    60 */   588,  588,  588,  588,  455,  588,  588,  588,  588,  588,
 /*    70 */   588,  573,  457,  541,  574,  455,  471,  460,  540,  480,
 /*    80 */   484,  432,  461,  452,  479,  483,  572,  474,  475,  476,
 /*    90 */   487,  488,  499,  463,  450,  387,  588,  450,  450,  554,
 /*   100 */   588,  507,  470,  450,  450,  588,  588,  450,  450,  588,
 /*   110 */   588,  463,  588,  515,  463,  463,  515,  463,  515,  588,
 /*   120 */   508,  463,  508,  588,  588,  588,  588,  588,  588,  588,
 /*   130 */   588,  588,  588,  588,  588,  508,  515,  588,  588,  588,
 /*   140 */   588,  588,  463,  588,  588,  467,  450,  473,  551,  490,
 /*   150 */   450,  468,  486,  491,  492,  508,  466,  549,  588,  516,
 /*   160 */   588,  588,  588,  588,  533,  515,  532,  588,  588,  513,
 /*   170 */   588,  588,  588,  588,  534,  588,  588,  588,  535,  588,
 /*   180 */   588,  588,  588,  588,  588,  588,  588,  588,  588,  405,
 /*   190 */   587,  587,  529,  555,  470,  552,  507,  543,  544,  515,
 /*   200 */   515,  515,  548,  548,  548,  465,  499,  499,  588,  499,
 /*   210 */   499,  588,  527,  485,  499,  489,  588,  489,  588,  588,
 /*   220 */   495,  588,  588,  588,  527,  489,  588,  588,  588,  527,
 /*   230 */   495,  588,  553,  588,  588,  588,  497,  588,  588,  588,
 /*   240 */   588,  588,  588,  588,  588,  588,  501,  527,  588,  458,
 /*   250 */   588,  588,  588,  435,  524,  439,  501,  523,  511,  569,
 /*   260 */   521,  415,  522,  570,  520,  388,  537,  512,  440,  462,
 /*   270 */   459,  429,  550,  586,  430,  536,  528,  538,  539,  434,
 /*   280 */   433,  565,  441,  527,  442,  547,  443,  526,  438,  449,
 /*   290 */   428,  431,  436,  437,  444,  445,  498,  496,  504,  464,
 /*   300 */   465,  494,  493,  545,  546,  446,  447,  427,  448,  397,
 /*   310 */   396,  398,  399,  400,  395,  394,  389,  391,  392,  393,
 /*   320 */   401,  402,  411,  410,  412,  413,  414,  409,  408,  403,
 /*   330 */   404,  406,  407,  509,  514,  421,  420,  530,  422,  423,
 /*   340 */   419,  418,  531,  510,  416,  417,  583,  424,  426,  581,
 /*   350 */   579,  584,  585,  578,  580,  577,  425,  582,  575,  576,
 /*   360 */   506,  469,  503,  502,  505,  477,  478,  472,  500,  517,
 /*   370 */   525,  518,  519,  481,  482,  563,  562,  564,  566,  567,
 /*   380 */   561,  560,  556,  557,  558,  559,  568,
);
    const YYNOCODE = 122;
    const YYSTACKDEPTH = 100;
    const YYNSTATE = 387;
    const YYNRULE = 201;
    const YYERRORSYMBOL = 79;
    const YYERRSYMDT = 'yy0';
    const YYFALLBACK = 0;
    static public $yyFallback = array(
    );
    static function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        self::$yyTraceFILE = $TraceFILE;
        self::$yyTracePrompt = $zTracePrompt;
    }

    static function PrintTrace()
    {
        self::$yyTraceFILE = fopen('php://output', 'w');
        self::$yyTracePrompt = '<br>';
    }

    static public $yyTraceFILE;
    static public $yyTracePrompt;
    public $yyidx;                    /* Index of top element in stack */
    public $yyerrcnt;                 /* Shifts left before out of the error */
    public $yystack = array();  /* The parser's stack */

    public $yyTokenName = array(
  '$',             'VERT',          'COLON',         'COMMENT',
  'PHPSTARTTAG',   'PHPENDTAG',     'ASPSTARTTAG',   'ASPENDTAG',
  'FAKEPHPSTARTTAG',  'XMLTAG',        'OTHER',         'LINEBREAK',
  'LITERALSTART',  'LITERALEND',    'LITERAL',       'LDEL',
  'RDEL',          'DOLLAR',        'ID',            'EQUAL',
  'PTR',           'LDELIF',        'LDELFOR',       'SEMICOLON',
  'INCDEC',        'TO',            'STEP',          'LDELFOREACH',
  'SPACE',         'AS',            'APTR',          'LDELSETFILTER',
  'SMARTYBLOCKCHILD',  'LDELSLASH',     'INTEGER',       'COMMA',
  'OPENP',         'CLOSEP',        'MATH',          'UNIMATH',
  'ANDSYM',        'ISIN',          'ISDIVBY',       'ISNOTDIVBY',
  'ISEVEN',        'ISNOTEVEN',     'ISEVENBY',      'ISNOTEVENBY',
  'ISODD',         'ISNOTODD',      'ISODDBY',       'ISNOTODDBY',
  'INSTANCEOF',    'QMARK',         'NOT',           'TYPECAST',
  'HEX',           'DOT',           'SINGLEQUOTESTRING',  'DOUBLECOLON',
  'AT',            'HATCH',         'OPENB',         'CLOSEB',
  'EQUALS',        'NOTEQUALS',     'GREATERTHAN',   'LESSTHAN',
  'GREATEREQUAL',  'LESSEQUAL',     'IDENTITY',      'NONEIDENTITY',
  'MOD',           'LAND',          'LOR',           'LXOR',
  'QUOTE',         'BACKTICK',      'DOLLARID',      'error',
  'start',         'template',      'template_element',  'smartytag',
  'literal',       'literal_elements',  'literal_element',  'value',
  'modifierlist',  'attributes',    'expr',          'varindexed',
  'statement',     'statements',    'optspace',      'varvar',
  'foraction',     'modparameters',  'attribute',     'ternary',
  'array',         'ifcond',        'lop',           'variable',
  'function',      'doublequoted_with_quotes',  'static_class_access',  'object',
  'arrayindex',    'indexdef',      'varvarele',     'objectchain',
  'objectelement',  'method',        'params',        'modifier',
  'modparameter',  'arrayelements',  'arrayelement',  'doublequoted',
  'doublequotedcontent',
    );

    static public $yyRuleName = array(
 /*   0 */ "start ::= template",
 /*   1 */ "template ::= template_element",
 /*   2 */ "template ::= template template_element",
 /*   3 */ "template ::=",
 /*   4 */ "template_element ::= smartytag",
 /*   5 */ "template_element ::= COMMENT",
 /*   6 */ "template_element ::= literal",
 /*   7 */ "template_element ::= PHPSTARTTAG",
 /*   8 */ "template_element ::= PHPENDTAG",
 /*   9 */ "template_element ::= ASPSTARTTAG",
 /*  10 */ "template_element ::= ASPENDTAG",
 /*  11 */ "template_element ::= FAKEPHPSTARTTAG",
 /*  12 */ "template_element ::= XMLTAG",
 /*  13 */ "template_element ::= OTHER",
 /*  14 */ "template_element ::= LINEBREAK",
 /*  15 */ "literal ::= LITERALSTART LITERALEND",
 /*  16 */ "literal ::= LITERALSTART literal_elements LITERALEND",
 /*  17 */ "literal_elements ::= literal_elements literal_element",
 /*  18 */ "literal_elements ::=",
 /*  19 */ "literal_element ::= literal",
 /*  20 */ "literal_element ::= LITERAL",
 /*  21 */ "literal_element ::= PHPSTARTTAG",
 /*  22 */ "literal_element ::= FAKEPHPSTARTTAG",
 /*  23 */ "literal_element ::= PHPENDTAG",
 /*  24 */ "literal_element ::= ASPSTARTTAG",
 /*  25 */ "literal_element ::= ASPENDTAG",
 /*  26 */ "smartytag ::= LDEL value RDEL",
 /*  27 */ "smartytag ::= LDEL value modifierlist attributes RDEL",
 /*  28 */ "smartytag ::= LDEL value attributes RDEL",
 /*  29 */ "smartytag ::= LDEL expr modifierlist attributes RDEL",
 /*  30 */ "smartytag ::= LDEL expr attributes RDEL",
 /*  31 */ "smartytag ::= LDEL DOLLAR ID EQUAL value RDEL",
 /*  32 */ "smartytag ::= LDEL DOLLAR ID EQUAL expr RDEL",
 /*  33 */ "smartytag ::= LDEL DOLLAR ID EQUAL expr attributes RDEL",
 /*  34 */ "smartytag ::= LDEL varindexed EQUAL expr attributes RDEL",
 /*  35 */ "smartytag ::= LDEL ID attributes RDEL",
 /*  36 */ "smartytag ::= LDEL ID RDEL",
 /*  37 */ "smartytag ::= LDEL ID PTR ID attributes RDEL",
 /*  38 */ "smartytag ::= LDEL ID modifierlist attributes RDEL",
 /*  39 */ "smartytag ::= LDEL ID PTR ID modifierlist attributes RDEL",
 /*  40 */ "smartytag ::= LDELIF expr RDEL",
 /*  41 */ "smartytag ::= LDELIF expr attributes RDEL",
 /*  42 */ "smartytag ::= LDELIF statement RDEL",
 /*  43 */ "smartytag ::= LDELIF statement attributes RDEL",
 /*  44 */ "smartytag ::= LDELFOR statements SEMICOLON optspace expr SEMICOLON optspace DOLLAR varvar foraction attributes RDEL",
 /*  45 */ "foraction ::= EQUAL expr",
 /*  46 */ "foraction ::= INCDEC",
 /*  47 */ "smartytag ::= LDELFOR statement TO expr attributes RDEL",
 /*  48 */ "smartytag ::= LDELFOR statement TO expr STEP expr attributes RDEL",
 /*  49 */ "smartytag ::= LDELFOREACH attributes RDEL",
 /*  50 */ "smartytag ::= LDELFOREACH SPACE value AS DOLLAR varvar attributes RDEL",
 /*  51 */ "smartytag ::= LDELFOREACH SPACE value AS DOLLAR varvar APTR DOLLAR varvar attributes RDEL",
 /*  52 */ "smartytag ::= LDELFOREACH SPACE expr AS DOLLAR varvar attributes RDEL",
 /*  53 */ "smartytag ::= LDELFOREACH SPACE expr AS DOLLAR varvar APTR DOLLAR varvar attributes RDEL",
 /*  54 */ "smartytag ::= LDELSETFILTER ID modparameters RDEL",
 /*  55 */ "smartytag ::= LDELSETFILTER ID modparameters modifierlist RDEL",
 /*  56 */ "smartytag ::= SMARTYBLOCKCHILD",
 /*  57 */ "smartytag ::= LDELSLASH ID RDEL",
 /*  58 */ "smartytag ::= LDELSLASH ID modifierlist RDEL",
 /*  59 */ "smartytag ::= LDELSLASH ID PTR ID RDEL",
 /*  60 */ "smartytag ::= LDELSLASH ID PTR ID modifierlist RDEL",
 /*  61 */ "attributes ::= attributes attribute",
 /*  62 */ "attributes ::= attribute",
 /*  63 */ "attributes ::=",
 /*  64 */ "attribute ::= SPACE ID EQUAL ID",
 /*  65 */ "attribute ::= SPACE ID EQUAL expr",
 /*  66 */ "attribute ::= SPACE ID EQUAL value",
 /*  67 */ "attribute ::= SPACE ID",
 /*  68 */ "attribute ::= SPACE expr",
 /*  69 */ "attribute ::= SPACE value",
 /*  70 */ "attribute ::= SPACE INTEGER EQUAL expr",
 /*  71 */ "statements ::= statement",
 /*  72 */ "statements ::= statements COMMA statement",
 /*  73 */ "statement ::= DOLLAR varvar EQUAL expr",
 /*  74 */ "statement ::= varindexed EQUAL expr",
 /*  75 */ "statement ::= OPENP statement CLOSEP",
 /*  76 */ "expr ::= value",
 /*  77 */ "expr ::= ternary",
 /*  78 */ "expr ::= DOLLAR ID COLON ID",
 /*  79 */ "expr ::= expr MATH value",
 /*  80 */ "expr ::= expr UNIMATH value",
 /*  81 */ "expr ::= expr ANDSYM value",
 /*  82 */ "expr ::= array",
 /*  83 */ "expr ::= expr modifierlist",
 /*  84 */ "expr ::= expr ifcond expr",
 /*  85 */ "expr ::= expr ISIN array",
 /*  86 */ "expr ::= expr ISIN value",
 /*  87 */ "expr ::= expr lop expr",
 /*  88 */ "expr ::= expr ISDIVBY expr",
 /*  89 */ "expr ::= expr ISNOTDIVBY expr",
 /*  90 */ "expr ::= expr ISEVEN",
 /*  91 */ "expr ::= expr ISNOTEVEN",
 /*  92 */ "expr ::= expr ISEVENBY expr",
 /*  93 */ "expr ::= expr ISNOTEVENBY expr",
 /*  94 */ "expr ::= expr ISODD",
 /*  95 */ "expr ::= expr ISNOTODD",
 /*  96 */ "expr ::= expr ISODDBY expr",
 /*  97 */ "expr ::= expr ISNOTODDBY expr",
 /*  98 */ "expr ::= value INSTANCEOF ID",
 /*  99 */ "expr ::= value INSTANCEOF value",
 /* 100 */ "ternary ::= OPENP expr CLOSEP QMARK DOLLAR ID COLON expr",
 /* 101 */ "ternary ::= OPENP expr CLOSEP QMARK expr COLON expr",
 /* 102 */ "value ::= variable",
 /* 103 */ "value ::= UNIMATH value",
 /* 104 */ "value ::= NOT value",
 /* 105 */ "value ::= TYPECAST value",
 /* 106 */ "value ::= variable INCDEC",
 /* 107 */ "value ::= HEX",
 /* 108 */ "value ::= INTEGER",
 /* 109 */ "value ::= INTEGER DOT INTEGER",
 /* 110 */ "value ::= INTEGER DOT",
 /* 111 */ "value ::= DOT INTEGER",
 /* 112 */ "value ::= ID",
 /* 113 */ "value ::= function",
 /* 114 */ "value ::= OPENP expr CLOSEP",
 /* 115 */ "value ::= SINGLEQUOTESTRING",
 /* 116 */ "value ::= doublequoted_with_quotes",
 /* 117 */ "value ::= ID DOUBLECOLON static_class_access",
 /* 118 */ "value ::= varindexed DOUBLECOLON static_class_access",
 /* 119 */ "value ::= smartytag",
 /* 120 */ "value ::= value modifierlist",
 /* 121 */ "variable ::= varindexed",
 /* 122 */ "variable ::= DOLLAR varvar AT ID",
 /* 123 */ "variable ::= object",
 /* 124 */ "variable ::= HATCH ID HATCH",
 /* 125 */ "variable ::= HATCH variable HATCH",
 /* 126 */ "varindexed ::= DOLLAR varvar arrayindex",
 /* 127 */ "arrayindex ::= arrayindex indexdef",
 /* 128 */ "arrayindex ::=",
 /* 129 */ "indexdef ::= DOT DOLLAR varvar",
 /* 130 */ "indexdef ::= DOT DOLLAR varvar AT ID",
 /* 131 */ "indexdef ::= DOT ID",
 /* 132 */ "indexdef ::= DOT INTEGER",
 /* 133 */ "indexdef ::= DOT LDEL expr RDEL",
 /* 134 */ "indexdef ::= OPENB ID CLOSEB",
 /* 135 */ "indexdef ::= OPENB ID DOT ID CLOSEB",
 /* 136 */ "indexdef ::= OPENB expr CLOSEB",
 /* 137 */ "indexdef ::= OPENB CLOSEB",
 /* 138 */ "varvar ::= varvarele",
 /* 139 */ "varvar ::= varvar varvarele",
 /* 140 */ "varvarele ::= ID",
 /* 141 */ "varvarele ::= LDEL expr RDEL",
 /* 142 */ "object ::= varindexed objectchain",
 /* 143 */ "objectchain ::= objectelement",
 /* 144 */ "objectchain ::= objectchain objectelement",
 /* 145 */ "objectelement ::= PTR ID arrayindex",
 /* 146 */ "objectelement ::= PTR DOLLAR varvar arrayindex",
 /* 147 */ "objectelement ::= PTR LDEL expr RDEL arrayindex",
 /* 148 */ "objectelement ::= PTR ID LDEL expr RDEL arrayindex",
 /* 149 */ "objectelement ::= PTR method",
 /* 150 */ "function ::= ID OPENP params CLOSEP",
 /* 151 */ "method ::= ID OPENP params CLOSEP",
 /* 152 */ "method ::= DOLLAR ID OPENP params CLOSEP",
 /* 153 */ "params ::= params COMMA expr",
 /* 154 */ "params ::= expr",
 /* 155 */ "params ::=",
 /* 156 */ "modifierlist ::= modifierlist modifier modparameters",
 /* 157 */ "modifierlist ::= modifier modparameters",
 /* 158 */ "modifier ::= VERT AT ID",
 /* 159 */ "modifier ::= VERT ID",
 /* 160 */ "modparameters ::= modparameters modparameter",
 /* 161 */ "modparameters ::=",
 /* 162 */ "modparameter ::= COLON value",
 /* 163 */ "modparameter ::= COLON array",
 /* 164 */ "static_class_access ::= method",
 /* 165 */ "static_class_access ::= method objectchain",
 /* 166 */ "static_class_access ::= ID",
 /* 167 */ "static_class_access ::= DOLLAR ID arrayindex",
 /* 168 */ "static_class_access ::= DOLLAR ID arrayindex objectchain",
 /* 169 */ "ifcond ::= EQUALS",
 /* 170 */ "ifcond ::= NOTEQUALS",
 /* 171 */ "ifcond ::= GREATERTHAN",
 /* 172 */ "ifcond ::= LESSTHAN",
 /* 173 */ "ifcond ::= GREATEREQUAL",
 /* 174 */ "ifcond ::= LESSEQUAL",
 /* 175 */ "ifcond ::= IDENTITY",
 /* 176 */ "ifcond ::= NONEIDENTITY",
 /* 177 */ "ifcond ::= MOD",
 /* 178 */ "lop ::= LAND",
 /* 179 */ "lop ::= LOR",
 /* 180 */ "lop ::= LXOR",
 /* 181 */ "array ::= OPENB arrayelements CLOSEB",
 /* 182 */ "arrayelements ::= arrayelement",
 /* 183 */ "arrayelements ::= arrayelements COMMA arrayelement",
 /* 184 */ "arrayelements ::=",
 /* 185 */ "arrayelement ::= value APTR expr",
 /* 186 */ "arrayelement ::= ID APTR expr",
 /* 187 */ "arrayelement ::= expr",
 /* 188 */ "doublequoted_with_quotes ::= QUOTE QUOTE",
 /* 189 */ "doublequoted_with_quotes ::= QUOTE doublequoted QUOTE",
 /* 190 */ "doublequoted ::= doublequoted doublequotedcontent",
 /* 191 */ "doublequoted ::= doublequotedcontent",
 /* 192 */ "doublequotedcontent ::= BACKTICK variable BACKTICK",
 /* 193 */ "doublequotedcontent ::= BACKTICK expr BACKTICK",
 /* 194 */ "doublequotedcontent ::= DOLLARID",
 /* 195 */ "doublequotedcontent ::= LDEL variable RDEL",
 /* 196 */ "doublequotedcontent ::= LDEL expr RDEL",
 /* 197 */ "doublequotedcontent ::= smartytag",
 /* 198 */ "doublequotedcontent ::= OTHER",
 /* 199 */ "optspace ::= SPACE",
 /* 200 */ "optspace ::=",
    );

    function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[$tokenType];
        } else {
            return "Unknown";
        }
    }

    static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:  break;   /* If no destructor action specified: do nothing */
        }
    }

    function yy_pop_parser_stack()
    {
        if (!count($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if (self::$yyTraceFILE && $this->yyidx >= 0) {
            fwrite(self::$yyTraceFILE,
                self::$yyTracePrompt . 'Popping ' . $this->yyTokenName[$yytos->major] .
                    "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx--;
        return $yymajor;
    }

    function __destruct()
    {
        while ($this->yystack !== Array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource(self::$yyTraceFILE)) {
            fclose(self::$yyTraceFILE);
        }
    }

    function yy_get_expected_tokens($token)
    {
        $state = $this->yystack[$this->yyidx]->stateno;
        $expected = self::$yyExpectedTokens[$state];
        if (in_array($token, self::$yyExpectedTokens[$state], true)) {
            return $expected;
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno]['rhs'];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno]['lhs']);
                    if (isset(self::$yyExpectedTokens[$nextstate])) {
		        $expected = array_merge($expected, self::$yyExpectedTokens[$nextstate]);
                            if (in_array($token,
                                  self::$yyExpectedTokens[$nextstate], true)) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return array_unique($expected);
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno]['lhs'];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
	$this->yyidx = $yyidx;
	$this->yystack = $stack;
        return array_unique($expected);
    }

    function yy_is_expected_token($token)
    {
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[$this->yyidx]->stateno;
        if (in_array($token, self::$yyExpectedTokens[$state], true)) {
            return true;
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno]['rhs'];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno]['lhs']);
                    if (isset(self::$yyExpectedTokens[$nextstate]) &&
                          in_array($token, self::$yyExpectedTokens[$nextstate], true)) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        return true;
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno]['lhs'];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;
        return true;
    }

   function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[$this->yyidx]->stateno;

        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[$stateno])) {
            // no shift actions
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_shift_ofst[$stateno];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
              self::$yy_lookahead[$i] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback)
                   && ($iFallback = self::$yyFallback[$iLookAhead]) != 0) {
                if (self::$yyTraceFILE) {
                    fwrite(self::$yyTraceFILE, self::$yyTracePrompt . "FALLBACK " .
                        $this->yyTokenName[$iLookAhead] . " => " .
                        $this->yyTokenName[$iFallback] . "\n");
                }
                return $this->yy_find_shift_action($iFallback);
            }
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */

        if (!isset(self::$yy_reduce_ofst[$stateno])) {
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_reduce_ofst[$stateno];
        if ($i == self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
              self::$yy_lookahead[$i] != $iLookAhead) {
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx--;
            if (self::$yyTraceFILE) {
                fprintf(self::$yyTraceFILE, "%sStack Overflow!\n", self::$yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
#line 83 "smarty_internal_templateparser.y"

    $this->internalError = true;
    $this->compiler->trigger_template_error("Stack overflow in template parser");
#line 1715 "smarty_internal_templateparser.php"
            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        array_push($this->yystack, $yytos);
        if (self::$yyTraceFILE && $this->yyidx > 0) {
            fprintf(self::$yyTraceFILE, "%sShift %d\n", self::$yyTracePrompt,
                $yyNewState);
            fprintf(self::$yyTraceFILE, "%sStack:", self::$yyTracePrompt);
            for($i = 1; $i <= $this->yyidx; $i++) {
                fprintf(self::$yyTraceFILE, " %s",
                    $this->yyTokenName[$this->yystack[$i]->major]);
            }
            fwrite(self::$yyTraceFILE,"\n");
        }
    }

    static public $yyRuleInfo = array(
  array( 'lhs' => 80, 'rhs' => 1 ),
  array( 'lhs' => 81, 'rhs' => 1 ),
  array( 'lhs' => 81, 'rhs' => 2 ),
  array( 'lhs' => 81, 'rhs' => 0 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 82, 'rhs' => 1 ),
  array( 'lhs' => 84, 'rhs' => 2 ),
  array( 'lhs' => 84, 'rhs' => 3 ),
  array( 'lhs' => 85, 'rhs' => 2 ),
  array( 'lhs' => 85, 'rhs' => 0 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 86, 'rhs' => 1 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 5 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 5 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 83, 'rhs' => 7 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 83, 'rhs' => 5 ),
  array( 'lhs' => 83, 'rhs' => 7 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 12 ),
  array( 'lhs' => 96, 'rhs' => 2 ),
  array( 'lhs' => 96, 'rhs' => 1 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 83, 'rhs' => 8 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 8 ),
  array( 'lhs' => 83, 'rhs' => 11 ),
  array( 'lhs' => 83, 'rhs' => 8 ),
  array( 'lhs' => 83, 'rhs' => 11 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 5 ),
  array( 'lhs' => 83, 'rhs' => 1 ),
  array( 'lhs' => 83, 'rhs' => 3 ),
  array( 'lhs' => 83, 'rhs' => 4 ),
  array( 'lhs' => 83, 'rhs' => 5 ),
  array( 'lhs' => 83, 'rhs' => 6 ),
  array( 'lhs' => 89, 'rhs' => 2 ),
  array( 'lhs' => 89, 'rhs' => 1 ),
  array( 'lhs' => 89, 'rhs' => 0 ),
  array( 'lhs' => 98, 'rhs' => 4 ),
  array( 'lhs' => 98, 'rhs' => 4 ),
  array( 'lhs' => 98, 'rhs' => 4 ),
  array( 'lhs' => 98, 'rhs' => 2 ),
  array( 'lhs' => 98, 'rhs' => 2 ),
  array( 'lhs' => 98, 'rhs' => 2 ),
  array( 'lhs' => 98, 'rhs' => 4 ),
  array( 'lhs' => 93, 'rhs' => 1 ),
  array( 'lhs' => 93, 'rhs' => 3 ),
  array( 'lhs' => 92, 'rhs' => 4 ),
  array( 'lhs' => 92, 'rhs' => 3 ),
  array( 'lhs' => 92, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 1 ),
  array( 'lhs' => 90, 'rhs' => 1 ),
  array( 'lhs' => 90, 'rhs' => 4 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 1 ),
  array( 'lhs' => 90, 'rhs' => 2 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 2 ),
  array( 'lhs' => 90, 'rhs' => 2 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 2 ),
  array( 'lhs' => 90, 'rhs' => 2 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 90, 'rhs' => 3 ),
  array( 'lhs' => 99, 'rhs' => 8 ),
  array( 'lhs' => 99, 'rhs' => 7 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 3 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 3 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 3 ),
  array( 'lhs' => 87, 'rhs' => 3 ),
  array( 'lhs' => 87, 'rhs' => 1 ),
  array( 'lhs' => 87, 'rhs' => 2 ),
  array( 'lhs' => 103, 'rhs' => 1 ),
  array( 'lhs' => 103, 'rhs' => 4 ),
  array( 'lhs' => 103, 'rhs' => 1 ),
  array( 'lhs' => 103, 'rhs' => 3 ),
  array( 'lhs' => 103, 'rhs' => 3 ),
  array( 'lhs' => 91, 'rhs' => 3 ),
  array( 'lhs' => 108, 'rhs' => 2 ),
  array( 'lhs' => 108, 'rhs' => 0 ),
  array( 'lhs' => 109, 'rhs' => 3 ),
  array( 'lhs' => 109, 'rhs' => 5 ),
  array( 'lhs' => 109, 'rhs' => 2 ),
  array( 'lhs' => 109, 'rhs' => 2 ),
  array( 'lhs' => 109, 'rhs' => 4 ),
  array( 'lhs' => 109, 'rhs' => 3 ),
  array( 'lhs' => 109, 'rhs' => 5 ),
  array( 'lhs' => 109, 'rhs' => 3 ),
  array( 'lhs' => 109, 'rhs' => 2 ),
  array( 'lhs' => 95, 'rhs' => 1 ),
  array( 'lhs' => 95, 'rhs' => 2 ),
  array( 'lhs' => 110, 'rhs' => 1 ),
  array( 'lhs' => 110, 'rhs' => 3 ),
  array( 'lhs' => 107, 'rhs' => 2 ),
  array( 'lhs' => 111, 'rhs' => 1 ),
  array( 'lhs' => 111, 'rhs' => 2 ),
  array( 'lhs' => 112, 'rhs' => 3 ),
  array( 'lhs' => 112, 'rhs' => 4 ),
  array( 'lhs' => 112, 'rhs' => 5 ),
  array( 'lhs' => 112, 'rhs' => 6 ),
  array( 'lhs' => 112, 'rhs' => 2 ),
  array( 'lhs' => 104, 'rhs' => 4 ),
  array( 'lhs' => 113, 'rhs' => 4 ),
  array( 'lhs' => 113, 'rhs' => 5 ),
  array( 'lhs' => 114, 'rhs' => 3 ),
  array( 'lhs' => 114, 'rhs' => 1 ),
  array( 'lhs' => 114, 'rhs' => 0 ),
  array( 'lhs' => 88, 'rhs' => 3 ),
  array( 'lhs' => 88, 'rhs' => 2 ),
  array( 'lhs' => 115, 'rhs' => 3 ),
  array( 'lhs' => 115, 'rhs' => 2 ),
  array( 'lhs' => 97, 'rhs' => 2 ),
  array( 'lhs' => 97, 'rhs' => 0 ),
  array( 'lhs' => 116, 'rhs' => 2 ),
  array( 'lhs' => 116, 'rhs' => 2 ),
  array( 'lhs' => 106, 'rhs' => 1 ),
  array( 'lhs' => 106, 'rhs' => 2 ),
  array( 'lhs' => 106, 'rhs' => 1 ),
  array( 'lhs' => 106, 'rhs' => 3 ),
  array( 'lhs' => 106, 'rhs' => 4 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 101, 'rhs' => 1 ),
  array( 'lhs' => 102, 'rhs' => 1 ),
  array( 'lhs' => 102, 'rhs' => 1 ),
  array( 'lhs' => 102, 'rhs' => 1 ),
  array( 'lhs' => 100, 'rhs' => 3 ),
  array( 'lhs' => 117, 'rhs' => 1 ),
  array( 'lhs' => 117, 'rhs' => 3 ),
  array( 'lhs' => 117, 'rhs' => 0 ),
  array( 'lhs' => 118, 'rhs' => 3 ),
  array( 'lhs' => 118, 'rhs' => 3 ),
  array( 'lhs' => 118, 'rhs' => 1 ),
  array( 'lhs' => 105, 'rhs' => 2 ),
  array( 'lhs' => 105, 'rhs' => 3 ),
  array( 'lhs' => 119, 'rhs' => 2 ),
  array( 'lhs' => 119, 'rhs' => 1 ),
  array( 'lhs' => 120, 'rhs' => 3 ),
  array( 'lhs' => 120, 'rhs' => 3 ),
  array( 'lhs' => 120, 'rhs' => 1 ),
  array( 'lhs' => 120, 'rhs' => 3 ),
  array( 'lhs' => 120, 'rhs' => 3 ),
  array( 'lhs' => 120, 'rhs' => 1 ),
  array( 'lhs' => 120, 'rhs' => 1 ),
  array( 'lhs' => 94, 'rhs' => 1 ),
  array( 'lhs' => 94, 'rhs' => 0 ),
    );

    static public $yyReduceMap = array(
        0 => 0,
        1 => 1,
        2 => 1,
        4 => 4,
        5 => 5,
        6 => 6,
        7 => 7,
        8 => 8,
        9 => 9,
        10 => 10,
        11 => 11,
        12 => 12,
        13 => 13,
        14 => 14,
        15 => 15,
        18 => 15,
        200 => 15,
        16 => 16,
        75 => 16,
        17 => 17,
        103 => 17,
        105 => 17,
        106 => 17,
        127 => 17,
        165 => 17,
        19 => 19,
        20 => 19,
        46 => 19,
        68 => 19,
        69 => 19,
        76 => 19,
        77 => 19,
        82 => 19,
        102 => 19,
        107 => 19,
        108 => 19,
        113 => 19,
        115 => 19,
        116 => 19,
        123 => 19,
        138 => 19,
        164 => 19,
        166 => 19,
        182 => 19,
        187 => 19,
        199 => 19,
        21 => 21,
        22 => 21,
        23 => 23,
        24 => 24,
        25 => 25,
        26 => 26,
        27 => 27,
        28 => 28,
        30 => 28,
        29 => 29,
        31 => 31,
        32 => 31,
        33 => 33,
        34 => 34,
        35 => 35,
        36 => 36,
        37 => 37,
        38 => 38,
        39 => 39,
        40 => 40,
        41 => 41,
        43 => 41,
        42 => 42,
        44 => 44,
        45 => 45,
        47 => 47,
        48 => 48,
        49 => 49,
        50 => 50,
        51 => 51,
        52 => 52,
        53 => 53,
        54 => 54,
        55 => 55,
        56 => 56,
        57 => 57,
        58 => 58,
        59 => 59,
        60 => 60,
        61 => 61,
        62 => 62,
        71 => 62,
        154 => 62,
        158 => 62,
        162 => 62,
        163 => 62,
        63 => 63,
        155 => 63,
        161 => 63,
        64 => 64,
        65 => 65,
        66 => 65,
        70 => 65,
        67 => 67,
        72 => 72,
        73 => 73,
        74 => 73,
        78 => 78,
        79 => 79,
        80 => 79,
        81 => 79,
        83 => 83,
        120 => 83,
        84 => 84,
        87 => 84,
        98 => 84,
        85 => 85,
        86 => 86,
        88 => 88,
        89 => 89,
        90 => 90,
        95 => 90,
        91 => 91,
        94 => 91,
        92 => 92,
        97 => 92,
        93 => 93,
        96 => 93,
        99 => 99,
        100 => 100,
        101 => 101,
        104 => 104,
        109 => 109,
        110 => 110,
        111 => 111,
        112 => 112,
        114 => 114,
        117 => 117,
        118 => 118,
        119 => 119,
        121 => 121,
        122 => 122,
        124 => 124,
        125 => 125,
        126 => 126,
        128 => 128,
        184 => 128,
        129 => 129,
        130 => 130,
        131 => 131,
        132 => 132,
        133 => 133,
        136 => 133,
        134 => 134,
        135 => 135,
        137 => 137,
        139 => 139,
        140 => 140,
        141 => 141,
        142 => 142,
        143 => 143,
        144 => 144,
        145 => 145,
        146 => 146,
        147 => 147,
        148 => 148,
        149 => 149,
        150 => 150,
        151 => 151,
        152 => 152,
        153 => 153,
        156 => 156,
        157 => 157,
        159 => 159,
        160 => 160,
        167 => 167,
        168 => 168,
        169 => 169,
        170 => 170,
        171 => 171,
        172 => 172,
        173 => 173,
        174 => 174,
        175 => 175,
        176 => 176,
        177 => 177,
        178 => 178,
        179 => 179,
        180 => 180,
        181 => 181,
        183 => 183,
        185 => 185,
        186 => 186,
        188 => 188,
        189 => 189,
        190 => 190,
        191 => 191,
        192 => 192,
        193 => 192,
        195 => 192,
        194 => 194,
        196 => 196,
        197 => 197,
        198 => 198,
    );
#line 94 "smarty_internal_templateparser.y"
    function yy_r0(){
    $this->_retvalue = $this->root_buffer->to_smarty_php();
    }
#line 2145 "smarty_internal_templateparser.php"
#line 102 "smarty_internal_templateparser.y"
    function yy_r1(){
    $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
    }
#line 2150 "smarty_internal_templateparser.php"
#line 118 "smarty_internal_templateparser.y"
    function yy_r4(){
    if ($this->compiler->has_code) {
        $tmp =''; foreach ($this->compiler->prefix_code as $code) {$tmp.=$code;} $this->compiler->prefix_code=array();
        $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode($tmp.$this->yystack[$this->yyidx + 0]->minor,true));
    } else {
        $this->_retvalue = new _smarty_tag($this, $this->yystack[$this->yyidx + 0]->minor);
    }
    $this->compiler->has_variable_string = false;
    $this->block_nesting_level = count($this->compiler->_tag_stack);
    }
#line 2162 "smarty_internal_templateparser.php"
#line 130 "smarty_internal_templateparser.y"
    function yy_r5(){
    $this->_retvalue = new _smarty_tag($this, '');
    }
#line 2167 "smarty_internal_templateparser.php"
#line 135 "smarty_internal_templateparser.y"
    function yy_r6(){
    $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
    }
#line 2172 "smarty_internal_templateparser.php"
#line 140 "smarty_internal_templateparser.y"
    function yy_r7(){
    if ($this->php_handling == Smarty::PHP_PASSTHRU) {
        $this->_retvalue = new _smarty_text($this, self::escape_start_tag($this->yystack[$this->yyidx + 0]->minor));
    } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
        $this->_retvalue = new _smarty_text($this, htmlspecialchars($this->yystack[$this->yyidx + 0]->minor, ENT_QUOTES));
    } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
        if (!($this->smarty instanceof SmartyBC)) {
            $this->compiler->trigger_template_error (self::Err3);
        }
        $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode('<?php', true));
    } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
        $this->_retvalue = new _smarty_text($this, '');
    }
    }
#line 2188 "smarty_internal_templateparser.php"
#line 156 "smarty_internal_templateparser.y"
    function yy_r8(){
    if ($this->is_xml) {
        $this->compiler->tag_nocache = true;
        $this->is_xml = false;
        $save = $this->template->has_nocache_code;
        $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode("<?php echo '?>';?>", $this->compiler, true));
        $this->template->has_nocache_code = $save;
    } elseif ($this->php_handling == Smarty::PHP_PASSTHRU) {
        $this->_retvalue = new _smarty_text($this, '?<?php ?>>');
    } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
        $this->_retvalue = new _smarty_text($this, htmlspecialchars('?>', ENT_QUOTES));
    } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
        $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode('?>', true));
    } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
        $this->_retvalue = new _smarty_text($this, '');
    }
    }
#line 2207 "smarty_internal_templateparser.php"
#line 175 "smarty_internal_templateparser.y"
    function yy_r9(){
    if ($this->php_handling == Smarty::PHP_PASSTHRU) {
        $this->_retvalue = new _smarty_text($this, '<<?php ?>%');
    } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
        $this->_retvalue = new _smarty_text($this, htmlspecialchars($this->yystack[$this->yyidx + 0]->minor, ENT_QUOTES));
    } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
        if ($this->asp_tags) {
            if (!($this->smarty instanceof SmartyBC)) {
                $this->compiler->trigger_template_error (self::Err3);
            }
            $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode('<%', true));
        } else {
            $this->_retvalue = new _smarty_text($this, '<<?php ?>%');
        }
    } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
        if ($this->asp_tags) {
            $this->_retvalue = new _smarty_text($this, '');
        } else {
            $this->_retvalue = new _smarty_text($this, '<<?php ?>%');
        }
    }
    }
#line 2231 "smarty_internal_templateparser.php"
#line 199 "smarty_internal_templateparser.y"
    function yy_r10(){
    if ($this->php_handling == Smarty::PHP_PASSTHRU) {
        $this->_retvalue = new _smarty_text($this, '%<?php ?>>');
    } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
        $this->_retvalue = new _smarty_text($this, htmlspecialchars('%>', ENT_QUOTES));
    } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
        if ($this->asp_tags) {
            $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode('%>', true));
        } else {
            $this->_retvalue = new _smarty_text($this, '%<?php ?>>');
        }
    } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
        if ($this->asp_tags) {
            $this->_retvalue = new _smarty_text($this, '');
        } else {
            $this->_retvalue = new _smarty_text($this, '%<?php ?>>');
        }
    }
    }
#line 2252 "smarty_internal_templateparser.php"
#line 219 "smarty_internal_templateparser.y"
    function yy_r11(){
    if ($this->lex->strip) {
        $this->_retvalue = new _smarty_text($this, preg_replace('![\$this->yystack[$this->yyidx + 0]->minor ]*[\r\n]+[\$this->yystack[$this->yyidx + 0]->minor ]*!', '', self::escape_start_tag($this->yystack[$this->yyidx + 0]->minor)));
    } else {
        $this->_retvalue = new _smarty_text($this, self::escape_start_tag($this->yystack[$this->yyidx + 0]->minor));
    }
    }
#line 2261 "smarty_internal_templateparser.php"
#line 228 "smarty_internal_templateparser.y"
    function yy_r12(){
    $this->compiler->tag_nocache = true;
    $this->is_xml = true;
    $save = $this->template->has_nocache_code;
    $this->_retvalue = new _smarty_text($this, $this->compiler->processNocacheCode("<?php echo '<?xml';?>", $this->compiler, true));
    $this->template->has_nocache_code = $save;
    }
#line 2270 "smarty_internal_templateparser.php"
#line 237 "smarty_internal_templateparser.y"
    function yy_r13(){
    if ($this->lex->strip) {
        $this->_retvalue = new _smarty_text($this, preg_replace('![\t ]*[\r\n]+[\t ]*!', '', $this->yystack[$this->yyidx + 0]->minor));
    } else {
        $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
    }
    }
#line 2279 "smarty_internal_templateparser.php"
#line 245 "smarty_internal_templateparser.y"
    function yy_r14(){
    $this->_retvalue = new _smarty_linebreak($this, $this->yystack[$this->yyidx + 0]->minor);
    }
#line 2284 "smarty_internal_templateparser.php"
#line 250 "smarty_internal_templateparser.y"
    function yy_r15(){
    $this->_retvalue = '';
    }
#line 2289 "smarty_internal_templateparser.php"
#line 254 "smarty_internal_templateparser.y"
    function yy_r16(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    }
#line 2294 "smarty_internal_templateparser.php"
#line 258 "smarty_internal_templateparser.y"
    function yy_r17(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2299 "smarty_internal_templateparser.php"
#line 266 "smarty_internal_templateparser.y"
    function yy_r19(){
    $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }
#line 2304 "smarty_internal_templateparser.php"
#line 274 "smarty_internal_templateparser.y"
    function yy_r21(){
    $this->_retvalue = self::escape_start_tag($this->yystack[$this->yyidx + 0]->minor);
    }
#line 2309 "smarty_internal_templateparser.php"
#line 282 "smarty_internal_templateparser.y"
    function yy_r23(){
    $this->_retvalue = self::escape_end_tag($this->yystack[$this->yyidx + 0]->minor);
    }
#line 2314 "smarty_internal_templateparser.php"
#line 286 "smarty_internal_templateparser.y"
    function yy_r24(){
    $this->_retvalue = '<<?php ?>%';
    }
#line 2319 "smarty_internal_templateparser.php"
#line 290 "smarty_internal_templateparser.y"
    function yy_r25(){
    $this->_retvalue = '%<?php ?>>';
    }
#line 2324 "smarty_internal_templateparser.php"
#line 300 "smarty_internal_templateparser.y"
    function yy_r26(){
    $this->_retvalue = $this->compiler->compileTag('private_print_expression',array(),array('value'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2329 "smarty_internal_templateparser.php"
#line 304 "smarty_internal_templateparser.y"
    function yy_r27(){
    $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + -1]->minor,array('value'=>$this->yystack[$this->yyidx + -3]->minor, 'modifierlist'=>$this->yystack[$this->yyidx + -2]->minor));
    }
#line 2334 "smarty_internal_templateparser.php"
#line 308 "smarty_internal_templateparser.y"
    function yy_r28(){
    $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + -1]->minor,array('value'=>$this->yystack[$this->yyidx + -2]->minor));
    }
#line 2339 "smarty_internal_templateparser.php"
#line 312 "smarty_internal_templateparser.y"
    function yy_r29(){
    $this->_retvalue = $this->compiler->compileTag('private_print_expression',$this->yystack[$this->yyidx + -1]->minor,array('value'=>$this->yystack[$this->yyidx + -3]->minor,'modifierlist'=>$this->yystack[$this->yyidx + -2]->minor));
    }
#line 2344 "smarty_internal_templateparser.php"
#line 325 "smarty_internal_templateparser.y"
    function yy_r31(){
    $this->_retvalue = $this->compiler->compileTag('assign',array(array('value'=>$this->yystack[$this->yyidx + -1]->minor),array('var'=>"'".$this->yystack[$this->yyidx + -3]->minor."'")));
    }
#line 2349 "smarty_internal_templateparser.php"
#line 333 "smarty_internal_templateparser.y"
    function yy_r33(){
    $this->_retvalue = $this->compiler->compileTag('assign',array_merge(array(array('value'=>$this->yystack[$this->yyidx + -2]->minor),array('var'=>"'".$this->yystack[$this->yyidx + -4]->minor."'")),$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2354 "smarty_internal_templateparser.php"
#line 337 "smarty_internal_templateparser.y"
    function yy_r34(){
    $this->_retvalue = $this->compiler->compileTag('assign',array_merge(array(array('value'=>$this->yystack[$this->yyidx + -2]->minor),array('var'=>$this->yystack[$this->yyidx + -4]->minor['var'])),$this->yystack[$this->yyidx + -1]->minor),array('smarty_internal_index'=>$this->yystack[$this->yyidx + -4]->minor['smarty_internal_index']));
    }
#line 2359 "smarty_internal_templateparser.php"
#line 342 "smarty_internal_templateparser.y"
    function yy_r35(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -2]->minor,$this->yystack[$this->yyidx + -1]->minor);
    }
#line 2364 "smarty_internal_templateparser.php"
#line 346 "smarty_internal_templateparser.y"
    function yy_r36(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -1]->minor,array());
    }
#line 2369 "smarty_internal_templateparser.php"
#line 351 "smarty_internal_templateparser.y"
    function yy_r37(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -4]->minor,$this->yystack[$this->yyidx + -1]->minor,array('object_methode'=>$this->yystack[$this->yyidx + -2]->minor));
    }
#line 2374 "smarty_internal_templateparser.php"
#line 356 "smarty_internal_templateparser.y"
    function yy_r38(){
    $this->_retvalue = '<?php ob_start();?>'.$this->compiler->compileTag($this->yystack[$this->yyidx + -3]->minor,$this->yystack[$this->yyidx + -1]->minor).'<?php echo ';
    $this->_retvalue .= $this->compiler->compileTag('private_modifier',array(),array('modifierlist'=>$this->yystack[$this->yyidx + -2]->minor,'value'=>'ob_get_clean()')).'?>';
    }
#line 2380 "smarty_internal_templateparser.php"
#line 362 "smarty_internal_templateparser.y"
    function yy_r39(){
    $this->_retvalue = '<?php ob_start();?>'.$this->compiler->compileTag($this->yystack[$this->yyidx + -5]->minor,$this->yystack[$this->yyidx + -1]->minor,array('object_methode'=>$this->yystack[$this->yyidx + -3]->minor)).'<?php echo ';
    $this->_retvalue .= $this->compiler->compileTag('private_modifier',array(),array('modifierlist'=>$this->yystack[$this->yyidx + -2]->minor,'value'=>'ob_get_clean()')).'?>';
    }
#line 2386 "smarty_internal_templateparser.php"
#line 368 "smarty_internal_templateparser.y"
    function yy_r40(){
    $tag = trim(substr($this->yystack[$this->yyidx + -2]->minor,$this->lex->ldel_length));
    $this->_retvalue = $this->compiler->compileTag(($tag == 'else if')? 'elseif' : $tag,array(),array('if condition'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2392 "smarty_internal_templateparser.php"
#line 373 "smarty_internal_templateparser.y"
    function yy_r41(){
    $tag = trim(substr($this->yystack[$this->yyidx + -3]->minor,$this->lex->ldel_length));
    $this->_retvalue = $this->compiler->compileTag(($tag == 'else if')? 'elseif' : $tag,$this->yystack[$this->yyidx + -1]->minor,array('if condition'=>$this->yystack[$this->yyidx + -2]->minor));
    }
#line 2398 "smarty_internal_templateparser.php"
#line 378 "smarty_internal_templateparser.y"
    function yy_r42(){
    $tag = trim(substr($this->yystack[$this->yyidx + -2]->minor,$this->lex->ldel_length));
    $this->_retvalue = $this->compiler->compileTag(($tag == 'else if')? 'elseif' : $tag,array(),array('if condition'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2404 "smarty_internal_templateparser.php"
#line 389 "smarty_internal_templateparser.y"
    function yy_r44(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('start'=>$this->yystack[$this->yyidx + -10]->minor),array('ifexp'=>$this->yystack[$this->yyidx + -7]->minor),array('var'=>$this->yystack[$this->yyidx + -3]->minor),array('step'=>$this->yystack[$this->yyidx + -2]->minor))),1);
    }
#line 2409 "smarty_internal_templateparser.php"
#line 393 "smarty_internal_templateparser.y"
    function yy_r45(){
    $this->_retvalue = '='.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2414 "smarty_internal_templateparser.php"
#line 401 "smarty_internal_templateparser.y"
    function yy_r47(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('start'=>$this->yystack[$this->yyidx + -4]->minor),array('to'=>$this->yystack[$this->yyidx + -2]->minor))),0);
    }
#line 2419 "smarty_internal_templateparser.php"
#line 405 "smarty_internal_templateparser.y"
    function yy_r48(){
    $this->_retvalue = $this->compiler->compileTag('for',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('start'=>$this->yystack[$this->yyidx + -6]->minor),array('to'=>$this->yystack[$this->yyidx + -4]->minor),array('step'=>$this->yystack[$this->yyidx + -2]->minor))),0);
    }
#line 2424 "smarty_internal_templateparser.php"
#line 410 "smarty_internal_templateparser.y"
    function yy_r49(){
    $this->_retvalue = $this->compiler->compileTag('foreach',$this->yystack[$this->yyidx + -1]->minor);
    }
#line 2429 "smarty_internal_templateparser.php"
#line 415 "smarty_internal_templateparser.y"
    function yy_r50(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('from'=>$this->yystack[$this->yyidx + -5]->minor),array('item'=>$this->yystack[$this->yyidx + -2]->minor))));
    }
#line 2434 "smarty_internal_templateparser.php"
#line 419 "smarty_internal_templateparser.y"
    function yy_r51(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('from'=>$this->yystack[$this->yyidx + -8]->minor),array('item'=>$this->yystack[$this->yyidx + -2]->minor),array('key'=>$this->yystack[$this->yyidx + -5]->minor))));
    }
#line 2439 "smarty_internal_templateparser.php"
#line 423 "smarty_internal_templateparser.y"
    function yy_r52(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('from'=>$this->yystack[$this->yyidx + -5]->minor),array('item'=>$this->yystack[$this->yyidx + -2]->minor))));
    }
#line 2444 "smarty_internal_templateparser.php"
#line 427 "smarty_internal_templateparser.y"
    function yy_r53(){
    $this->_retvalue = $this->compiler->compileTag('foreach',array_merge($this->yystack[$this->yyidx + -1]->minor,array(array('from'=>$this->yystack[$this->yyidx + -8]->minor),array('item'=>$this->yystack[$this->yyidx + -2]->minor),array('key'=>$this->yystack[$this->yyidx + -5]->minor))));
    }
#line 2449 "smarty_internal_templateparser.php"
#line 432 "smarty_internal_templateparser.y"
    function yy_r54(){
    $this->_retvalue = $this->compiler->compileTag('setfilter',array(),array('modifier_list'=>array(array_merge(array($this->yystack[$this->yyidx + -2]->minor),$this->yystack[$this->yyidx + -1]->minor))));
    }
#line 2454 "smarty_internal_templateparser.php"
#line 436 "smarty_internal_templateparser.y"
    function yy_r55(){
    $this->_retvalue = $this->compiler->compileTag('setfilter',array(),array('modifier_list'=>array_merge(array(array_merge(array($this->yystack[$this->yyidx + -3]->minor),$this->yystack[$this->yyidx + -2]->minor)),$this->yystack[$this->yyidx + -1]->minor)));
    }
#line 2459 "smarty_internal_templateparser.php"
#line 441 "smarty_internal_templateparser.y"
    function yy_r56(){
    $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileChildBlock($this->compiler);
    }
#line 2464 "smarty_internal_templateparser.php"
#line 447 "smarty_internal_templateparser.y"
    function yy_r57(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -1]->minor.'close',array());
    }
#line 2469 "smarty_internal_templateparser.php"
#line 451 "smarty_internal_templateparser.y"
    function yy_r58(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -2]->minor.'close',array(),array('modifier_list'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2474 "smarty_internal_templateparser.php"
#line 456 "smarty_internal_templateparser.y"
    function yy_r59(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -3]->minor.'close',array(),array('object_methode'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2479 "smarty_internal_templateparser.php"
#line 460 "smarty_internal_templateparser.y"
    function yy_r60(){
    $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + -4]->minor.'close',array(),array('object_methode'=>$this->yystack[$this->yyidx + -2]->minor, 'modifier_list'=>$this->yystack[$this->yyidx + -1]->minor));
    }
#line 2484 "smarty_internal_templateparser.php"
#line 468 "smarty_internal_templateparser.y"
    function yy_r61(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    $this->_retvalue[] = $this->yystack[$this->yyidx + 0]->minor;
    }
#line 2490 "smarty_internal_templateparser.php"
#line 474 "smarty_internal_templateparser.y"
    function yy_r62(){
    $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }
#line 2495 "smarty_internal_templateparser.php"
#line 479 "smarty_internal_templateparser.y"
    function yy_r63(){
    $this->_retvalue = array();
    }
#line 2500 "smarty_internal_templateparser.php"
#line 484 "smarty_internal_templateparser.y"
    function yy_r64(){
    if (preg_match('~^true$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>'true');
    } elseif (preg_match('~^false$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>'false');
    } elseif (preg_match('~^null$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>'null');
    } else {
        $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>"'".$this->yystack[$this->yyidx + 0]->minor."'");
    }
    }
#line 2513 "smarty_internal_templateparser.php"
#line 496 "smarty_internal_templateparser.y"
    function yy_r65(){
    $this->_retvalue = array($this->yystack[$this->yyidx + -2]->minor=>$this->yystack[$this->yyidx + 0]->minor);
    }
#line 2518 "smarty_internal_templateparser.php"
#line 504 "smarty_internal_templateparser.y"
    function yy_r67(){
    $this->_retvalue = "'".$this->yystack[$this->yyidx + 0]->minor."'";
    }
#line 2523 "smarty_internal_templateparser.php"
#line 529 "smarty_internal_templateparser.y"
    function yy_r72(){
    $this->yystack[$this->yyidx + -2]->minor[]=$this->yystack[$this->yyidx + 0]->minor;
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor;
    }
#line 2529 "smarty_internal_templateparser.php"
#line 534 "smarty_internal_templateparser.y"
    function yy_r73(){
    $this->_retvalue = array('var' => $this->yystack[$this->yyidx + -2]->minor, 'value'=>$this->yystack[$this->yyidx + 0]->minor);
    }
#line 2534 "smarty_internal_templateparser.php"
#line 562 "smarty_internal_templateparser.y"
    function yy_r78(){
    $this->_retvalue = '$_smarty_tpl->getStreamVariable(\''. $this->yystack[$this->yyidx + -2]->minor .'://'. $this->yystack[$this->yyidx + 0]->minor . '\')';
    }
#line 2539 "smarty_internal_templateparser.php"
#line 567 "smarty_internal_templateparser.y"
    function yy_r79(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor . trim($this->yystack[$this->yyidx + -1]->minor) . $this->yystack[$this->yyidx + 0]->minor;
    }
#line 2544 "smarty_internal_templateparser.php"
#line 586 "smarty_internal_templateparser.y"
    function yy_r83(){
    $this->_retvalue = $this->compiler->compileTag('private_modifier',array(),array('value'=>$this->yystack[$this->yyidx + -1]->minor,'modifierlist'=>$this->yystack[$this->yyidx + 0]->minor));
    }
#line 2549 "smarty_internal_templateparser.php"
#line 592 "smarty_internal_templateparser.y"
    function yy_r84(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2554 "smarty_internal_templateparser.php"
#line 596 "smarty_internal_templateparser.y"
    function yy_r85(){
    $this->_retvalue = 'in_array('.$this->yystack[$this->yyidx + -2]->minor.','.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2559 "smarty_internal_templateparser.php"
#line 600 "smarty_internal_templateparser.y"
    function yy_r86(){
    $this->_retvalue = 'in_array('.$this->yystack[$this->yyidx + -2]->minor.',(array)'.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2564 "smarty_internal_templateparser.php"
#line 608 "smarty_internal_templateparser.y"
    function yy_r88(){
    $this->_retvalue = '!('.$this->yystack[$this->yyidx + -2]->minor.' % '.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2569 "smarty_internal_templateparser.php"
#line 612 "smarty_internal_templateparser.y"
    function yy_r89(){
    $this->_retvalue = '('.$this->yystack[$this->yyidx + -2]->minor.' % '.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2574 "smarty_internal_templateparser.php"
#line 616 "smarty_internal_templateparser.y"
    function yy_r90(){
    $this->_retvalue = '!(1 & '.$this->yystack[$this->yyidx + -1]->minor.')';
    }
#line 2579 "smarty_internal_templateparser.php"
#line 620 "smarty_internal_templateparser.y"
    function yy_r91(){
    $this->_retvalue = '(1 & '.$this->yystack[$this->yyidx + -1]->minor.')';
    }
#line 2584 "smarty_internal_templateparser.php"
#line 624 "smarty_internal_templateparser.y"
    function yy_r92(){
    $this->_retvalue = '!(1 & '.$this->yystack[$this->yyidx + -2]->minor.' / '.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2589 "smarty_internal_templateparser.php"
#line 628 "smarty_internal_templateparser.y"
    function yy_r93(){
    $this->_retvalue = '(1 & '.$this->yystack[$this->yyidx + -2]->minor.' / '.$this->yystack[$this->yyidx + 0]->minor.')';
    }
#line 2594 "smarty_internal_templateparser.php"
#line 652 "smarty_internal_templateparser.y"
    function yy_r99(){
    $this->prefix_number++;
    $this->compiler->prefix_code[] = '<?php $_tmp'.$this->prefix_number.'='.$this->yystack[$this->yyidx + 0]->minor.';?>';
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor.'$_tmp'.$this->prefix_number;
    }
#line 2601 "smarty_internal_templateparser.php"
#line 661 "smarty_internal_templateparser.y"
    function yy_r100(){
    $this->_retvalue = $this->yystack[$this->yyidx + -6]->minor.' ? '. $this->compileVariable("'".$this->yystack[$this->yyidx + -2]->minor."'") . ' : '.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2606 "smarty_internal_templateparser.php"
#line 665 "smarty_internal_templateparser.y"
    function yy_r101(){
    $this->_retvalue = $this->yystack[$this->yyidx + -5]->minor.' ? '.$this->yystack[$this->yyidx + -2]->minor.' : '.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2611 "smarty_internal_templateparser.php"
#line 680 "smarty_internal_templateparser.y"
    function yy_r104(){
    $this->_retvalue = '!'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2616 "smarty_internal_templateparser.php"
#line 701 "smarty_internal_templateparser.y"
    function yy_r109(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'.'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2621 "smarty_internal_templateparser.php"
#line 705 "smarty_internal_templateparser.y"
    function yy_r110(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.'.';
    }
#line 2626 "smarty_internal_templateparser.php"
#line 709 "smarty_internal_templateparser.y"
    function yy_r111(){
    $this->_retvalue = '.'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2631 "smarty_internal_templateparser.php"
#line 714 "smarty_internal_templateparser.y"
    function yy_r112(){
    if (preg_match('~^true$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = 'true';
    } elseif (preg_match('~^false$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = 'false';
    } elseif (preg_match('~^null$~i', $this->yystack[$this->yyidx + 0]->minor)) {
        $this->_retvalue = 'null';
    } else {
        $this->_retvalue = "'".$this->yystack[$this->yyidx + 0]->minor."'";
    }
    }
#line 2644 "smarty_internal_templateparser.php"
#line 732 "smarty_internal_templateparser.y"
    function yy_r114(){
    $this->_retvalue = "(". $this->yystack[$this->yyidx + -1]->minor .")";
    }
#line 2649 "smarty_internal_templateparser.php"
#line 747 "smarty_internal_templateparser.y"
    function yy_r117(){
    if (!$this->security || isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + -2]->minor]) || $this->smarty->security_policy->isTrustedStaticClass($this->yystack[$this->yyidx + -2]->minor, $this->compiler)) {
        if (isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + -2]->minor])) {
            $this->_retvalue = $this->smarty->registered_classes[$this->yystack[$this->yyidx + -2]->minor].'::'.$this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'::'.$this->yystack[$this->yyidx + 0]->minor;
        }
    } else {
        $this->compiler->trigger_template_error ("static class '".$this->yystack[$this->yyidx + -2]->minor."' is undefined or not allowed by security setting");
    }
    }
#line 2662 "smarty_internal_templateparser.php"
#line 759 "smarty_internal_templateparser.y"
    function yy_r118(){
    if ($this->yystack[$this->yyidx + -2]->minor['var'] == '\'smarty\'') {
        $this->_retvalue =  $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + -2]->minor['smarty_internal_index']).'::'.$this->yystack[$this->yyidx + 0]->minor;
    } else {
        $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + -2]->minor['var']).$this->yystack[$this->yyidx + -2]->minor['smarty_internal_index'].'::'.$this->yystack[$this->yyidx + 0]->minor;
    }
    }
#line 2671 "smarty_internal_templateparser.php"
#line 768 "smarty_internal_templateparser.y"
    function yy_r119(){
    $this->prefix_number++;
    $this->compiler->prefix_code[] = '<?php ob_start();?>'.$this->yystack[$this->yyidx + 0]->minor.'<?php $_tmp'.$this->prefix_number.'=ob_get_clean();?>';
    $this->_retvalue = '$_tmp'.$this->prefix_number;
    }
#line 2678 "smarty_internal_templateparser.php"
#line 783 "smarty_internal_templateparser.y"
    function yy_r121(){
    if ($this->yystack[$this->yyidx + 0]->minor['var'] == '\'smarty\'') {
        $smarty_var = $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + 0]->minor['smarty_internal_index']);
        $this->_retvalue = $smarty_var;
    } else {
        // used for array reset,next,prev,end,current
        $this->last_variable = $this->yystack[$this->yyidx + 0]->minor['var'];
        $this->last_index = $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
        $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + 0]->minor['var']).$this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
    }
    }
#line 2691 "smarty_internal_templateparser.php"
#line 796 "smarty_internal_templateparser.y"
    function yy_r122(){
    $this->_retvalue = '$_smarty_tpl->tpl_vars['. $this->yystack[$this->yyidx + -2]->minor .']->'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2696 "smarty_internal_templateparser.php"
#line 806 "smarty_internal_templateparser.y"
    function yy_r124(){
    $this->_retvalue = '$_smarty_tpl->getConfigVariable(\''. $this->yystack[$this->yyidx + -1]->minor .'\')';
    }
#line 2701 "smarty_internal_templateparser.php"
#line 810 "smarty_internal_templateparser.y"
    function yy_r125(){
    $this->_retvalue = '$_smarty_tpl->getConfigVariable('. $this->yystack[$this->yyidx + -1]->minor .')';
    }
#line 2706 "smarty_internal_templateparser.php"
#line 814 "smarty_internal_templateparser.y"
    function yy_r126(){
    $this->_retvalue = array('var'=>$this->yystack[$this->yyidx + -1]->minor, 'smarty_internal_index'=>$this->yystack[$this->yyidx + 0]->minor);
    }
#line 2711 "smarty_internal_templateparser.php"
#line 827 "smarty_internal_templateparser.y"
    function yy_r128(){
    return;
    }
#line 2716 "smarty_internal_templateparser.php"
#line 833 "smarty_internal_templateparser.y"
    function yy_r129(){
    $this->_retvalue = '['.$this->compileVariable($this->yystack[$this->yyidx + 0]->minor).']';
    }
#line 2721 "smarty_internal_templateparser.php"
#line 837 "smarty_internal_templateparser.y"
    function yy_r130(){
    $this->_retvalue = '['.$this->compileVariable($this->yystack[$this->yyidx + -2]->minor).'->'.$this->yystack[$this->yyidx + 0]->minor.']';
    }
#line 2726 "smarty_internal_templateparser.php"
#line 841 "smarty_internal_templateparser.y"
    function yy_r131(){
    $this->_retvalue = "['". $this->yystack[$this->yyidx + 0]->minor ."']";
    }
#line 2731 "smarty_internal_templateparser.php"
#line 845 "smarty_internal_templateparser.y"
    function yy_r132(){
    $this->_retvalue = "[". $this->yystack[$this->yyidx + 0]->minor ."]";
    }
#line 2736 "smarty_internal_templateparser.php"
#line 849 "smarty_internal_templateparser.y"
    function yy_r133(){
    $this->_retvalue = "[". $this->yystack[$this->yyidx + -1]->minor ."]";
    }
#line 2741 "smarty_internal_templateparser.php"
#line 854 "smarty_internal_templateparser.y"
    function yy_r134(){
    $this->_retvalue = '['.$this->compiler->compileTag('private_special_variable',array(),'[\'section\'][\''.$this->yystack[$this->yyidx + -1]->minor.'\'][\'index\']').']';
    }
#line 2746 "smarty_internal_templateparser.php"
#line 858 "smarty_internal_templateparser.y"
    function yy_r135(){
    $this->_retvalue = '['.$this->compiler->compileTag('private_special_variable',array(),'[\'section\'][\''.$this->yystack[$this->yyidx + -3]->minor.'\'][\''.$this->yystack[$this->yyidx + -1]->minor.'\']').']';
    }
#line 2751 "smarty_internal_templateparser.php"
#line 868 "smarty_internal_templateparser.y"
    function yy_r137(){
    $this->_retvalue = '[]';
    }
#line 2756 "smarty_internal_templateparser.php"
#line 881 "smarty_internal_templateparser.y"
    function yy_r139(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor.'.'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2761 "smarty_internal_templateparser.php"
#line 886 "smarty_internal_templateparser.y"
    function yy_r140(){
    $this->_retvalue = '\''.$this->yystack[$this->yyidx + 0]->minor.'\'';
    }
#line 2766 "smarty_internal_templateparser.php"
#line 891 "smarty_internal_templateparser.y"
    function yy_r141(){
    $this->_retvalue = '('.$this->yystack[$this->yyidx + -1]->minor.')';
    }
#line 2771 "smarty_internal_templateparser.php"
#line 898 "smarty_internal_templateparser.y"
    function yy_r142(){
    if ($this->yystack[$this->yyidx + -1]->minor['var'] == '\'smarty\'') {
        $this->_retvalue =  $this->compiler->compileTag('private_special_variable',array(),$this->yystack[$this->yyidx + -1]->minor['smarty_internal_index']).$this->yystack[$this->yyidx + 0]->minor;
    } else {
        $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + -1]->minor['var']).$this->yystack[$this->yyidx + -1]->minor['smarty_internal_index'].$this->yystack[$this->yyidx + 0]->minor;
    }
    }
#line 2780 "smarty_internal_templateparser.php"
#line 907 "smarty_internal_templateparser.y"
    function yy_r143(){
    $this->_retvalue  = $this->yystack[$this->yyidx + 0]->minor;
    }
#line 2785 "smarty_internal_templateparser.php"
#line 912 "smarty_internal_templateparser.y"
    function yy_r144(){
    $this->_retvalue  = $this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2790 "smarty_internal_templateparser.php"
#line 917 "smarty_internal_templateparser.y"
    function yy_r145(){
    if ($this->security && substr($this->yystack[$this->yyidx + -1]->minor,0,1) == '_') {
        $this->compiler->trigger_template_error (self::Err1);
    }
    $this->_retvalue = '->'.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2798 "smarty_internal_templateparser.php"
#line 924 "smarty_internal_templateparser.y"
    function yy_r146(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::Err2);
    }
    $this->_retvalue = '->{'.$this->compileVariable($this->yystack[$this->yyidx + -1]->minor).$this->yystack[$this->yyidx + 0]->minor.'}';
    }
#line 2806 "smarty_internal_templateparser.php"
#line 931 "smarty_internal_templateparser.y"
    function yy_r147(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::Err2);
    }
    $this->_retvalue = '->{'.$this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + 0]->minor.'}';
    }
#line 2814 "smarty_internal_templateparser.php"
#line 938 "smarty_internal_templateparser.y"
    function yy_r148(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::Err2);
    }
    $this->_retvalue = '->{\''.$this->yystack[$this->yyidx + -4]->minor.'\'.'.$this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + 0]->minor.'}';
    }
#line 2822 "smarty_internal_templateparser.php"
#line 946 "smarty_internal_templateparser.y"
    function yy_r149(){
    $this->_retvalue = '->'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2827 "smarty_internal_templateparser.php"
#line 954 "smarty_internal_templateparser.y"
    function yy_r150(){
    if (!$this->security || $this->smarty->security_policy->isTrustedPhpFunction($this->yystack[$this->yyidx + -3]->minor, $this->compiler)) {
        if (strcasecmp($this->yystack[$this->yyidx + -3]->minor,'isset') === 0 || strcasecmp($this->yystack[$this->yyidx + -3]->minor,'empty') === 0 || strcasecmp($this->yystack[$this->yyidx + -3]->minor,'array') === 0 || is_callable($this->yystack[$this->yyidx + -3]->minor)) {
            $func_name = strtolower($this->yystack[$this->yyidx + -3]->minor);
            if ($func_name == 'isset') {
                if (count($this->yystack[$this->yyidx + -1]->minor) == 0) {
                    $this->compiler->trigger_template_error ('Illegal number of paramer in "isset()"');
                }
                $par = implode(',',$this->yystack[$this->yyidx + -1]->minor);
                if (strncasecmp($par,'$_smarty_tpl->getConfigVariable',strlen('$_smarty_tpl->getConfigVariable')) === 0) {
                    $this->prefix_number++;
                    $this->compiler->prefix_code[] = '<?php $_tmp'.$this->prefix_number.'='.str_replace(')',', false)',$par).';?>';
                    $isset_par = '$_tmp'.$this->prefix_number;
                } else {
                    $isset_par=str_replace("')->value","',null,true,false)->value",$par);
                }
                $this->_retvalue = $this->yystack[$this->yyidx + -3]->minor . "(". $isset_par .")";
            } elseif (in_array($func_name,array('empty','reset','current','end','prev','next'))){
                if (count($this->yystack[$this->yyidx + -1]->minor) != 1) {
                    $this->compiler->trigger_template_error ('Illegal number of paramer in "empty()"');
                }
                if ($func_name == 'empty') {
                    $this->_retvalue = $func_name.'('.str_replace("')->value","',null,true,false)->value",$this->yystack[$this->yyidx + -1]->minor[0]).')';
                } else {
                    $this->_retvalue = $func_name.'('.$this->yystack[$this->yyidx + -1]->minor[0].')';
                }
            } else {
                $this->_retvalue = $this->yystack[$this->yyidx + -3]->minor . "(". implode(',',$this->yystack[$this->yyidx + -1]->minor) .")";
            }
        } else {
            $this->compiler->trigger_template_error ("unknown function \"" . $this->yystack[$this->yyidx + -3]->minor . "\"");
        }
    }
    }
#line 2863 "smarty_internal_templateparser.php"
#line 992 "smarty_internal_templateparser.y"
    function yy_r151(){
    if ($this->security && substr($this->yystack[$this->yyidx + -3]->minor,0,1) == '_') {
        $this->compiler->trigger_template_error (self::Err1);
    }
    $this->_retvalue = $this->yystack[$this->yyidx + -3]->minor . "(". implode(',',$this->yystack[$this->yyidx + -1]->minor) .")";
    }
#line 2871 "smarty_internal_templateparser.php"
#line 999 "smarty_internal_templateparser.y"
    function yy_r152(){
    if ($this->security) {
        $this->compiler->trigger_template_error (self::Err2);
    }
    $this->prefix_number++;
    $this->compiler->prefix_code[] = '<?php $_tmp'.$this->prefix_number.'='.$this->compileVariable("'".$this->yystack[$this->yyidx + -3]->minor."'").';?>';
    $this->_retvalue = '$_tmp'.$this->prefix_number.'('. implode(',',$this->yystack[$this->yyidx + -1]->minor) .')';
    }
#line 2881 "smarty_internal_templateparser.php"
#line 1010 "smarty_internal_templateparser.y"
    function yy_r153(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -2]->minor,array($this->yystack[$this->yyidx + 0]->minor));
    }
#line 2886 "smarty_internal_templateparser.php"
#line 1027 "smarty_internal_templateparser.y"
    function yy_r156(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -2]->minor,array(array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor)));
    }
#line 2891 "smarty_internal_templateparser.php"
#line 1031 "smarty_internal_templateparser.y"
    function yy_r157(){
    $this->_retvalue = array(array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor));
    }
#line 2896 "smarty_internal_templateparser.php"
#line 1039 "smarty_internal_templateparser.y"
    function yy_r159(){
    $this->_retvalue =  array($this->yystack[$this->yyidx + 0]->minor);
    }
#line 2901 "smarty_internal_templateparser.php"
#line 1047 "smarty_internal_templateparser.y"
    function yy_r160(){
    $this->_retvalue = array_merge($this->yystack[$this->yyidx + -1]->minor,$this->yystack[$this->yyidx + 0]->minor);
    }
#line 2906 "smarty_internal_templateparser.php"
#line 1081 "smarty_internal_templateparser.y"
    function yy_r167(){
    $this->_retvalue = '$'.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2911 "smarty_internal_templateparser.php"
#line 1086 "smarty_internal_templateparser.y"
    function yy_r168(){
    $this->_retvalue = '$'.$this->yystack[$this->yyidx + -2]->minor.$this->yystack[$this->yyidx + -1]->minor.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2916 "smarty_internal_templateparser.php"
#line 1092 "smarty_internal_templateparser.y"
    function yy_r169(){
    $this->_retvalue = '==';
    }
#line 2921 "smarty_internal_templateparser.php"
#line 1096 "smarty_internal_templateparser.y"
    function yy_r170(){
    $this->_retvalue = '!=';
    }
#line 2926 "smarty_internal_templateparser.php"
#line 1100 "smarty_internal_templateparser.y"
    function yy_r171(){
    $this->_retvalue = '>';
    }
#line 2931 "smarty_internal_templateparser.php"
#line 1104 "smarty_internal_templateparser.y"
    function yy_r172(){
    $this->_retvalue = '<';
    }
#line 2936 "smarty_internal_templateparser.php"
#line 1108 "smarty_internal_templateparser.y"
    function yy_r173(){
    $this->_retvalue = '>=';
    }
#line 2941 "smarty_internal_templateparser.php"
#line 1112 "smarty_internal_templateparser.y"
    function yy_r174(){
    $this->_retvalue = '<=';
    }
#line 2946 "smarty_internal_templateparser.php"
#line 1116 "smarty_internal_templateparser.y"
    function yy_r175(){
    $this->_retvalue = '===';
    }
#line 2951 "smarty_internal_templateparser.php"
#line 1120 "smarty_internal_templateparser.y"
    function yy_r176(){
    $this->_retvalue = '!==';
    }
#line 2956 "smarty_internal_templateparser.php"
#line 1124 "smarty_internal_templateparser.y"
    function yy_r177(){
    $this->_retvalue = '%';
    }
#line 2961 "smarty_internal_templateparser.php"
#line 1128 "smarty_internal_templateparser.y"
    function yy_r178(){
    $this->_retvalue = '&&';
    }
#line 2966 "smarty_internal_templateparser.php"
#line 1132 "smarty_internal_templateparser.y"
    function yy_r179(){
    $this->_retvalue = '||';
    }
#line 2971 "smarty_internal_templateparser.php"
#line 1136 "smarty_internal_templateparser.y"
    function yy_r180(){
    $this->_retvalue = ' XOR ';
    }
#line 2976 "smarty_internal_templateparser.php"
#line 1143 "smarty_internal_templateparser.y"
    function yy_r181(){
    $this->_retvalue = 'array('.$this->yystack[$this->yyidx + -1]->minor.')';
    }
#line 2981 "smarty_internal_templateparser.php"
#line 1151 "smarty_internal_templateparser.y"
    function yy_r183(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.','.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2986 "smarty_internal_templateparser.php"
#line 1159 "smarty_internal_templateparser.y"
    function yy_r185(){
    $this->_retvalue = $this->yystack[$this->yyidx + -2]->minor.'=>'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2991 "smarty_internal_templateparser.php"
#line 1163 "smarty_internal_templateparser.y"
    function yy_r186(){
    $this->_retvalue = '\''.$this->yystack[$this->yyidx + -2]->minor.'\'=>'.$this->yystack[$this->yyidx + 0]->minor;
    }
#line 2996 "smarty_internal_templateparser.php"
#line 1175 "smarty_internal_templateparser.y"
    function yy_r188(){
    $this->_retvalue = "''";
    }
#line 3001 "smarty_internal_templateparser.php"
#line 1179 "smarty_internal_templateparser.y"
    function yy_r189(){
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor->to_smarty_php();
    }
#line 3006 "smarty_internal_templateparser.php"
#line 1184 "smarty_internal_templateparser.y"
    function yy_r190(){
    $this->yystack[$this->yyidx + -1]->minor->append_subtree($this->yystack[$this->yyidx + 0]->minor);
    $this->_retvalue = $this->yystack[$this->yyidx + -1]->minor;
    }
#line 3012 "smarty_internal_templateparser.php"
#line 1189 "smarty_internal_templateparser.y"
    function yy_r191(){
    $this->_retvalue = new _smarty_doublequoted($this, $this->yystack[$this->yyidx + 0]->minor);
    }
#line 3017 "smarty_internal_templateparser.php"
#line 1193 "smarty_internal_templateparser.y"
    function yy_r192(){
    $this->_retvalue = new _smarty_code($this, $this->yystack[$this->yyidx + -1]->minor);
    }
#line 3022 "smarty_internal_templateparser.php"
#line 1201 "smarty_internal_templateparser.y"
    function yy_r194(){
    $this->_retvalue = new _smarty_code($this, '$_smarty_tpl->tpl_vars[\''. substr($this->yystack[$this->yyidx + 0]->minor,1) .'\']->value');
    }
#line 3027 "smarty_internal_templateparser.php"
#line 1209 "smarty_internal_templateparser.y"
    function yy_r196(){
    $this->_retvalue = new _smarty_code($this, '('.$this->yystack[$this->yyidx + -1]->minor.')');
    }
#line 3032 "smarty_internal_templateparser.php"
#line 1213 "smarty_internal_templateparser.y"
    function yy_r197(){
    $this->_retvalue = new _smarty_tag($this, $this->yystack[$this->yyidx + 0]->minor);
    }
#line 3037 "smarty_internal_templateparser.php"
#line 1217 "smarty_internal_templateparser.y"
    function yy_r198(){
    $this->_retvalue = new _smarty_dq_content($this, $this->yystack[$this->yyidx + 0]->minor);
    }
#line 3042 "smarty_internal_templateparser.php"

    private $_retvalue;

    function yy_reduce($yyruleno)
    {
        $yymsp = $this->yystack[$this->yyidx];
        if (self::$yyTraceFILE && $yyruleno >= 0
              && $yyruleno < count(self::$yyRuleName)) {
            fprintf(self::$yyTraceFILE, "%sReduce (%d) [%s].\n",
                self::$yyTracePrompt, $yyruleno,
                self::$yyRuleName[$yyruleno]);
        }

        $this->_retvalue = $yy_lefthand_side = null;
        if (array_key_exists($yyruleno, self::$yyReduceMap)) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[$yyruleno]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[$yyruleno]['lhs'];
        $yysize = self::$yyRuleInfo[$yyruleno]['rhs'];
        $this->yyidx -= $yysize;
        for($i = $yysize; $i; $i--) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!self::$yyTraceFILE && $yysize) {
                $this->yyidx++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[$this->yyidx] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact == self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    function yy_parse_failed()
    {
        if (self::$yyTraceFILE) {
            fprintf(self::$yyTraceFILE, "%sFail!\n", self::$yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    function yy_syntax_error($yymajor, $TOKEN)
    {
#line 76 "smarty_internal_templateparser.y"

    $this->internalError = true;
    $this->yymajor = $yymajor;
    $this->compiler->trigger_template_error();
#line 3105 "smarty_internal_templateparser.php"
    }

    function yy_accept()
    {
        if (self::$yyTraceFILE) {
            fprintf(self::$yyTraceFILE, "%sAccept!\n", self::$yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $stack = $this->yy_pop_parser_stack();
        }
#line 68 "smarty_internal_templateparser.y"

    $this->successful = !$this->internalError;
    $this->internalError = false;
    $this->retvalue = $this->_retvalue;
    //echo $this->retvalue."\n\n";
#line 3123 "smarty_internal_templateparser.php"
    }

    function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */

        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = -1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            array_push($this->yystack, $x);
        }
        $yyendofinput = ($yymajor==0);

        if (self::$yyTraceFILE) {
            fprintf(self::$yyTraceFILE, "%sInput %s\n",
                self::$yyTracePrompt, $this->yyTokenName[$yymajor]);
        }

        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL &&
                  !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt--;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact == self::YY_ERROR_ACTION) {
                if (self::$yyTraceFILE) {
                    fprintf(self::$yyTraceFILE, "%sSyntax Error!\n",
                        self::$yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[$this->yyidx]->major;
                    if ($yymx == self::YYERRORSYMBOL || $yyerrorhit ){
                        if (self::$yyTraceFILE) {
                            fprintf(self::$yyTraceFILE, "%sDiscard input token %s\n",
                                self::$yyTracePrompt, $this->yyTokenName[$yymajor]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 &&
                                 $yymx != self::YYERRORSYMBOL &&
        ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE
                              ){
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor==0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx != self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor != self::YYNOCODE && $this->yyidx >= 0);
    }
}
?>