<?php
/**
 * Smarty plugin
 * @package Smarty
 * @subpackage PluginsModifier
 */

/**
 * Smarty replace modifier plugin
 * 
 * Type:     modifier<br>
 * Name:     replace<br>
 * Purpose:  simple search/replace
 * 
 * @link http://smarty.php.net/manual/en/language.modifier.replace.php replace (Smarty online manual)
 * <AUTHOR> <monte at ohrt dot com> 
 * <AUTHOR> Tews 
 * @param string $string  input string
 * @param string $search  text to search for
 * @param string $replace replacement text
 * @return string 
 */
function smarty_modifier_replace($string, $search, $replace)
{
    if (SMARTY_MBSTRING /* ^phpunit */&&empty($_SERVER['SMARTY_PHPUNIT_DISABLE_MBSTRING'])/* phpunit$ */) {
        require_once(SMARTY_PLUGINS_DIR . 'shared.mb_str_replace.php');
        return smarty_mb_str_replace($search, $replace, $string);
    }
    
    return str_replace($search, $replace, $string);
} 

?>